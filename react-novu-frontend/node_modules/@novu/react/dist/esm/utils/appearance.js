// src/utils/appearance.ts
function adaptAppearanceForJs(appearance, mountElement) {
  if (!appearance) {
    return void 0;
  }
  const jsAppearance = JSON.parse(JSON.stringify(appearance));
  if (appearance.icons) {
    const jsIcons = {};
    const reactIcons = appearance.icons;
    const iconKeys = Object.keys(reactIcons);
    for (const iconKey of iconKeys) {
      const reactRenderer = reactIcons[iconKey];
      if (reactRenderer) {
        jsIcons[iconKey] = (el, props) => {
          return mountElement(el, reactRenderer(props));
        };
      }
    }
    jsAppearance.icons = jsIcons;
  } else {
    delete jsAppearance.icons;
  }
  return jsAppearance;
}
export {
  adaptAppearanceForJs
};
//# sourceMappingURL=appearance.js.map