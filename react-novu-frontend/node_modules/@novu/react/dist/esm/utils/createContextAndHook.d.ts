import React__default from 'react';

declare function assertContextExists(contextVal: unknown, msgOrCtx: string | React__default.Context<any>): asserts contextVal;
type Options = {
    assertCtxFn?: (v: unknown, msg: string) => void;
};
type ContextOf<T> = React__default.Context<{
    value: T;
} | undefined>;
type UseCtxFn<T> = () => T;
/**
 * Creates and returns a Context and two hooks that return the context value.
 * The Context type is derived from the type passed in by the user.
 * The first hook returned guarantees that the context exists so the returned value is always CtxValue
 * The second hook makes no guarantees, so the returned value can be CtxValue | undefined
 */
declare const createContextAndHook: <CtxVal>(displayName: string, options?: Options) => [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>];

export { assertContextExists, createContextAndHook };
