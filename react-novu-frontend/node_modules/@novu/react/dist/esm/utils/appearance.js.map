{"version": 3, "sources": ["../../../src/utils/appearance.ts"], "sourcesContent": ["import type { Appearance as J<PERSON><PERSON><PERSON>aran<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IconOverrides as JsIconO<PERSON><PERSON>s, IconKey } from '@novu/js/ui';\n\nimport type { ReactAppearance, ReactIconRenderer } from './types';\nimport { MountedElement } from '../context/RendererContext';\n\nexport function adaptAppearanceForJs(\n  appearance: ReactAppearance,\n  mountElement: (el: HTMLElement, mountedElement: MountedElement) => () => void\n): JsAppearance | undefined {\n  if (!appearance) {\n    return undefined;\n  }\n\n  const jsAppearance: JsAppearance = JSON.parse(JSON.stringify(appearance));\n\n  if (appearance.icons) {\n    const jsIcons: JsIconOverrides = {};\n    const reactIcons = appearance.icons;\n    const iconKeys = Object.keys(reactIcons) as IconKey[];\n\n    for (const iconKey of iconKeys) {\n      const reactRenderer = reactIcons[iconKey];\n\n      if (reactRenderer) {\n        jsIcons[iconKey] = (el: HTMLDivElement, props: { class?: string }) => {\n          return mountElement(el, reactRenderer(props));\n        };\n      }\n    }\n\n    // JsAppearance also has .icons directly (from JsTheme part of JsAppearance)\n    jsAppearance.icons = jsIcons;\n  } else {\n    // If original didn't have icons, ensure the clone doesn't either\n    delete jsAppearance.icons;\n  }\n\n  return jsAppearance;\n}\n"], "mappings": ";AAKO,SAAS,qBACd,YACA,cAC0B;AAC1B,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,QAAM,eAA6B,KAAK,MAAM,KAAK,UAAU,UAAU,CAAC;AAExE,MAAI,WAAW,OAAO;AACpB,UAAM,UAA2B,CAAC;AAClC,UAAM,aAAa,WAAW;AAC9B,UAAM,WAAW,OAAO,KAAK,UAAU;AAEvC,eAAW,WAAW,UAAU;AAC9B,YAAM,gBAAgB,WAAW,OAAO;AAExC,UAAI,eAAe;AACjB,gBAAQ,OAAO,IAAI,CAAC,IAAoB,UAA8B;AACpE,iBAAO,aAAa,IAAI,cAAc,KAAK,CAAC;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAGA,iBAAa,QAAQ;AAAA,EACvB,OAAO;AAEL,WAAO,aAAa;AAAA,EACtB;AAEA,SAAO;AACT;", "names": []}