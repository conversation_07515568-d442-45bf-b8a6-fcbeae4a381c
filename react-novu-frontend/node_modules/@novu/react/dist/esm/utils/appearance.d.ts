import { Appearance } from '@novu/js/ui';
import { ReactAppearance } from './types.js';
import { MountedElement } from '../context/RendererContext.js';
import '@novu/js';
import 'react';
import 'react/jsx-runtime';

declare function adaptAppearanceForJs(appearance: ReactAppearance, mountElement: (el: HTMLElement, mountedElement: MountedElement) => () => void): Appearance | undefined;

export { adaptAppearanceForJs };
