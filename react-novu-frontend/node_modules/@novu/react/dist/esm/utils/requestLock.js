// src/utils/requestLock.ts
function requestLock(id, cb) {
  if (!("locks" in navigator)) {
    cb(id);
    return () => {
    };
  }
  let isFulfilled = false;
  let promiseResolve;
  const promise = new Promise((resolve) => {
    promiseResolve = resolve;
  });
  navigator.locks.request(id, () => {
    if (!isFulfilled) {
      cb(id);
    }
    return promise;
  });
  return () => {
    isFulfilled = true;
    promiseResolve();
  };
}
export {
  requestLock
};
//# sourceMappingURL=requestLock.js.map