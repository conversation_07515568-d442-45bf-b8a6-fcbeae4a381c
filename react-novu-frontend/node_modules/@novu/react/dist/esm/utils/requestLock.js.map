{"version": 3, "sources": ["../../../src/utils/requestLock.ts"], "sourcesContent": ["export function requestLock(id: string, cb: (id: string) => void) {\n  // Check if the Lock API is available\n  if (!('locks' in navigator)) {\n    // If Lock API is not available, immediately invoke the callback and return a no-op function\n    cb(id);\n\n    return () => {};\n  }\n\n  let isFulfilled = false;\n  let promiseResolve: () => void;\n\n  const promise = new Promise<void>((resolve) => {\n    promiseResolve = resolve;\n  });\n\n  navigator.locks.request(id, () => {\n    if (!isFulfilled) {\n      cb(id);\n    }\n\n    return promise;\n  });\n\n  return () => {\n    isFulfilled = true;\n    promiseResolve();\n  };\n}\n"], "mappings": ";AAAO,SAAS,YAAY,IAAY,IAA0B;AAEhE,MAAI,EAAE,WAAW,YAAY;AAE3B,OAAG,EAAE;AAEL,WAAO,MAAM;AAAA,IAAC;AAAA,EAChB;AAEA,MAAI,cAAc;AAClB,MAAI;AAEJ,QAAM,UAAU,IAAI,QAAc,CAAC,YAAY;AAC7C,qBAAiB;AAAA,EACnB,CAAC;AAED,YAAU,MAAM,QAAQ,IAAI,MAAM;AAChC,QAAI,CAAC,aAAa;AAChB,SAAG,EAAE;AAAA,IACP;AAEA,WAAO;AAAA,EACT,CAAC;AAED,SAAO,MAAM;AACX,kBAAc;AACd,mBAAe;AAAA,EACjB;AACF;", "names": []}