{"version": 3, "sources": ["../../../src/server/index.tsx"], "sourcesContent": ["import type {\n  UseNotificationsProps,\n  UseNotificationsResult,\n  UsePreferencesProps,\n  UsePreferencesResult,\n} from '../hooks';\nimport type { UseCountsProps, UseCountsResult } from '../hooks/useCounts';\nimport type { InboxProps } from '../components/Inbox';\nimport type { NovuProviderProps } from '../hooks/NovuProvider';\nimport { ShadowRootDetector } from '../components/ShadowRootDetector';\n\n/**\n * Exporting all components from the components folder\n * as empty functions to fix build errors in SSR\n * This will be replaced with actual components\n * when we implement the SSR components in @novu/js/ui\n */\nexport function Inbox(props: InboxProps) {\n  return <ShadowRootDetector />;\n}\n\nexport function InboxContent() {}\n\nexport function Notifications() {}\n\nexport function Preferences() {}\n\nexport function Bell() {}\n\nexport function NovuProvider(props: NovuProviderProps) {}\n\nexport function useNovu() {\n  return null;\n}\n\nexport function useCounts(_: UseCountsProps): UseCountsResult {\n  return {\n    isLoading: false,\n    isFetching: false,\n    refetch: () => Promise.resolve(),\n  };\n}\n\nexport function useNotifications(_: UseNotificationsProps): UseNotificationsResult {\n  return {\n    isLoading: false,\n    isFetching: false,\n    hasMore: false,\n    readAll: () => Promise.resolve({ data: undefined, error: undefined }),\n    archiveAll: () => Promise.resolve({ data: undefined, error: undefined }),\n    archiveAllRead: () => Promise.resolve({ data: undefined, error: undefined }),\n    refetch: () => Promise.resolve(),\n    fetchMore: () => Promise.resolve(),\n  };\n}\n\nexport function usePreferences(_: UsePreferencesProps): UsePreferencesResult {\n  return {\n    isLoading: false,\n    isFetching: false,\n    refetch: () => Promise.resolve(),\n  };\n}\n\nexport type {\n  FiltersCountResponse,\n  ListNotificationsResponse,\n  NovuError,\n  Preference,\n  ChannelPreference,\n  Notification,\n  ChannelType,\n  InboxNotification,\n  NotificationFilter,\n  NotificationStatus,\n  NovuOptions,\n  PreferenceLevel,\n} from '@novu/js';\n\nexport type {\n  Appearance,\n  AppearanceKey,\n  Elements,\n  ElementStyles,\n  Localization,\n  LocalizationKey,\n  NotificationActionClickHandler,\n  NotificationClickHandler,\n  NotificationRenderer,\n  PreferencesFilter,\n  PreferenceGroups,\n  RouterPush,\n  Tab,\n  Variables,\n} from '@novu/js/ui';\n\nexport type { InboxProps, BellProps, InboxContentProps, NotificationProps, NovuProviderProps } from '../components';\n\nexport type {\n  UseCountsProps,\n  UseCountsResult,\n  UseNotificationsProps,\n  UseNotificationsResult,\n  UsePreferencesProps,\n  UsePreferencesResult,\n} from '../hooks';\n\nexport type {\n  NotificationsRenderer,\n  SubjectRenderer,\n  BodyRenderer,\n  BellRenderer,\n  DefaultInboxProps,\n  BaseProps,\n  NotificationRendererProps,\n  SubjectBodyRendererProps,\n  NoRendererProps,\n  DefaultProps,\n  WithChildrenProps,\n} from '../utils/types';\n"], "mappings": ";AASA,SAAS,0BAA0B;AAS1B;AADF,SAAS,MAAM,OAAmB;AACvC,SAAO,oBAAC,sBAAmB;AAC7B;AAEO,SAAS,eAAe;AAAC;AAEzB,SAAS,gBAAgB;AAAC;AAE1B,SAAS,cAAc;AAAC;AAExB,SAAS,OAAO;AAAC;AAEjB,SAAS,aAAa,OAA0B;AAAC;AAEjD,SAAS,UAAU;AACxB,SAAO;AACT;AAEO,SAAS,UAAU,GAAoC;AAC5D,SAAO;AAAA,IACL,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS,MAAM,QAAQ,QAAQ;AAAA,EACjC;AACF;AAEO,SAAS,iBAAiB,GAAkD;AACjF,SAAO;AAAA,IACL,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS,MAAM,QAAQ,QAAQ,EAAE,MAAM,QAAW,OAAO,OAAU,CAAC;AAAA,IACpE,YAAY,MAAM,QAAQ,QAAQ,EAAE,MAAM,QAAW,OAAO,OAAU,CAAC;AAAA,IACvE,gBAAgB,MAAM,QAAQ,QAAQ,EAAE,MAAM,QAAW,OAAO,OAAU,CAAC;AAAA,IAC3E,SAAS,MAAM,QAAQ,QAAQ;AAAA,IAC/B,WAAW,MAAM,QAAQ,QAAQ;AAAA,EACnC;AACF;AAEO,SAAS,eAAe,GAA8C;AAC3E,SAAO;AAAA,IACL,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS,MAAM,QAAQ,QAAQ;AAAA,EACjC;AACF;", "names": []}