// src/server/index.tsx
import { ShadowRootDetector } from "../components/ShadowRootDetector.js";
import { jsx } from "react/jsx-runtime";
function Inbox(props) {
  return /* @__PURE__ */ jsx(ShadowRootDetector, {});
}
function InboxContent() {
}
function Notifications() {
}
function Preferences() {
}
function Bell() {
}
function NovuProvider(props) {
}
function useNovu() {
  return null;
}
function useCounts(_) {
  return {
    isLoading: false,
    isFetching: false,
    refetch: () => Promise.resolve()
  };
}
function useNotifications(_) {
  return {
    isLoading: false,
    isFetching: false,
    hasMore: false,
    readAll: () => Promise.resolve({ data: void 0, error: void 0 }),
    archiveAll: () => Promise.resolve({ data: void 0, error: void 0 }),
    archiveAllRead: () => Promise.resolve({ data: void 0, error: void 0 }),
    refetch: () => Promise.resolve(),
    fetchMore: () => Promise.resolve()
  };
}
function usePreferences(_) {
  return {
    isLoading: false,
    isFetching: false,
    refetch: () => Promise.resolve()
  };
}
export {
  Bell,
  Inbox,
  InboxContent,
  Notifications,
  NovuProvider,
  Preferences,
  useCounts,
  useNotifications,
  useNovu,
  usePreferences
};
//# sourceMappingURL=index.js.map