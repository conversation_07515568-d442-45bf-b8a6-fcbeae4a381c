import * as react_jsx_runtime from 'react/jsx-runtime';
import { UseNotificationsProps, UseNotificationsResult } from '../hooks/useNotifications.js';
import { UsePreferencesProps, UsePreferencesResult } from '../hooks/usePreferences.js';
import { UseCountsProps, UseCountsResult } from '../hooks/useCounts.js';
import { NovuProviderProps } from '../hooks/NovuProvider.js';
import { InboxProps } from '../components/Inbox.js';
export { ChannelPreference, ChannelType, FiltersCountResponse, InboxNotification, ListNotificationsResponse, Notification, NotificationFilter, NotificationStatus, NovuError, NovuOptions, Preference, PreferenceLevel } from '@novu/js';
export { Appearance, AppearanceKey, ElementStyles, Elements, Localization, LocalizationKey, NotificationActionClickHandler, NotificationClickHandler, NotificationRenderer, PreferenceGroups, PreferencesFilter, RouterPush, Tab, Variables } from '@novu/js/ui';
export { BellProps } from '../components/Bell.js';
export { NotificationProps } from '../components/Notifications.js';
export { InboxContentProps } from '../components/InboxContent.js';
export { BaseProps, BellRenderer, BodyRenderer, DefaultInboxProps, DefaultProps, NoRendererProps, NotificationRendererProps, NotificationsRenderer, SubjectBodyRendererProps, SubjectRenderer, WithChildrenProps } from '../utils/types.js';
import 'react';

/**
 * Exporting all components from the components folder
 * as empty functions to fix build errors in SSR
 * This will be replaced with actual components
 * when we implement the SSR components in @novu/js/ui
 */
declare function Inbox(props: InboxProps): react_jsx_runtime.JSX.Element;
declare function InboxContent(): void;
declare function Notifications(): void;
declare function Preferences(): void;
declare function Bell(): void;
declare function NovuProvider(props: NovuProviderProps): void;
declare function useNovu(): null;
declare function useCounts(_: UseCountsProps): UseCountsResult;
declare function useNotifications(_: UseNotificationsProps): UseNotificationsResult;
declare function usePreferences(_: UsePreferencesProps): UsePreferencesResult;

export { Bell, Inbox, InboxContent, InboxProps, Notifications, NovuProvider, NovuProviderProps, Preferences, UseCountsProps, UseCountsResult, UseNotificationsProps, UseNotificationsResult, UsePreferencesProps, UsePreferencesResult, useCounts, useNotifications, useNovu, usePreferences };
