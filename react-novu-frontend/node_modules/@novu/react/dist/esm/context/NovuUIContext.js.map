{"version": 3, "sources": ["../../../src/context/NovuUIContext.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Novu<PERSON> } from '@novu/js/ui';\nimport { createContextAndHook } from '../utils/createContextAndHook';\n\ntype NovuUIContextValue = {\n  novuUI: NovuUI;\n};\n\nconst [NovuUIContext, useNovuUIContext, useUnsafeNovuUIContext] =\n  createContextAndHook<NovuUIContextValue>('NovuUIContext');\n\nconst NovuUIProvider = (props: React.PropsWithChildren<{ value: NovuUIContextValue }>) => {\n  return <NovuUIContext.Provider value={{ value: props.value }}>{props.children}</NovuUIContext.Provider>;\n};\n\nexport { useNovuUIContext as useNovuUI, useUnsafeNovuUIContext as useUnsafeNovuUI, NovuUIProvider };\n"], "mappings": ";AAEA,SAAS,4BAA4B;AAU5B;AAJT,IAAM,CAAC,eAAe,kBAAkB,sBAAsB,IAC5D,qBAAyC,eAAe;AAE1D,IAAM,iBAAiB,CAAC,UAAkE;AACxF,SAAO,oBAAC,cAAc,UAAd,EAAuB,OAAO,EAAE,OAAO,MAAM,MAAM,GAAI,gBAAM,UAAS;AAChF;", "names": []}