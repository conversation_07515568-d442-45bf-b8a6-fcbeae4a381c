{"version": 3, "sources": ["../../../src/context/RendererContext.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextAndHook } from '../utils/createContextAndHook';\n\nexport type MountedElement = React.ReactNode;\nexport type MountedElements = Map<HTMLElement, MountedElement>;\n\ntype RendererContextValue = {\n  mountElement: (el: HTMLElement, mountedElement: MountedElement) => () => void;\n};\n\nconst [RendererContext, useRendererContext, useUnsafeRendererContext] =\n  createContextAndHook<RendererContextValue>('RendererContext');\n\nconst RendererProvider = (props: React.PropsWithChildren<{ value: RendererContextValue }>) => {\n  return <RendererContext.Provider value={{ value: props.value }}>{props.children}</RendererContext.Provider>;\n};\n\nexport { useRendererContext as useRenderer, useUnsafeRendererContext as useUnsafe<PERSON><PERSON><PERSON>, RendererProvider };\n"], "mappings": ";AACA,SAAS,4BAA4B;AAa5B;AAJT,IAAM,CAAC,iBAAiB,oBAAoB,wBAAwB,IAClE,qBAA2C,iBAAiB;AAE9D,IAAM,mBAAmB,CAAC,UAAoE;AAC5F,SAAO,oBAAC,gBAAgB,UAAhB,EAAyB,OAAO,EAAE,OAAO,MAAM,MAAM,GAAI,gBAAM,UAAS;AAClF;", "names": []}