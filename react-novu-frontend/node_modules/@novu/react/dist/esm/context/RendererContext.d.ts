import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default from 'react';

type MountedElement = React__default.ReactNode;
type MountedElements = Map<HTMLElement, MountedElement>;
type RendererContextValue = {
    mountElement: (el: HTMLElement, mountedElement: MountedElement) => () => void;
};
declare const useRendererContext: () => RendererContextValue;
declare const useUnsafeRendererContext: () => RendererContextValue | Partial<RendererContextValue>;
declare const RendererProvider: (props: React__default.PropsWithChildren<{
    value: RendererContextValue;
}>) => react_jsx_runtime.JSX.Element;

export { type MountedElement, type MountedElements, RendererProvider, useRenderer<PERSON>ontext as useRenderer, useUnsafeRendererContext as useUnsafeRenderer };
