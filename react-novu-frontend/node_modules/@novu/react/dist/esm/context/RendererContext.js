// src/context/RendererContext.tsx
import { createContextAndHook } from "../utils/createContextAndHook.js";
import { jsx } from "react/jsx-runtime";
var [RendererContext, useRendererContext, useUnsafeRendererContext] = createContextAndHook("RendererContext");
var RendererProvider = (props) => {
  return /* @__PURE__ */ jsx(RendererContext.Provider, { value: { value: props.value }, children: props.children });
};
export {
  RendererProvider,
  useRendererContext as useRenderer,
  useUnsafeRendererContext as useUnsafeRenderer
};
//# sourceMappingURL=RendererContext.js.map