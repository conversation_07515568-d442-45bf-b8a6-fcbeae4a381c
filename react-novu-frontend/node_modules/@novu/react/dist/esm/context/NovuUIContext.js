// src/context/NovuUIContext.tsx
import { createContextAndHook } from "../utils/createContextAndHook.js";
import { jsx } from "react/jsx-runtime";
var [NovuUIContext, useNovuUIContext, useUnsafeNovuUIContext] = createContextAndHook("NovuUIContext");
var NovuUIProvider = (props) => {
  return /* @__PURE__ */ jsx(NovuUIContext.Provider, { value: { value: props.value }, children: props.children });
};
export {
  NovuUIProvider,
  useNovuUIContext as useNovuUI,
  useUnsafeNovuUIContext as useUnsafeNovuUI
};
//# sourceMappingURL=NovuUIContext.js.map