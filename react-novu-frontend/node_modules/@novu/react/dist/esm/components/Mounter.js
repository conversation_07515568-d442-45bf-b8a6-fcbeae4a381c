// src/components/Mounter.tsx
import { useEffect, useRef } from "react";
import { jsx } from "react/jsx-runtime";
function Mounter({ mount }) {
  const ref = useRef(null);
  useEffect(() => {
    let unmount;
    const element = ref.current;
    if (element && mount) {
      const possibleUnmount = mount(element);
      if (possibleUnmount) {
        unmount = possibleUnmount;
      }
    }
    return () => {
      if (element && unmount) {
        unmount(element);
      }
    };
  }, [ref, mount]);
  return /* @__PURE__ */ jsx("div", { ref });
}
export {
  Mounter
};
//# sourceMappingURL=Mounter.js.map