// src/components/Bell.tsx
import React from "react";
import { Mounter } from "./Mounter.js";
import { with<PERSON><PERSON><PERSON> } from "./Renderer.js";
import { useNovuUI } from "../context/NovuUIContext.js";
import { useRenderer } from "../context/RendererContext.js";
import { jsx } from "react/jsx-runtime";
var _Bell = React.memo((props) => {
  const { renderBell } = props;
  const { novuUI } = useNovuUI();
  const { mountElement } = useRenderer();
  const mount = React.useCallback(
    (element) => {
      return novuUI.mountComponent({
        name: "Bell",
        element,
        props: renderBell ? { renderBell: (el, unreadCount) => mountElement(el, renderBell(unreadCount)) } : void 0
      });
    },
    [renderBell]
  );
  return /* @__PURE__ */ jsx(Mounter, { mount });
});
var Bell = withRenderer(_Bell);
export {
  Bell
};
//# sourceMappingURL=Bell.js.map