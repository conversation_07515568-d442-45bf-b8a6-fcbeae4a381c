import React__default from 'react';
import { NotificationClickHandler, NotificationActionClickHandler } from '@novu/js/ui';
import { NotificationRendererProps, SubjectBodyRendererProps, NoRendererProps } from '../utils/types.js';
import '@novu/js';

type NotificationProps = {
    onNotificationClick?: NotificationClickHandler;
    onPrimaryActionClick?: NotificationActionClickHandler;
    onSecondaryActionClick?: NotificationActionClickHandler;
} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);
declare const Notifications: React__default.ComponentType<NotificationProps & {
    children?: React__default.ReactNode | undefined;
}>;

export { type NotificationProps, Notifications };
