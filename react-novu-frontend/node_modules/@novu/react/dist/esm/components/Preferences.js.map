{"version": 3, "sources": ["../../../src/components/Preferences.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mounter } from './Mounter';\nimport { useNovuUI } from '../context/NovuUIContext';\n\nexport const Preferences = () => {\n  const { novuUI } = useNovuUI();\n\n  const mount = React.useCallback((element: HTMLElement) => {\n    return novuUI.mountComponent({\n      name: 'Preferences',\n      element,\n    });\n  }, []);\n\n  return <Mounter mount={mount} />;\n};\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAYjB;AAVF,IAAM,cAAc,MAAM;AAC/B,QAAM,EAAE,OAAO,IAAI,UAAU;AAE7B,QAAM,QAAQ,MAAM,YAAY,CAAC,YAAyB;AACxD,WAAO,OAAO,eAAe;AAAA,MAC3B,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,SAAO,oBAAC,WAAQ,OAAc;AAChC;", "names": []}