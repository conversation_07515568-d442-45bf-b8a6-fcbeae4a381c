// src/components/InboxContent.tsx
import React from "react";
import { Mounter } from "./Mounter.js";
import { useRenderer } from "../context/RendererContext.js";
import { useNovuUI } from "../context/NovuUIContext.js";
import { with<PERSON><PERSON><PERSON> } from "./Renderer.js";
import { jsx } from "react/jsx-runtime";
var _InboxContent = React.memo((props) => {
  const {
    onNotificationClick,
    onPrimaryActionClick,
    renderNotification,
    renderSubject,
    renderBody,
    onSecondaryActionClick,
    initialPage,
    hideNav
  } = props;
  const { novuUI } = useNovuUI();
  const { mountElement } = useRenderer();
  const mount = React.useCallback(
    (element) => {
      if (renderNotification) {
        return novuUI.mountComponent({
          name: "InboxContent",
          element,
          props: {
            renderNotification: renderNotification ? (el, notification) => mountElement(el, renderNotification(notification)) : void 0,
            onNotificationClick,
            onPrimaryActionClick,
            onSecondaryActionClick,
            initialPage,
            hideNav
          }
        });
      }
      return novuUI.mountComponent({
        name: "InboxContent",
        element,
        props: {
          renderSubject: renderSubject ? (el, notification) => mountElement(el, renderSubject(notification)) : void 0,
          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : void 0,
          onNotificationClick,
          onPrimaryActionClick,
          onSecondaryActionClick,
          initialPage,
          hideNav
        }
      });
    },
    [renderNotification, renderSubject, renderBody, onNotificationClick, onPrimaryActionClick, onSecondaryActionClick]
  );
  return /* @__PURE__ */ jsx(Mounter, { mount });
});
var InboxContent = withRenderer(_InboxContent);
export {
  InboxContent
};
//# sourceMappingURL=InboxContent.js.map