// src/components/NovuUI.tsx
import { NovuUI as NovuUIClass } from "@novu/js/ui";
import { useEffect, useMemo, useRef, useState } from "react";
import { NovuUIProvider } from "../context/NovuUIContext.js";
import { useDataRef } from "../hooks/internal/useDataRef.js";
import { adaptAppearanceForJs } from "../utils/appearance.js";
import { useRenderer } from "../context/RendererContext.js";
import { ShadowRootDetector } from "./ShadowRootDetector.js";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var findParentShadowRoot = (child) => {
  if (!child) {
    return null;
  }
  let node = child;
  while (node) {
    if (node instanceof Element && node.shadowRoot) {
      return node.shadowRoot;
    }
    if (node instanceof ShadowRoot) {
      return node;
    }
    node = node.parentNode;
    if (!node || node === document) {
      break;
    }
  }
  return null;
};
var NovuUI = ({ options, novu, children }) => {
  const shadowRootDetector = useRef(null);
  const { mountElement } = useRenderer();
  const adaptedAppearanceForUpdate = useMemo(
    () => adaptAppearanceForJs(options.appearance || {}, mountElement),
    [options.appearance, mountElement]
  );
  const adaptedOptions = useMemo(() => {
    return {
      ...options,
      appearance: adaptedAppearanceForUpdate,
      novu
    };
  }, [options, novu, adaptedAppearanceForUpdate]);
  const optionsRef = useDataRef(adaptedOptions);
  const [novuUI, setNovuUI] = useState();
  useEffect(() => {
    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);
    const instance = new NovuUIClass({
      ...optionsRef.current,
      container: optionsRef.current.container ?? parentShadowRoot
    });
    setNovuUI(instance);
    return () => {
      instance.unmount();
    };
  }, []);
  useEffect(() => {
    if (!novuUI) {
      return;
    }
    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);
    novuUI.updateContainer(options.container ?? parentShadowRoot);
    novuUI.updateAppearance(adaptedAppearanceForUpdate);
    novuUI.updateLocalization(options.localization);
    novuUI.updateTabs(options.tabs);
    novuUI.updateOptions(options.options);
    novuUI.updateRouterPush(options.routerPush);
  }, [
    shadowRootDetector,
    novuUI,
    adaptedAppearanceForUpdate,
    options.localization,
    options.tabs,
    options.options,
    options.routerPush
  ]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(ShadowRootDetector, { ref: shadowRootDetector }),
    novuUI && /* @__PURE__ */ jsx(NovuUIProvider, { value: { novuUI }, children })
  ] });
};
export {
  NovuUI
};
//# sourceMappingURL=NovuUI.js.map