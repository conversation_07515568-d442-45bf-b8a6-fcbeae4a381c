{"version": 3, "sources": ["../../../src/components/Mounter.tsx"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\ntype MounterProps = {\n  mount: (node: HTMLElement) => ((node: HTMLElement) => void) | void;\n};\n\n/**\n * Mounter allows you to mount a component to a DOM node.\n */\nexport function Mounter({ mount }: MounterProps) {\n  const ref = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    let unmount: (node: HTMLDivElement) => void | undefined;\n    const element = ref.current;\n    if (element && mount) {\n      const possibleUnmount = mount(element);\n      if (possibleUnmount) {\n        unmount = possibleUnmount;\n      }\n    }\n\n    return () => {\n      if (element && unmount) {\n        unmount(element);\n      }\n    };\n  }, [ref, mount]);\n\n  return <div ref={ref} />;\n}\n"], "mappings": ";AAAA,SAAS,WAAW,cAAc;AA6BzB;AApBF,SAAS,QAAQ,EAAE,MAAM,GAAiB;AAC/C,QAAM,MAAM,OAAuB,IAAI;AAEvC,YAAU,MAAM;AACd,QAAI;AACJ,UAAM,UAAU,IAAI;AACpB,QAAI,WAAW,OAAO;AACpB,YAAM,kBAAkB,MAAM,OAAO;AACrC,UAAI,iBAAiB;AACnB,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,MAAM;AACX,UAAI,WAAW,SAAS;AACtB,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,KAAK,KAAK,CAAC;AAEf,SAAO,oBAAC,SAAI,KAAU;AACxB;", "names": []}