{"version": 3, "sources": ["../../../src/components/Bell.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mounter } from './Mounter';\nimport { BellRenderer } from '../utils/types';\nimport { withRender<PERSON> } from './Renderer';\nimport { useNovuUI } from '../context/NovuUIContext';\nimport { useRenderer } from '../context/RendererContext';\n\nexport type BellProps = {\n  renderBell?: BellRenderer;\n};\n\nconst _Bell = React.memo((props: BellProps) => {\n  const { renderBell } = props;\n  const { novuUI } = useNovuUI();\n  const { mountElement } = useRenderer();\n\n  const mount = React.useCallback(\n    (element: HTMLElement) => {\n      return novuUI.mountComponent({\n        name: 'Bell',\n        element,\n        props: renderBell ? { renderBell: (el, unreadCount) => mountElement(el, renderBell(unreadCount)) } : undefined,\n      });\n    },\n    [renderBell]\n  );\n\n  return <Mounter mount={mount} />;\n});\n\nexport const Bell = withRenderer(_Bell);\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,eAAe;AAExB,SAAS,oBAAoB;AAC7B,SAAS,iBAAiB;AAC1B,SAAS,mBAAmB;AAsBnB;AAhBT,IAAM,QAAQ,MAAM,KAAK,CAAC,UAAqB;AAC7C,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,OAAO,IAAI,UAAU;AAC7B,QAAM,EAAE,aAAa,IAAI,YAAY;AAErC,QAAM,QAAQ,MAAM;AAAA,IAClB,CAAC,YAAyB;AACxB,aAAO,OAAO,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN;AAAA,QACA,OAAO,aAAa,EAAE,YAAY,CAAC,IAAI,gBAAgB,aAAa,IAAI,WAAW,WAAW,CAAC,EAAE,IAAI;AAAA,MACvG,CAAC;AAAA,IACH;AAAA,IACA,CAAC,UAAU;AAAA,EACb;AAEA,SAAO,oBAAC,WAAQ,OAAc;AAChC,CAAC;AAEM,IAAM,OAAO,aAAa,KAAK;", "names": []}