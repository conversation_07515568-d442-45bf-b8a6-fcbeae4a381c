// src/components/Inbox.tsx
import React, { useMemo } from "react";
import { Mounter } from "./Mounter.js";
import { useNovuUI } from "../context/NovuUIContext.js";
import { useRenderer } from "../context/RendererContext.js";
import { InternalNovuProvider, useNovu, useUnsafeNovu } from "../hooks/NovuProvider.js";
import { NovuUI } from "./NovuUI.js";
import { withRenderer } from "./Renderer.js";
import { jsx } from "react/jsx-runtime";
var DefaultInbox = (props) => {
  const {
    open,
    renderNotification,
    renderSubject,
    renderBody,
    renderBell,
    onNotificationClick,
    onPrimaryActionClick,
    onSecondaryActionClick,
    placement,
    placementOffset
  } = props;
  const { novuUI } = useNovuUI();
  const { mountElement } = useRenderer();
  const mount = React.useCallback(
    (element) => {
      if (renderNotification) {
        return novuUI.mountComponent({
          name: "Inbox",
          props: {
            open,
            renderNotification: renderNotification ? (el, notification) => mountElement(el, renderNotification(notification)) : void 0,
            renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : void 0,
            onNotificationClick,
            onPrimaryActionClick,
            onSecondaryActionClick,
            placementOffset,
            placement
          },
          element
        });
      }
      return novuUI.mountComponent({
        name: "Inbox",
        props: {
          open,
          renderSubject: renderSubject ? (el, notification) => mountElement(el, renderSubject(notification)) : void 0,
          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : void 0,
          renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : void 0,
          onNotificationClick,
          onPrimaryActionClick,
          onSecondaryActionClick,
          placementOffset,
          placement
        },
        element
      });
    },
    [
      open,
      renderNotification,
      renderSubject,
      renderBody,
      renderBell,
      onNotificationClick,
      onPrimaryActionClick,
      onSecondaryActionClick
    ]
  );
  return /* @__PURE__ */ jsx(Mounter, { mount });
};
var Inbox = React.memo((props) => {
  const { subscriberId, ...propsWithoutSubscriberId } = props;
  const subscriber = buildSubscriber(props.subscriber, props.subscriberId);
  const applicationIdentifier = props.applicationIdentifier ? props.applicationIdentifier : "";
  const novu = useUnsafeNovu();
  if (novu) {
    return /* @__PURE__ */ jsx(InboxChild, { ...propsWithoutSubscriberId, applicationIdentifier, subscriber });
  }
  const providerProps = {
    applicationIdentifier,
    subscriberHash: props.subscriberHash,
    backendUrl: props.backendUrl,
    socketUrl: props.socketUrl,
    subscriber
  };
  return /* @__PURE__ */ jsx(InternalNovuProvider, { ...providerProps, userAgentType: "components", children: /* @__PURE__ */ jsx(InboxChild, { ...propsWithoutSubscriberId, applicationIdentifier, subscriber }) });
});
var InboxChild = withRenderer(
  React.memo((props) => {
    const {
      localization,
      appearance,
      tabs,
      preferencesFilter,
      preferenceGroups,
      routerPush,
      applicationIdentifier = "",
      // for keyless we provide an empty string, the api will generate a identifier
      subscriberId,
      subscriberHash,
      backendUrl,
      socketUrl,
      subscriber
    } = props;
    const novu = useNovu();
    const options = useMemo(() => {
      return {
        localization,
        appearance,
        tabs,
        preferencesFilter,
        preferenceGroups,
        routerPush,
        options: {
          applicationIdentifier,
          subscriberHash,
          backendUrl,
          socketUrl,
          subscriber: buildSubscriber(subscriber, subscriberId)
        }
      };
    }, [
      localization,
      appearance,
      tabs,
      preferencesFilter,
      preferenceGroups,
      applicationIdentifier,
      subscriberId,
      subscriberHash,
      backendUrl,
      socketUrl,
      subscriber
    ]);
    if (isWithChildrenProps(props)) {
      return /* @__PURE__ */ jsx(NovuUI, { options, novu, children: props.children });
    }
    const {
      open,
      renderNotification,
      renderSubject,
      renderBody,
      renderBell,
      onNotificationClick,
      onPrimaryActionClick,
      onSecondaryActionClick,
      placementOffset,
      placement
    } = props;
    return /* @__PURE__ */ jsx(NovuUI, { options, novu, children: /* @__PURE__ */ jsx(
      DefaultInbox,
      {
        open,
        renderNotification,
        renderSubject,
        renderBody,
        renderBell,
        onNotificationClick,
        onPrimaryActionClick,
        onSecondaryActionClick,
        placement,
        placementOffset
      }
    ) });
  })
);
function isWithChildrenProps(props) {
  return "children" in props;
}
function buildSubscriber(subscriber, subscriberId) {
  if (subscriber) {
    return typeof subscriber === "string" ? { subscriberId: subscriber } : subscriber;
  }
  if (subscriberId) {
    return { subscriberId };
  }
  return { subscriberId: "" };
}
export {
  Inbox
};
//# sourceMappingURL=Inbox.js.map