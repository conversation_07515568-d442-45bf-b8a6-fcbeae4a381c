// src/components/Preferences.tsx
import React from "react";
import { Mounter } from "./Mounter.js";
import { useNovuUI } from "../context/NovuUIContext.js";
import { jsx } from "react/jsx-runtime";
var Preferences = () => {
  const { novuUI } = useNovuUI();
  const mount = React.useCallback((element) => {
    return novuUI.mountComponent({
      name: "Preferences",
      element
    });
  }, []);
  return /* @__PURE__ */ jsx(Mounter, { mount });
};
export {
  Preferences
};
//# sourceMappingURL=Preferences.js.map