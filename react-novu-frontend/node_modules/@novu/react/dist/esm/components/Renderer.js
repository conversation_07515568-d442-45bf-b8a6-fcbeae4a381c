// src/components/Renderer.tsx
import { useCallback, useState } from "react";
import { createPortal } from "react-dom";
import { RendererProvider } from "../context/RendererContext.js";
import { jsx, jsxs } from "react/jsx-runtime";
var Renderer = (props) => {
  const { children } = props;
  const [mountedElements, setMountedElements] = useState(/* @__PURE__ */ new Map());
  const mountElement = useCallback(
    (el, mountedElement) => {
      setMountedElements((prev) => {
        const newMountedElements = new Map(prev);
        newMountedElements.set(el, mountedElement);
        return newMountedElements;
      });
      return () => {
        setMountedElements((prev) => {
          const newMountedElements = new Map(prev);
          newMountedElements.delete(el);
          return newMountedElements;
        });
      };
    },
    [setMountedElements]
  );
  return /* @__PURE__ */ jsxs(RendererProvider, { value: { mountElement }, children: [
    [...mountedElements].map(([element, mountedElement]) => {
      return createPortal(mountedElement, element);
    }),
    children
  ] });
};
var withRenderer = (WrappedComponent) => {
  const HOC = (props) => {
    return /* @__PURE__ */ jsx(Renderer, { children: /* @__PURE__ */ jsx(WrappedComponent, { ...props }) });
  };
  HOC.displayName = `WithRenderer(${WrappedComponent.displayName || WrappedComponent.name || "Component"})`;
  return HOC;
};
export {
  Renderer,
  withRenderer
};
//# sourceMappingURL=Renderer.js.map