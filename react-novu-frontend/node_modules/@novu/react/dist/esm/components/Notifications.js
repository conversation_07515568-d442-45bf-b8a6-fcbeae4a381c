// src/components/Notifications.tsx
import React from "react";
import { Mounter } from "./Mounter.js";
import { useRenderer } from "../context/RendererContext.js";
import { useNovuUI } from "../context/NovuUIContext.js";
import { with<PERSON><PERSON><PERSON> } from "./Renderer.js";
import { jsx } from "react/jsx-runtime";
var _Notifications = React.memo((props) => {
  const {
    renderNotification,
    renderSubject,
    renderBody,
    onNotificationClick,
    onPrimaryActionClick,
    onSecondaryActionClick
  } = props;
  const { novuUI } = useNovuUI();
  const { mountElement } = useRenderer();
  const mount = React.useCallback(
    (element) => {
      if (renderNotification) {
        return novuUI.mountComponent({
          name: "Notifications",
          element,
          props: {
            renderNotification: renderNotification ? (el, notification) => mountElement(el, renderNotification(notification)) : void 0,
            onNotificationClick,
            onPrimaryActionClick,
            onSecondaryActionClick
          }
        });
      }
      return novuUI.mountComponent({
        name: "Notifications",
        element,
        props: {
          renderSubject: renderSubject ? (el, notification) => mountElement(el, renderSubject(notification)) : void 0,
          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : void 0,
          onNotificationClick,
          onPrimaryActionClick,
          onSecondaryActionClick
        }
      });
    },
    [renderNotification, renderSubject, renderBody, onNotificationClick, onPrimaryActionClick, onSecondaryActionClick]
  );
  return /* @__PURE__ */ jsx(Mounter, { mount });
});
var Notifications = withRenderer(_Notifications);
export {
  Notifications
};
//# sourceMappingURL=Notifications.js.map