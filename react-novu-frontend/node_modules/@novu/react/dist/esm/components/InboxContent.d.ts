import React__default from 'react';
import { Notification<PERSON>lick<PERSON>andler, NotificationActionClickHandler, InboxPage } from '@novu/js/ui';
import { NotificationRendererProps, SubjectBodyRendererProps, NoRendererProps } from '../utils/types.js';
import '@novu/js';

type InboxContentProps = {
    onNotificationClick?: NotificationClickHandler;
    onPrimaryActionClick?: NotificationActionClickHandler;
    onSecondaryActionClick?: NotificationActionClickHandler;
    initialPage?: InboxPage;
    hideNav?: boolean;
} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);
declare const InboxContent: React__default.ComponentType<InboxContentProps & {
    children?: React__default.ReactNode | undefined;
}>;

export { InboxContent, type InboxContentProps };
