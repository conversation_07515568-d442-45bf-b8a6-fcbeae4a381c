{"version": 3, "sources": ["../../../src/components/ShadowRootDetector.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ShadowRootDetector = React.forwardRef<HTMLDivElement>((props, ref) => {\n  return (\n    <div\n      ref={ref}\n      style={{\n        position: 'absolute',\n        width: 1,\n        height: 1,\n        padding: 0,\n        margin: -1,\n        overflow: 'hidden',\n        clip: 'rect(0, 0, 0, 0)',\n        whiteSpace: 'nowrap',\n        borderWidth: 0,\n      }}\n      data-shadow-root-detector\n      {...props}\n    />\n  );\n});\n"], "mappings": ";AAAA,OAAO,WAAW;AAId;AAFG,IAAM,qBAAqB,MAAM,WAA2B,CAAC,OAAO,QAAQ;AACjF,SACE;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AAAA,MACA,6BAAyB;AAAA,MACxB,GAAG;AAAA;AAAA,EACN;AAEJ,CAAC;", "names": []}