{"version": 3, "sources": ["../../../src/components/Notifications.tsx"], "sourcesContent": ["import React from 'react';\nimport type { NotificationClickHandler, NotificationActionClickHandler } from '@novu/js/ui';\nimport { Mounter } from './Mounter';\nimport { NoRendererProps, NotificationRendererProps, SubjectBodyRendererProps } from '../utils/types';\nimport { useRenderer } from '../context/RendererContext';\nimport { useNovuUI } from '../context/NovuUIContext';\nimport { withRenderer } from './Renderer';\n\nexport type NotificationProps = {\n  onNotificationClick?: NotificationClickHandler;\n  onPrimaryActionClick?: NotificationActionClickHandler;\n  onSecondaryActionClick?: NotificationActionClickHandler;\n} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);\n\nconst _Notifications = React.memo((props: NotificationProps) => {\n  const {\n    renderNotification,\n    renderSubject,\n    renderBody,\n    onNotificationClick,\n    onPrimaryActionClick,\n    onSecondaryActionClick,\n  } = props;\n  const { novuUI } = useNovuUI();\n  const { mountElement } = useRenderer();\n\n  const mount = React.useCallback(\n    (element: HTMLElement) => {\n      if (renderNotification) {\n        return novuUI.mountComponent({\n          name: 'Notifications',\n          element,\n          props: {\n            renderNotification: renderNotification\n              ? (el, notification) => mountElement(el, renderNotification(notification))\n              : undefined,\n            onNotificationClick,\n            onPrimaryActionClick,\n            onSecondaryActionClick,\n          },\n        });\n      }\n\n      return novuUI.mountComponent({\n        name: 'Notifications',\n        element,\n        props: {\n          renderSubject: renderSubject\n            ? (el, notification) => mountElement(el, renderSubject(notification))\n            : undefined,\n          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : undefined,\n          onNotificationClick,\n          onPrimaryActionClick,\n          onSecondaryActionClick,\n        },\n      });\n    },\n    [renderNotification, renderSubject, renderBody, onNotificationClick, onPrimaryActionClick, onSecondaryActionClick]\n  );\n\n  return <Mounter mount={mount} />;\n});\n\nexport const Notifications = withRenderer(_Notifications);\n"], "mappings": ";AAAA,OAAO,WAAW;AAElB,SAAS,eAAe;AAExB,SAAS,mBAAmB;AAC5B,SAAS,iBAAiB;AAC1B,SAAS,oBAAoB;AAsDpB;AA9CT,IAAM,iBAAiB,MAAM,KAAK,CAAC,UAA6B;AAC9D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,EAAE,OAAO,IAAI,UAAU;AAC7B,QAAM,EAAE,aAAa,IAAI,YAAY;AAErC,QAAM,QAAQ,MAAM;AAAA,IAClB,CAAC,YAAyB;AACxB,UAAI,oBAAoB;AACtB,eAAO,OAAO,eAAe;AAAA,UAC3B,MAAM;AAAA,UACN;AAAA,UACA,OAAO;AAAA,YACL,oBAAoB,qBAChB,CAAC,IAAI,iBAAiB,aAAa,IAAI,mBAAmB,YAAY,CAAC,IACvE;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,OAAO,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN;AAAA,QACA,OAAO;AAAA,UACL,eAAe,gBACX,CAAC,IAAI,iBAAiB,aAAa,IAAI,cAAc,YAAY,CAAC,IAClE;AAAA,UACJ,YAAY,aAAa,CAAC,IAAI,iBAAiB,aAAa,IAAI,WAAW,YAAY,CAAC,IAAI;AAAA,UAC5F;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,oBAAoB,eAAe,YAAY,qBAAqB,sBAAsB,sBAAsB;AAAA,EACnH;AAEA,SAAO,oBAAC,WAAQ,OAAc;AAChC,CAAC;AAEM,IAAM,gBAAgB,aAAa,cAAc;", "names": []}