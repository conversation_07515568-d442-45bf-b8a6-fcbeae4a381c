// src/components/ShadowRootDetector.tsx
import React from "react";
import { jsx } from "react/jsx-runtime";
var ShadowRootDetector = React.forwardRef((props, ref) => {
  return /* @__PURE__ */ jsx(
    "div",
    {
      ref,
      style: {
        position: "absolute",
        width: 1,
        height: 1,
        padding: 0,
        margin: -1,
        overflow: "hidden",
        clip: "rect(0, 0, 0, 0)",
        whiteSpace: "nowrap",
        borderWidth: 0
      },
      "data-shadow-root-detector": true,
      ...props
    }
  );
});
export {
  ShadowRootDetector
};
//# sourceMappingURL=ShadowRootDetector.js.map