// src/hooks/useNotifications.ts
import { useState, useEffect, useRef } from "react";
import { isSameFilter } from "@novu/js";
import { useNovu } from "./NovuProvider.js";
var useNotifications = (props) => {
  const { tags, data: dataFilter, read, archived = false, snoozed = false, limit, onSuccess, onError } = props || {};
  const filterRef = useRef(void 0);
  const { notifications, on } = useNovu();
  const [data, setData] = useState();
  const [error, setError] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const length = data?.length;
  const after = length ? data[length - 1].id : void 0;
  const sync = (event) => {
    if (!event.data || filterRef.current && !isSameFilter(filterRef.current, event.data.filter)) {
      return;
    }
    setData(event.data.notifications);
    setHasMore(event.data.hasMore);
  };
  useEffect(() => {
    const cleanup = on("notifications.list.updated", sync);
    return () => {
      cleanup();
    };
  }, []);
  useEffect(() => {
    const newFilter = { tags, data: dataFilter, read, archived, snoozed };
    if (filterRef.current && isSameFilter(filterRef.current, newFilter)) {
      return;
    }
    notifications.clearCache({ filter: filterRef.current });
    filterRef.current = newFilter;
    fetchNotifications({ refetch: true });
  }, [tags, dataFilter, read, archived, snoozed]);
  const fetchNotifications = async (options) => {
    if (options?.refetch) {
      setError(void 0);
      setIsLoading(true);
      setIsFetching(false);
    }
    setIsFetching(true);
    const response = await notifications.list({
      tags,
      data: dataFilter,
      read,
      archived,
      snoozed,
      limit,
      after: options?.refetch ? void 0 : after
    });
    if (response.error) {
      setError(response.error);
      onError?.(response.error);
    } else {
      onSuccess?.(response.data.notifications);
      setData(response.data.notifications);
      setHasMore(response.data.hasMore);
    }
    setIsLoading(false);
    setIsFetching(false);
  };
  const refetch = () => {
    notifications.clearCache({ filter: { tags, read, archived, snoozed, data: dataFilter } });
    return fetchNotifications({ refetch: true });
  };
  const fetchMore = async () => {
    if (!hasMore || isFetching) return;
    return fetchNotifications();
  };
  const readAll = async () => {
    return await notifications.readAll({ tags, data: dataFilter });
  };
  const archiveAll = async () => {
    return await notifications.archiveAll({ tags, data: dataFilter });
  };
  const archiveAllRead = async () => {
    return await notifications.archiveAllRead({ tags, data: dataFilter });
  };
  return {
    readAll,
    archiveAll,
    archiveAllRead,
    notifications: data,
    error,
    isLoading,
    isFetching,
    refetch,
    fetchMore,
    hasMore
  };
};
export {
  useNotifications
};
//# sourceMappingURL=useNotifications.js.map