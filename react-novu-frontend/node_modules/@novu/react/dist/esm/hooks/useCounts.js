// src/hooks/useCounts.ts
import { useEffect, useState } from "react";
import { areTagsEqual } from "@novu/js";
import { useNovu } from "./NovuProvider.js";
import { useWebSocketEvent } from "./internal/useWebsocketEvent.js";
var useCounts = (props) => {
  const { filters, onSuccess, onError } = props;
  const { notifications } = useNovu();
  const [error, setError] = useState();
  const [counts, setCounts] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const sync = async (notification) => {
    const existingCounts = counts ?? filters.map((filter) => ({ count: 0, filter }));
    let countFiltersToFetch = [];
    if (notification) {
      for (let i = 0; i < existingCounts.length; i++) {
        const filter = filters[i];
        if (areTagsEqual(filter.tags, notification.tags)) {
          countFiltersToFetch.push(filter);
        }
      }
    } else {
      countFiltersToFetch = filters;
    }
    if (countFiltersToFetch.length === 0) {
      return;
    }
    setIsFetching(true);
    const countsRes = await notifications.count({ filters: countFiltersToFetch });
    setIsFetching(false);
    setIsLoading(false);
    if (countsRes.error) {
      setError(countsRes.error);
      onError?.(countsRes.error);
      return;
    }
    const data = countsRes.data;
    onSuccess?.(data.counts);
    setCounts((oldCounts) => {
      const newCounts = [];
      const countsReceived = data.counts;
      for (let i = 0; i < existingCounts.length; i++) {
        const countReceived = countsReceived.find((c) => areTagsEqual(c.filter.tags, existingCounts[i].filter.tags));
        const count = countReceived || oldCounts && oldCounts[i];
        if (count) {
          newCounts.push(count);
        }
      }
      return newCounts;
    });
  };
  useWebSocketEvent({
    event: "notifications.notification_received",
    eventHandler: (data) => {
      sync(data.result);
    }
  });
  useWebSocketEvent({
    event: "notifications.unread_count_changed",
    eventHandler: () => {
      sync();
    }
  });
  useEffect(() => {
    setError(void 0);
    setIsLoading(true);
    setIsFetching(false);
    sync();
  }, [JSON.stringify(filters)]);
  const refetch = async () => {
    await sync();
  };
  return { counts, error, refetch, isLoading, isFetching };
};
export {
  useCounts
};
//# sourceMappingURL=useCounts.js.map