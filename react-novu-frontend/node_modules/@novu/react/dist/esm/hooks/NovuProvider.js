// src/hooks/NovuProvider.tsx
import { Novu } from "@novu/js";
import { createContext, useContext, useMemo, useEffect } from "react";
import { jsx } from "react/jsx-runtime";
var version = "3.6.0";
var name = "@novu/react";
var baseUserAgent = `${name}@${version}`;
var NovuContext = createContext(void 0);
var NovuProvider = (props) => {
  const { subscriberId, ...propsWithoutSubscriberId } = props;
  const subscriberObj = buildSubscriber(subscriberId, props.subscriber);
  const applicationIdentifier = propsWithoutSubscriberId.applicationIdentifier ? propsWithoutSubscriberId.applicationIdentifier : "";
  const providerProps = {
    ...propsWithoutSubscriberId,
    applicationIdentifier,
    subscriber: subscriberObj
  };
  return /* @__PURE__ */ jsx(InternalNovuProvider, { ...providerProps, applicationIdentifier, userAgentType: "hooks", children: props.children });
};
var InternalNovuProvider = (props) => {
  const applicationIdentifier = props.applicationIdentifier || "";
  const subscriberObj = buildSubscriber(props.subscriberId, props.subscriber);
  const { children, subscriberId, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType } = props;
  const novu = useMemo(
    () => new Novu({
      applicationIdentifier,
      subscriberHash,
      backendUrl,
      apiUrl,
      socketUrl,
      useCache,
      __userAgent: `${baseUserAgent} ${userAgentType}`,
      subscriber: subscriberObj
    }),
    [applicationIdentifier, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType]
  );
  useEffect(() => {
    novu.changeSubscriber({
      subscriber: subscriberObj,
      subscriberHash: props.subscriberHash
    });
  }, [subscriberObj.subscriberId, props.subscriberHash, novu]);
  return /* @__PURE__ */ jsx(NovuContext.Provider, { value: novu, children });
};
var useNovu = () => {
  const context = useContext(NovuContext);
  if (!context) {
    throw new Error("useNovu must be used within a <NovuProvider />");
  }
  return context;
};
var useUnsafeNovu = () => {
  const context = useContext(NovuContext);
  return context;
};
function buildSubscriber(subscriberId, subscriber) {
  if (subscriber) {
    return typeof subscriber === "string" ? { subscriberId: subscriber } : subscriber;
  }
  if (subscriberId) {
    return { subscriberId };
  }
  return { subscriberId: "" };
}
export {
  InternalNovuProvider,
  NovuProvider,
  useNovu,
  useUnsafeNovu
};
//# sourceMappingURL=NovuProvider.js.map