{"version": 3, "sources": ["../../../src/hooks/usePreferences.ts"], "sourcesContent": ["import { NovuError, Preference } from '@novu/js';\nimport { useEffect, useState } from 'react';\nimport { useNovu } from './NovuProvider';\n\nexport type UsePreferencesProps = {\n  filter?: { tags?: string[] };\n  onSuccess?: (data: Preference[]) => void;\n  onError?: (error: NovuError) => void;\n};\n\nexport type UsePreferencesResult = {\n  preferences?: Preference[];\n  error?: NovuError;\n  isLoading: boolean; // initial loading\n  isFetching: boolean; // the request is in flight\n  refetch: () => Promise<void>;\n};\n\nexport const usePreferences = (props?: UsePreferencesProps): UsePreferencesResult => {\n  const { onSuccess, onError } = props || {};\n  const [data, setData] = useState<Preference[]>();\n  const { preferences, on } = useNovu();\n  const [error, setError] = useState<NovuError>();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isFetching, setIsFetching] = useState(false);\n\n  const sync = (event: { data?: Preference[] }) => {\n    if (!event.data) {\n      return;\n    }\n    setData(event.data);\n  };\n\n  useEffect(() => {\n    fetchPreferences();\n\n    const listUpdatedCleanup = on('preferences.list.updated', sync);\n    const listPendingCleanup = on('preferences.list.pending', sync);\n    const listResolvedCleanup = on('preferences.list.resolved', sync);\n\n    return () => {\n      listUpdatedCleanup();\n      listPendingCleanup();\n      listResolvedCleanup();\n    };\n  }, []);\n\n  const fetchPreferences = async () => {\n    setIsFetching(true);\n    const response = await preferences.list(props?.filter);\n    if (response.error) {\n      setError(response.error);\n      onError?.(response.error);\n    } else {\n      onSuccess?.(response.data!);\n    }\n    setIsLoading(false);\n    setIsFetching(false);\n  };\n\n  const refetch = () => {\n    preferences.cache.clearAll();\n\n    return fetchPreferences();\n  };\n\n  return {\n    preferences: data,\n    error,\n    isLoading,\n    isFetching,\n    refetch,\n  };\n};\n"], "mappings": ";AACA,SAAS,WAAW,gBAAgB;AACpC,SAAS,eAAe;AAgBjB,IAAM,iBAAiB,CAAC,UAAsD;AACnF,QAAM,EAAE,WAAW,QAAQ,IAAI,SAAS,CAAC;AACzC,QAAM,CAAC,MAAM,OAAO,IAAI,SAAuB;AAC/C,QAAM,EAAE,aAAa,GAAG,IAAI,QAAQ;AACpC,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAoB;AAC9C,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,IAAI;AAC/C,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,KAAK;AAElD,QAAM,OAAO,CAAC,UAAmC;AAC/C,QAAI,CAAC,MAAM,MAAM;AACf;AAAA,IACF;AACA,YAAQ,MAAM,IAAI;AAAA,EACpB;AAEA,YAAU,MAAM;AACd,qBAAiB;AAEjB,UAAM,qBAAqB,GAAG,4BAA4B,IAAI;AAC9D,UAAM,qBAAqB,GAAG,4BAA4B,IAAI;AAC9D,UAAM,sBAAsB,GAAG,6BAA6B,IAAI;AAEhE,WAAO,MAAM;AACX,yBAAmB;AACnB,yBAAmB;AACnB,0BAAoB;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAmB,YAAY;AACnC,kBAAc,IAAI;AAClB,UAAM,WAAW,MAAM,YAAY,KAAK,OAAO,MAAM;AACrD,QAAI,SAAS,OAAO;AAClB,eAAS,SAAS,KAAK;AACvB,gBAAU,SAAS,KAAK;AAAA,IAC1B,OAAO;AACL,kBAAY,SAAS,IAAK;AAAA,IAC5B;AACA,iBAAa,KAAK;AAClB,kBAAc,KAAK;AAAA,EACrB;AAEA,QAAM,UAAU,MAAM;AACpB,gBAAY,MAAM,SAAS;AAE3B,WAAO,iBAAiB;AAAA,EAC1B;AAEA,SAAO;AAAA,IACL,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}