// src/hooks/internal/useBrowserTabsChannel.ts
import { useEffect, useState } from "react";
var useBrowserTabsChannel = ({
  channelName,
  onMessage
}) => {
  const [tabsChannel] = useState(
    typeof BroadcastChannel !== "undefined" ? new BroadcastChannel(channelName) : void 0
  );
  const postMessage = (data) => {
    tabsChannel?.postMessage(data);
  };
  useEffect(() => {
    const listener = (event) => {
      onMessage(event.data);
    };
    tabsChannel?.addEventListener("message", listener);
    return () => {
      tabsChannel?.removeEventListener("message", listener);
    };
  }, []);
  return { postMessage };
};
export {
  useBrowserTabsChannel
};
//# sourceMappingURL=useBrowserTabsChannel.js.map