{"version": 3, "sources": ["../../../../src/hooks/internal/useWebsocketEvent.ts"], "sourcesContent": ["import { EventHandler, Events, SocketEventNames } from '@novu/js';\nimport { useEffect } from 'react';\nimport { useNovu } from '../NovuProvider';\nimport { requestLock } from '../../utils/requestLock';\nimport { useBrowserTabsChannel } from './useBrowserTabsChannel';\n\nexport const useWebSocketEvent = <E extends SocketEventNames>({\n  event: webSocketEvent,\n  eventHandler: onMessage,\n}: {\n  event: E;\n  eventHandler: (args: Events[E]) => void;\n}) => {\n  const novu = useNovu();\n  const channelName = `nv_ws_connection:a=${novu.applicationIdentifier}:s=${novu.subscriberId}:e=${webSocketEvent}`;\n\n  const { postMessage } = useBrowserTabsChannel({\n    channelName,\n    onMessage,\n  });\n\n  const updateReadCount: EventHandler<Events[E]> = (data) => {\n    onMessage(data);\n    postMessage(data);\n  };\n\n  useEffect(() => {\n    let cleanup: () => void;\n    const resolveLock = requestLock(channelName, () => {\n      cleanup = novu.on(webSocketEvent, updateReadCount);\n    });\n\n    return () => {\n      if (cleanup) {\n        cleanup();\n      }\n\n      resolveLock();\n    };\n  }, []);\n};\n"], "mappings": ";AACA,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,mBAAmB;AAC5B,SAAS,6BAA6B;AAE/B,IAAM,oBAAoB,CAA6B;AAAA,EAC5D,OAAO;AAAA,EACP,cAAc;AAChB,MAGM;AACJ,QAAM,OAAO,QAAQ;AACrB,QAAM,cAAc,sBAAsB,KAAK,qBAAqB,MAAM,KAAK,YAAY,MAAM,cAAc;AAE/G,QAAM,EAAE,YAAY,IAAI,sBAAsB;AAAA,IAC5C;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,kBAA2C,CAAC,SAAS;AACzD,cAAU,IAAI;AACd,gBAAY,IAAI;AAAA,EAClB;AAEA,YAAU,MAAM;AACd,QAAI;AACJ,UAAM,cAAc,YAAY,aAAa,MAAM;AACjD,gBAAU,KAAK,GAAG,gBAAgB,eAAe;AAAA,IACnD,CAAC;AAED,WAAO,MAAM;AACX,UAAI,SAAS;AACX,gBAAQ;AAAA,MACV;AAEA,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,CAAC;AACP;", "names": []}