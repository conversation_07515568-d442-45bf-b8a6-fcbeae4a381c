// src/hooks/internal/useWebsocketEvent.ts
import { useEffect } from "react";
import { useNovu } from "../NovuProvider.js";
import { requestLock } from "../../utils/requestLock.js";
import { useBrowserTabsChannel } from "./useBrowserTabsChannel.js";
var useWebSocketEvent = ({
  event: webSocketEvent,
  eventHandler: onMessage
}) => {
  const novu = useNovu();
  const channelName = `nv_ws_connection:a=${novu.applicationIdentifier}:s=${novu.subscriberId}:e=${webSocketEvent}`;
  const { postMessage } = useBrowserTabsChannel({
    channelName,
    onMessage
  });
  const updateReadCount = (data) => {
    onMessage(data);
    postMessage(data);
  };
  useEffect(() => {
    let cleanup;
    const resolveLock = requestLock(channelName, () => {
      cleanup = novu.on(webSocketEvent, updateReadCount);
    });
    return () => {
      if (cleanup) {
        cleanup();
      }
      resolveLock();
    };
  }, []);
};
export {
  useWebSocketEvent
};
//# sourceMappingURL=useWebsocketEvent.js.map