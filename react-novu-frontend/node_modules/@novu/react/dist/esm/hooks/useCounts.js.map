{"version": 3, "sources": ["../../../src/hooks/useCounts.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { Notification, NotificationFilter, NovuError, areTagsEqual } from '@novu/js';\nimport { useNovu } from './NovuProvider';\nimport { useWebSocketEvent } from './internal/useWebsocketEvent';\n\ntype Count = {\n  count: number;\n  filter: NotificationFilter;\n};\n\nexport type UseCountsProps = {\n  filters: NotificationFilter[];\n  onSuccess?: (data: Count[]) => void;\n  onError?: (error: NovuError) => void;\n};\n\nexport type UseCountsResult = {\n  counts?: Count[];\n  error?: NovuError;\n  isLoading: boolean; // initial loading\n  isFetching: boolean; // the request is in flight\n  refetch: () => Promise<void>;\n};\n\nexport const useCounts = (props: UseCountsProps): UseCountsResult => {\n  const { filters, onSuccess, onError } = props;\n  const { notifications } = useNovu();\n  const [error, setError] = useState<NovuError>();\n  const [counts, setCounts] = useState<Count[]>();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isFetching, setIsFetching] = useState(false);\n\n  const sync = async (notification?: Notification) => {\n    const existingCounts = counts ?? filters.map((filter) => ({ count: 0, filter }));\n    let countFiltersToFetch: NotificationFilter[] = [];\n    if (notification) {\n      // eslint-disable-next-line no-plusplus\n      for (let i = 0; i < existingCounts.length; i++) {\n        const filter = filters[i];\n        if (areTagsEqual(filter.tags, notification.tags)) {\n          countFiltersToFetch.push(filter);\n        }\n      }\n    } else {\n      countFiltersToFetch = filters;\n    }\n\n    if (countFiltersToFetch.length === 0) {\n      return;\n    }\n\n    setIsFetching(true);\n    const countsRes = await notifications.count({ filters: countFiltersToFetch });\n    setIsFetching(false);\n    setIsLoading(false);\n    if (countsRes.error) {\n      setError(countsRes.error);\n      onError?.(countsRes.error);\n\n      return;\n    }\n    const data = countsRes.data!;\n    onSuccess?.(data.counts);\n\n    setCounts((oldCounts) => {\n      const newCounts: Count[] = [];\n      const countsReceived = data.counts;\n\n      // eslint-disable-next-line no-plusplus\n      for (let i = 0; i < existingCounts.length; i++) {\n        const countReceived = countsReceived.find((c) => areTagsEqual(c.filter.tags, existingCounts[i].filter.tags));\n        const count = countReceived || (oldCounts && oldCounts[i]);\n        if (count) {\n          newCounts.push(count);\n        }\n      }\n\n      return newCounts;\n    });\n  };\n\n  useWebSocketEvent({\n    event: 'notifications.notification_received',\n    eventHandler: (data) => {\n      sync(data.result);\n    },\n  });\n\n  useWebSocketEvent({\n    event: 'notifications.unread_count_changed',\n    eventHandler: () => {\n      sync();\n    },\n  });\n\n  useEffect(() => {\n    setError(undefined);\n    setIsLoading(true);\n    setIsFetching(false);\n    sync();\n  }, [JSON.stringify(filters)]);\n\n  const refetch = async () => {\n    await sync();\n  };\n\n  return { counts, error, refetch, isLoading, isFetching };\n};\n"], "mappings": ";AAAA,SAAS,WAAW,gBAAgB;AACpC,SAAsD,oBAAoB;AAC1E,SAAS,eAAe;AACxB,SAAS,yBAAyB;AAqB3B,IAAM,YAAY,CAAC,UAA2C;AACnE,QAAM,EAAE,SAAS,WAAW,QAAQ,IAAI;AACxC,QAAM,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAoB;AAC9C,QAAM,CAAC,QAAQ,SAAS,IAAI,SAAkB;AAC9C,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,IAAI;AAC/C,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,KAAK;AAElD,QAAM,OAAO,OAAO,iBAAgC;AAClD,UAAM,iBAAiB,UAAU,QAAQ,IAAI,CAAC,YAAY,EAAE,OAAO,GAAG,OAAO,EAAE;AAC/E,QAAI,sBAA4C,CAAC;AACjD,QAAI,cAAc;AAEhB,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,SAAS,QAAQ,CAAC;AACxB,YAAI,aAAa,OAAO,MAAM,aAAa,IAAI,GAAG;AAChD,8BAAoB,KAAK,MAAM;AAAA,QACjC;AAAA,MACF;AAAA,IACF,OAAO;AACL,4BAAsB;AAAA,IACxB;AAEA,QAAI,oBAAoB,WAAW,GAAG;AACpC;AAAA,IACF;AAEA,kBAAc,IAAI;AAClB,UAAM,YAAY,MAAM,cAAc,MAAM,EAAE,SAAS,oBAAoB,CAAC;AAC5E,kBAAc,KAAK;AACnB,iBAAa,KAAK;AAClB,QAAI,UAAU,OAAO;AACnB,eAAS,UAAU,KAAK;AACxB,gBAAU,UAAU,KAAK;AAEzB;AAAA,IACF;AACA,UAAM,OAAO,UAAU;AACvB,gBAAY,KAAK,MAAM;AAEvB,cAAU,CAAC,cAAc;AACvB,YAAM,YAAqB,CAAC;AAC5B,YAAM,iBAAiB,KAAK;AAG5B,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,gBAAgB,eAAe,KAAK,CAAC,MAAM,aAAa,EAAE,OAAO,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC;AAC3G,cAAM,QAAQ,iBAAkB,aAAa,UAAU,CAAC;AACxD,YAAI,OAAO;AACT,oBAAU,KAAK,KAAK;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,oBAAkB;AAAA,IAChB,OAAO;AAAA,IACP,cAAc,CAAC,SAAS;AACtB,WAAK,KAAK,MAAM;AAAA,IAClB;AAAA,EACF,CAAC;AAED,oBAAkB;AAAA,IAChB,OAAO;AAAA,IACP,cAAc,MAAM;AAClB,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AAED,YAAU,MAAM;AACd,aAAS,MAAS;AAClB,iBAAa,IAAI;AACjB,kBAAc,KAAK;AACnB,SAAK;AAAA,EACP,GAAG,CAAC,KAAK,UAAU,OAAO,CAAC,CAAC;AAE5B,QAAM,UAAU,YAAY;AAC1B,UAAM,KAAK;AAAA,EACb;AAEA,SAAO,EAAE,QAAQ,OAAO,SAAS,WAAW,WAAW;AACzD;", "names": []}