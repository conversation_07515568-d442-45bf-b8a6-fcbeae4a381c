// src/hooks/usePreferences.ts
import { useEffect, useState } from "react";
import { useNovu } from "./NovuProvider.js";
var usePreferences = (props) => {
  const { onSuccess, onError } = props || {};
  const [data, setData] = useState();
  const { preferences, on } = useNovu();
  const [error, setError] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const sync = (event) => {
    if (!event.data) {
      return;
    }
    setData(event.data);
  };
  useEffect(() => {
    fetchPreferences();
    const listUpdatedCleanup = on("preferences.list.updated", sync);
    const listPendingCleanup = on("preferences.list.pending", sync);
    const listResolvedCleanup = on("preferences.list.resolved", sync);
    return () => {
      listUpdatedCleanup();
      listPendingCleanup();
      listResolvedCleanup();
    };
  }, []);
  const fetchPreferences = async () => {
    setIsFetching(true);
    const response = await preferences.list(props?.filter);
    if (response.error) {
      setError(response.error);
      onError?.(response.error);
    } else {
      onSuccess?.(response.data);
    }
    setIsLoading(false);
    setIsFetching(false);
  };
  const refetch = () => {
    preferences.cache.clearAll();
    return fetchPreferences();
  };
  return {
    preferences: data,
    error,
    isLoading,
    isFetching,
    refetch
  };
};
export {
  usePreferences
};
//# sourceMappingURL=usePreferences.js.map