import { NotificationFilter, NovuError } from '@novu/js';

type Count = {
    count: number;
    filter: NotificationFilter;
};
type UseCountsProps = {
    filters: NotificationFilter[];
    onSuccess?: (data: Count[]) => void;
    onError?: (error: NovuError) => void;
};
type UseCountsResult = {
    counts?: Count[];
    error?: NovuError;
    isLoading: boolean;
    isFetching: boolean;
    refetch: () => Promise<void>;
};
declare const useCounts: (props: UseCountsProps) => UseCountsResult;

export { type UseCountsProps, type UseCountsResult, useCounts };
