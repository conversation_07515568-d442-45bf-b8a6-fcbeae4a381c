import * as react_jsx_runtime from 'react/jsx-runtime';
import { NovuOptions, Novu } from '@novu/js';
import { ReactNode } from 'react';

type NovuProviderProps = NovuOptions & {
    children: ReactNode;
};
declare const NovuProvider: (props: NovuProviderProps) => react_jsx_runtime.JSX.Element;
/**
 * @internal Should be used internally not to be exposed outside of the library
 * This is needed to differentiate between the hooks and components user agents
 * Better to use this internally to avoid confusion.
 */
declare const InternalNovuProvider: (props: NovuProviderProps & {
    userAgentType: "components" | "hooks";
}) => react_jsx_runtime.JSX.Element;
declare const useNovu: () => Novu;
declare const useUnsafeNovu: () => Novu | undefined;

export { InternalNovuProvider, NovuProvider, type NovuProviderProps, useNovu, useUnsafeNovu };
