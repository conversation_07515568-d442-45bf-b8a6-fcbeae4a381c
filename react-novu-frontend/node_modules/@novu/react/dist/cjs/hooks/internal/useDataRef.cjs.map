{"version": 3, "sources": ["../../../../src/hooks/internal/useDataRef.ts"], "sourcesContent": ["import { useRef } from 'react';\n\nexport const useDataRef = <T>(data: T) => {\n  const ref = useRef(data);\n  ref.current = data;\n\n  return ref;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAuB;AAEhB,IAAM,aAAa,CAAI,SAAY;AACxC,QAAM,UAAM,qBAAO,IAAI;AACvB,MAAI,UAAU;AAEd,SAAO;AACT;", "names": []}