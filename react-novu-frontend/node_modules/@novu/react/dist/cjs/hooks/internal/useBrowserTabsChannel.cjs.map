{"version": 3, "sources": ["../../../../src/hooks/internal/useBrowserTabsChannel.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nexport const useBrowserTabsChannel = <T = unknown>({\n  channelName,\n  onMessage,\n}: {\n  channelName: string;\n  onMessage: (args: T) => void;\n}) => {\n  const [tabsChannel] = useState(\n    typeof BroadcastChannel !== 'undefined' ? new BroadcastChannel(channelName) : undefined\n  );\n\n  const postMessage = (data: T) => {\n    tabsChannel?.postMessage(data);\n  };\n\n  useEffect(() => {\n    const listener = (event: MessageEvent<T>) => {\n      onMessage(event.data);\n    };\n\n    tabsChannel?.addEventListener('message', listener);\n\n    return () => {\n      tabsChannel?.removeEventListener('message', listener);\n    };\n  }, []);\n\n  return { postMessage };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAoC;AAE7B,IAAM,wBAAwB,CAAc;AAAA,EACjD;AAAA,EACA;AACF,MAGM;AACJ,QAAM,CAAC,WAAW,QAAI;AAAA,IACpB,OAAO,qBAAqB,cAAc,IAAI,iBAAiB,WAAW,IAAI;AAAA,EAChF;AAEA,QAAM,cAAc,CAAC,SAAY;AAC/B,+CAAa,YAAY;AAAA,EAC3B;AAEA,8BAAU,MAAM;AACd,UAAM,WAAW,CAAC,UAA2B;AAC3C,gBAAU,MAAM,IAAI;AAAA,IACtB;AAEA,+CAAa,iBAAiB,WAAW;AAEzC,WAAO,MAAM;AACX,iDAAa,oBAAoB,WAAW;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,EAAE,YAAY;AACvB;", "names": []}