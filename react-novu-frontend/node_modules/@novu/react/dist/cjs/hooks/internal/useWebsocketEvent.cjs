"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/internal/useWebsocketEvent.ts
var useWebsocketEvent_exports = {};
__export(useWebsocketEvent_exports, {
  useWebSocketEvent: () => useWebSocketEvent
});
module.exports = __toCommonJS(useWebsocketEvent_exports);
var import_react = require("react");
var import_NovuProvider = require("../NovuProvider.cjs");
var import_requestLock = require("../../utils/requestLock.cjs");
var import_useBrowserTabsChannel = require("./useBrowserTabsChannel.cjs");
var useWebSocketEvent = ({
  event: webSocketEvent,
  eventHandler: onMessage
}) => {
  const novu = (0, import_NovuProvider.useNovu)();
  const channelName = `nv_ws_connection:a=${novu.applicationIdentifier}:s=${novu.subscriberId}:e=${webSocketEvent}`;
  const { postMessage } = (0, import_useBrowserTabsChannel.useBrowserTabsChannel)({
    channelName,
    onMessage
  });
  const updateReadCount = (data) => {
    onMessage(data);
    postMessage(data);
  };
  (0, import_react.useEffect)(() => {
    let cleanup;
    const resolveLock = (0, import_requestLock.requestLock)(channelName, () => {
      cleanup = novu.on(webSocketEvent, updateReadCount);
    });
    return () => {
      if (cleanup) {
        cleanup();
      }
      resolveLock();
    };
  }, []);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useWebSocketEvent
});
//# sourceMappingURL=useWebsocketEvent.cjs.map