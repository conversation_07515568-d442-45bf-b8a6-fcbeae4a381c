"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/internal/useBrowserTabsChannel.ts
var useBrowserTabsChannel_exports = {};
__export(useBrowserTabsChannel_exports, {
  useBrowserTabsChannel: () => useBrowserTabsChannel
});
module.exports = __toCommonJS(useBrowserTabsChannel_exports);
var import_react = require("react");
var useBrowserTabsChannel = ({
  channelName,
  onMessage
}) => {
  const [tabsChannel] = (0, import_react.useState)(
    typeof BroadcastChannel !== "undefined" ? new BroadcastChannel(channelName) : void 0
  );
  const postMessage = (data) => {
    tabsChannel == null ? void 0 : tabsChannel.postMessage(data);
  };
  (0, import_react.useEffect)(() => {
    const listener = (event) => {
      onMessage(event.data);
    };
    tabsChannel == null ? void 0 : tabsChannel.addEventListener("message", listener);
    return () => {
      tabsChannel == null ? void 0 : tabsChannel.removeEventListener("message", listener);
    };
  }, []);
  return { postMessage };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useBrowserTabsChannel
});
//# sourceMappingURL=useBrowserTabsChannel.cjs.map