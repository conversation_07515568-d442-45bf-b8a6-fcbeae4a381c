{"version": 3, "sources": ["../../../../src/hooks/internal/useWebsocketEvent.ts"], "sourcesContent": ["import { EventHandler, Events, SocketEventNames } from '@novu/js';\nimport { useEffect } from 'react';\nimport { useNovu } from '../NovuProvider';\nimport { requestLock } from '../../utils/requestLock';\nimport { useBrowserTabsChannel } from './useBrowserTabsChannel';\n\nexport const useWebSocketEvent = <E extends SocketEventNames>({\n  event: webSocketEvent,\n  eventHandler: onMessage,\n}: {\n  event: E;\n  eventHandler: (args: Events[E]) => void;\n}) => {\n  const novu = useNovu();\n  const channelName = `nv_ws_connection:a=${novu.applicationIdentifier}:s=${novu.subscriberId}:e=${webSocketEvent}`;\n\n  const { postMessage } = useBrowserTabsChannel({\n    channelName,\n    onMessage,\n  });\n\n  const updateReadCount: EventHandler<Events[E]> = (data) => {\n    onMessage(data);\n    postMessage(data);\n  };\n\n  useEffect(() => {\n    let cleanup: () => void;\n    const resolveLock = requestLock(channelName, () => {\n      cleanup = novu.on(webSocketEvent, updateReadCount);\n    });\n\n    return () => {\n      if (cleanup) {\n        cleanup();\n      }\n\n      resolveLock();\n    };\n  }, []);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAA0B;AAC1B,0BAAwB;AACxB,yBAA4B;AAC5B,mCAAsC;AAE/B,IAAM,oBAAoB,CAA6B;AAAA,EAC5D,OAAO;AAAA,EACP,cAAc;AAChB,MAGM;AACJ,QAAM,WAAO,6BAAQ;AACrB,QAAM,cAAc,sBAAsB,KAAK,qBAAqB,MAAM,KAAK,YAAY,MAAM,cAAc;AAE/G,QAAM,EAAE,YAAY,QAAI,oDAAsB;AAAA,IAC5C;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,kBAA2C,CAAC,SAAS;AACzD,cAAU,IAAI;AACd,gBAAY,IAAI;AAAA,EAClB;AAEA,8BAAU,MAAM;AACd,QAAI;AACJ,UAAM,kBAAc,gCAAY,aAAa,MAAM;AACjD,gBAAU,KAAK,GAAG,gBAAgB,eAAe;AAAA,IACnD,CAAC;AAED,WAAO,MAAM;AACX,UAAI,SAAS;AACX,gBAAQ;AAAA,MACV;AAEA,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,CAAC;AACP;", "names": []}