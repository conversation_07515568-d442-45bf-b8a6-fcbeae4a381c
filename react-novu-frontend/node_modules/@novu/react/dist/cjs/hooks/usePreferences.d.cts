import { Preference, NovuError } from '@novu/js';

type UsePreferencesProps = {
    filter?: {
        tags?: string[];
    };
    onSuccess?: (data: Preference[]) => void;
    onError?: (error: NovuError) => void;
};
type UsePreferencesResult = {
    preferences?: Preference[];
    error?: NovuError;
    isLoading: boolean;
    isFetching: boolean;
    refetch: () => Promise<void>;
};
declare const usePreferences: (props?: UsePreferencesProps) => UsePreferencesResult;

export { type UsePreferencesProps, type UsePreferencesResult, usePreferences };
