"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/index.ts
var hooks_exports = {};
__export(hooks_exports, {
  NovuProvider: () => import_NovuProvider.NovuProvider,
  useNovu: () => import_NovuProvider.useNovu
});
module.exports = __toCommonJS(hooks_exports);
__reExport(hooks_exports, require("./useNotifications.cjs"), module.exports);
__reExport(hooks_exports, require("./usePreferences.cjs"), module.exports);
__reExport(hooks_exports, require("./useCounts.cjs"), module.exports);
var import_NovuProvider = require("./NovuProvider.cjs");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  NovuProvider,
  useNovu,
  ...require("./useNotifications.cjs"),
  ...require("./usePreferences.cjs"),
  ...require("./useCounts.cjs")
});
//# sourceMappingURL=index.cjs.map