"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/useCounts.ts
var useCounts_exports = {};
__export(useCounts_exports, {
  useCounts: () => useCounts
});
module.exports = __toCommonJS(useCounts_exports);
var import_react = require("react");
var import_js = require("@novu/js");
var import_NovuProvider = require("./NovuProvider.cjs");
var import_useWebsocketEvent = require("./internal/useWebsocketEvent.cjs");
var useCounts = (props) => {
  const { filters, onSuccess, onError } = props;
  const { notifications } = (0, import_NovuProvider.useNovu)();
  const [error, setError] = (0, import_react.useState)();
  const [counts, setCounts] = (0, import_react.useState)();
  const [isLoading, setIsLoading] = (0, import_react.useState)(true);
  const [isFetching, setIsFetching] = (0, import_react.useState)(false);
  const sync = async (notification) => {
    const existingCounts = counts ?? filters.map((filter) => ({ count: 0, filter }));
    let countFiltersToFetch = [];
    if (notification) {
      for (let i = 0; i < existingCounts.length; i++) {
        const filter = filters[i];
        if ((0, import_js.areTagsEqual)(filter.tags, notification.tags)) {
          countFiltersToFetch.push(filter);
        }
      }
    } else {
      countFiltersToFetch = filters;
    }
    if (countFiltersToFetch.length === 0) {
      return;
    }
    setIsFetching(true);
    const countsRes = await notifications.count({ filters: countFiltersToFetch });
    setIsFetching(false);
    setIsLoading(false);
    if (countsRes.error) {
      setError(countsRes.error);
      onError == null ? void 0 : onError(countsRes.error);
      return;
    }
    const data = countsRes.data;
    onSuccess == null ? void 0 : onSuccess(data.counts);
    setCounts((oldCounts) => {
      const newCounts = [];
      const countsReceived = data.counts;
      for (let i = 0; i < existingCounts.length; i++) {
        const countReceived = countsReceived.find((c) => (0, import_js.areTagsEqual)(c.filter.tags, existingCounts[i].filter.tags));
        const count = countReceived || oldCounts && oldCounts[i];
        if (count) {
          newCounts.push(count);
        }
      }
      return newCounts;
    });
  };
  (0, import_useWebsocketEvent.useWebSocketEvent)({
    event: "notifications.notification_received",
    eventHandler: (data) => {
      sync(data.result);
    }
  });
  (0, import_useWebsocketEvent.useWebSocketEvent)({
    event: "notifications.unread_count_changed",
    eventHandler: () => {
      sync();
    }
  });
  (0, import_react.useEffect)(() => {
    setError(void 0);
    setIsLoading(true);
    setIsFetching(false);
    sync();
  }, [JSON.stringify(filters)]);
  const refetch = async () => {
    await sync();
  };
  return { counts, error, refetch, isLoading, isFetching };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useCounts
});
//# sourceMappingURL=useCounts.cjs.map