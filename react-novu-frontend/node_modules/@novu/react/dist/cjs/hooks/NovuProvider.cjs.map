{"version": 3, "sources": ["../../../src/hooks/NovuProvider.tsx"], "sourcesContent": ["import { Novu, NovuOptions, Subscriber } from '@novu/js';\nimport { ReactNode, createContext, useContext, useMemo, useEffect } from 'react';\n\n// @ts-ignore\nconst version = PACKAGE_VERSION;\n// @ts-ignore\nconst name = PACKAGE_NAME;\nconst baseUserAgent = `${name}@${version}`;\n\nexport type NovuProviderProps = NovuOptions & {\n  children: ReactNode;\n};\n\nconst NovuContext = createContext<Novu | undefined>(undefined);\n\nexport const NovuProvider = (props: NovuProviderProps) => {\n  const { subscriberId, ...propsWithoutSubscriberId } = props;\n  const subscriberObj = buildSubscriber(subscriberId, props.subscriber);\n  const applicationIdentifier = propsWithoutSubscriberId.applicationIdentifier\n    ? propsWithoutSubscriberId.applicationIdentifier\n    : '';\n\n  const providerProps: NovuProviderProps = {\n    ...propsWithoutSubscriberId,\n    applicationIdentifier,\n    subscriber: subscriberObj,\n  };\n\n  return (\n    <InternalNovuProvider {...providerProps} applicationIdentifier={applicationIdentifier} userAgentType=\"hooks\">\n      {props.children}\n    </InternalNovuProvider>\n  );\n};\n\n/**\n * @internal Should be used internally not to be exposed outside of the library\n * This is needed to differentiate between the hooks and components user agents\n * Better to use this internally to avoid confusion.\n */\nexport const InternalNovuProvider = (props: NovuProviderProps & { userAgentType: 'components' | 'hooks' }) => {\n  const applicationIdentifier = props.applicationIdentifier || '';\n  const subscriberObj = buildSubscriber(props.subscriberId, props.subscriber);\n\n  const { children, subscriberId, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType } = props;\n\n  const novu = useMemo(\n    () =>\n      new Novu({\n        applicationIdentifier,\n        subscriberHash,\n        backendUrl,\n        apiUrl,\n        socketUrl,\n        useCache,\n        __userAgent: `${baseUserAgent} ${userAgentType}`,\n        subscriber: subscriberObj,\n      }),\n    [applicationIdentifier, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType]\n  );\n\n  useEffect(() => {\n    novu.changeSubscriber({\n      subscriber: subscriberObj,\n      subscriberHash: props.subscriberHash,\n    });\n  }, [subscriberObj.subscriberId, props.subscriberHash, novu]);\n\n  return <NovuContext.Provider value={novu}>{children}</NovuContext.Provider>;\n};\n\nexport const useNovu = () => {\n  const context = useContext(NovuContext);\n  if (!context) {\n    throw new Error('useNovu must be used within a <NovuProvider />');\n  }\n\n  return context;\n};\n\nexport const useUnsafeNovu = () => {\n  const context = useContext(NovuContext);\n\n  return context;\n};\n\nfunction buildSubscriber(subscriberId: string | undefined, subscriber: Subscriber | string | undefined): Subscriber {\n  // subscriber object\n  if (subscriber) {\n    return typeof subscriber === 'string' ? { subscriberId: subscriber } : subscriber;\n  }\n\n  // subscriberId\n  if (subscriberId) {\n    return { subscriberId: subscriberId as string };\n  }\n\n  // missing - keyless subscriber, the api will generate a subscriberId\n  return { subscriberId: '' };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAA8C;AAC9C,mBAAyE;AA4BrE;AAzBJ,IAAM,UAAU;AAEhB,IAAM,OAAO;AACb,IAAM,gBAAgB,GAAG,IAAI,IAAI,OAAO;AAMxC,IAAM,kBAAc,4BAAgC,MAAS;AAEtD,IAAM,eAAe,CAAC,UAA6B;AACxD,QAAM,EAAE,cAAc,GAAG,yBAAyB,IAAI;AACtD,QAAM,gBAAgB,gBAAgB,cAAc,MAAM,UAAU;AACpE,QAAM,wBAAwB,yBAAyB,wBACnD,yBAAyB,wBACzB;AAEJ,QAAM,gBAAmC;AAAA,IACvC,GAAG;AAAA,IACH;AAAA,IACA,YAAY;AAAA,EACd;AAEA,SACE,4CAAC,wBAAsB,GAAG,eAAe,uBAA8C,eAAc,SAClG,gBAAM,UACT;AAEJ;AAOO,IAAM,uBAAuB,CAAC,UAAyE;AAC5G,QAAM,wBAAwB,MAAM,yBAAyB;AAC7D,QAAM,gBAAgB,gBAAgB,MAAM,cAAc,MAAM,UAAU;AAE1E,QAAM,EAAE,UAAU,cAAc,gBAAgB,YAAY,QAAQ,WAAW,UAAU,cAAc,IAAI;AAE3G,QAAM,WAAO;AAAA,IACX,MACE,IAAI,eAAK;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,GAAG,aAAa,IAAI,aAAa;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,IACH,CAAC,uBAAuB,gBAAgB,YAAY,QAAQ,WAAW,UAAU,aAAa;AAAA,EAChG;AAEA,8BAAU,MAAM;AACd,SAAK,iBAAiB;AAAA,MACpB,YAAY;AAAA,MACZ,gBAAgB,MAAM;AAAA,IACxB,CAAC;AAAA,EACH,GAAG,CAAC,cAAc,cAAc,MAAM,gBAAgB,IAAI,CAAC;AAE3D,SAAO,4CAAC,YAAY,UAAZ,EAAqB,OAAO,MAAO,UAAS;AACtD;AAEO,IAAM,UAAU,MAAM;AAC3B,QAAM,cAAU,yBAAW,WAAW;AACtC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,gDAAgD;AAAA,EAClE;AAEA,SAAO;AACT;AAEO,IAAM,gBAAgB,MAAM;AACjC,QAAM,cAAU,yBAAW,WAAW;AAEtC,SAAO;AACT;AAEA,SAAS,gBAAgB,cAAkC,YAAyD;AAElH,MAAI,YAAY;AACd,WAAO,OAAO,eAAe,WAAW,EAAE,cAAc,WAAW,IAAI;AAAA,EACzE;AAGA,MAAI,cAAc;AAChB,WAAO,EAAE,aAAqC;AAAA,EAChD;AAGA,SAAO,EAAE,cAAc,GAAG;AAC5B;", "names": []}