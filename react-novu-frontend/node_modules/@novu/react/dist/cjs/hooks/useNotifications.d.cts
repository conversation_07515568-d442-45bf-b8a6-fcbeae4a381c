import { Notification, NovuError } from '@novu/js';

type UseNotificationsProps = {
    tags?: string[];
    data?: Record<string, unknown>;
    read?: boolean;
    archived?: boolean;
    snoozed?: boolean;
    limit?: number;
    onSuccess?: (data: Notification[]) => void;
    onError?: (error: NovuError) => void;
};
type UseNotificationsResult = {
    notifications?: Notification[];
    error?: NovuError;
    isLoading: boolean;
    isFetching: boolean;
    hasMore: boolean;
    readAll: () => Promise<{
        data?: void | undefined;
        error?: NovuError | undefined;
    }>;
    archiveAll: () => Promise<{
        data?: void | undefined;
        error?: NovuError | undefined;
    }>;
    archiveAllRead: () => Promise<{
        data?: void | undefined;
        error?: NovuError | undefined;
    }>;
    refetch: () => Promise<void>;
    fetchMore: () => Promise<void>;
};
declare const useNotifications: (props?: UseNotificationsProps) => UseNotificationsResult;

export { type UseNotificationsProps, type UseNotificationsResult, useNotifications };
