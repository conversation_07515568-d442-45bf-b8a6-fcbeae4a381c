"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/useNotifications.ts
var useNotifications_exports = {};
__export(useNotifications_exports, {
  useNotifications: () => useNotifications
});
module.exports = __toCommonJS(useNotifications_exports);
var import_react = require("react");
var import_js = require("@novu/js");
var import_NovuProvider = require("./NovuProvider.cjs");
var useNotifications = (props) => {
  const { tags, data: dataFilter, read, archived = false, snoozed = false, limit, onSuccess, onError } = props || {};
  const filterRef = (0, import_react.useRef)(void 0);
  const { notifications, on } = (0, import_NovuProvider.useNovu)();
  const [data, setData] = (0, import_react.useState)();
  const [error, setError] = (0, import_react.useState)();
  const [isLoading, setIsLoading] = (0, import_react.useState)(true);
  const [isFetching, setIsFetching] = (0, import_react.useState)(false);
  const [hasMore, setHasMore] = (0, import_react.useState)(false);
  const length = data == null ? void 0 : data.length;
  const after = length ? data[length - 1].id : void 0;
  const sync = (event) => {
    if (!event.data || filterRef.current && !(0, import_js.isSameFilter)(filterRef.current, event.data.filter)) {
      return;
    }
    setData(event.data.notifications);
    setHasMore(event.data.hasMore);
  };
  (0, import_react.useEffect)(() => {
    const cleanup = on("notifications.list.updated", sync);
    return () => {
      cleanup();
    };
  }, []);
  (0, import_react.useEffect)(() => {
    const newFilter = { tags, data: dataFilter, read, archived, snoozed };
    if (filterRef.current && (0, import_js.isSameFilter)(filterRef.current, newFilter)) {
      return;
    }
    notifications.clearCache({ filter: filterRef.current });
    filterRef.current = newFilter;
    fetchNotifications({ refetch: true });
  }, [tags, dataFilter, read, archived, snoozed]);
  const fetchNotifications = async (options) => {
    if (options == null ? void 0 : options.refetch) {
      setError(void 0);
      setIsLoading(true);
      setIsFetching(false);
    }
    setIsFetching(true);
    const response = await notifications.list({
      tags,
      data: dataFilter,
      read,
      archived,
      snoozed,
      limit,
      after: (options == null ? void 0 : options.refetch) ? void 0 : after
    });
    if (response.error) {
      setError(response.error);
      onError == null ? void 0 : onError(response.error);
    } else {
      onSuccess == null ? void 0 : onSuccess(response.data.notifications);
      setData(response.data.notifications);
      setHasMore(response.data.hasMore);
    }
    setIsLoading(false);
    setIsFetching(false);
  };
  const refetch = () => {
    notifications.clearCache({ filter: { tags, read, archived, snoozed, data: dataFilter } });
    return fetchNotifications({ refetch: true });
  };
  const fetchMore = async () => {
    if (!hasMore || isFetching) return;
    return fetchNotifications();
  };
  const readAll = async () => {
    return await notifications.readAll({ tags, data: dataFilter });
  };
  const archiveAll = async () => {
    return await notifications.archiveAll({ tags, data: dataFilter });
  };
  const archiveAllRead = async () => {
    return await notifications.archiveAllRead({ tags, data: dataFilter });
  };
  return {
    readAll,
    archiveAll,
    archiveAllRead,
    notifications: data,
    error,
    isLoading,
    isFetching,
    refetch,
    fetchMore,
    hasMore
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useNotifications
});
//# sourceMappingURL=useNotifications.cjs.map