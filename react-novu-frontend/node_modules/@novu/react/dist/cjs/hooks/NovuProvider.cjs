"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/NovuProvider.tsx
var NovuProvider_exports = {};
__export(NovuProvider_exports, {
  InternalNovuProvider: () => InternalNovuProvider,
  NovuProvider: () => NovuProvider,
  useNovu: () => useNovu,
  useUnsafeNovu: () => useUnsafeNovu
});
module.exports = __toCommonJS(NovuProvider_exports);
var import_js = require("@novu/js");
var import_react = require("react");
var import_jsx_runtime = require("react/jsx-runtime");
var version = "3.6.0";
var name = "@novu/react";
var baseUserAgent = `${name}@${version}`;
var NovuContext = (0, import_react.createContext)(void 0);
var NovuProvider = (props) => {
  const { subscriberId, ...propsWithoutSubscriberId } = props;
  const subscriberObj = buildSubscriber(subscriberId, props.subscriber);
  const applicationIdentifier = propsWithoutSubscriberId.applicationIdentifier ? propsWithoutSubscriberId.applicationIdentifier : "";
  const providerProps = {
    ...propsWithoutSubscriberId,
    applicationIdentifier,
    subscriber: subscriberObj
  };
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(InternalNovuProvider, { ...providerProps, applicationIdentifier, userAgentType: "hooks", children: props.children });
};
var InternalNovuProvider = (props) => {
  const applicationIdentifier = props.applicationIdentifier || "";
  const subscriberObj = buildSubscriber(props.subscriberId, props.subscriber);
  const { children, subscriberId, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType } = props;
  const novu = (0, import_react.useMemo)(
    () => new import_js.Novu({
      applicationIdentifier,
      subscriberHash,
      backendUrl,
      apiUrl,
      socketUrl,
      useCache,
      __userAgent: `${baseUserAgent} ${userAgentType}`,
      subscriber: subscriberObj
    }),
    [applicationIdentifier, subscriberHash, backendUrl, apiUrl, socketUrl, useCache, userAgentType]
  );
  (0, import_react.useEffect)(() => {
    novu.changeSubscriber({
      subscriber: subscriberObj,
      subscriberHash: props.subscriberHash
    });
  }, [subscriberObj.subscriberId, props.subscriberHash, novu]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(NovuContext.Provider, { value: novu, children });
};
var useNovu = () => {
  const context = (0, import_react.useContext)(NovuContext);
  if (!context) {
    throw new Error("useNovu must be used within a <NovuProvider />");
  }
  return context;
};
var useUnsafeNovu = () => {
  const context = (0, import_react.useContext)(NovuContext);
  return context;
};
function buildSubscriber(subscriberId, subscriber) {
  if (subscriber) {
    return typeof subscriber === "string" ? { subscriberId: subscriber } : subscriber;
  }
  if (subscriberId) {
    return { subscriberId };
  }
  return { subscriberId: "" };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  InternalNovuProvider,
  NovuProvider,
  useNovu,
  useUnsafeNovu
});
//# sourceMappingURL=NovuProvider.cjs.map