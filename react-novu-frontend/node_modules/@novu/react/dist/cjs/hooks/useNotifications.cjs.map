{"version": 3, "sources": ["../../../src/hooks/useNotifications.ts"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport { ListNotificationsResponse, Notification, NovuError, isSameFilter, NotificationFilter } from '@novu/js';\nimport { useNovu } from './NovuProvider';\n\nexport type UseNotificationsProps = {\n  tags?: string[];\n  data?: Record<string, unknown>;\n  read?: boolean;\n  archived?: boolean;\n  snoozed?: boolean;\n  limit?: number;\n  onSuccess?: (data: Notification[]) => void;\n  onError?: (error: NovuError) => void;\n};\n\nexport type UseNotificationsResult = {\n  notifications?: Notification[];\n  error?: NovuError;\n  isLoading: boolean;\n  isFetching: boolean;\n  hasMore: boolean;\n  readAll: () => Promise<{\n    data?: void | undefined;\n    error?: NovuError | undefined;\n  }>;\n  archiveAll: () => Promise<{\n    data?: void | undefined;\n    error?: NovuError | undefined;\n  }>;\n  archiveAllRead: () => Promise<{\n    data?: void | undefined;\n    error?: NovuError | undefined;\n  }>;\n  refetch: () => Promise<void>;\n  fetchMore: () => Promise<void>;\n};\n\nexport const useNotifications = (props?: UseNotificationsProps): UseNotificationsResult => {\n  const { tags, data: dataFilter, read, archived = false, snoozed = false, limit, onSuccess, onError } = props || {};\n  const filterRef = useRef<NotificationFilter | undefined>(undefined);\n  const { notifications, on } = useNovu();\n  const [data, setData] = useState<Array<Notification>>();\n  const [error, setError] = useState<NovuError>();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isFetching, setIsFetching] = useState(false);\n  const [hasMore, setHasMore] = useState(false);\n  const length = data?.length;\n  const after = length ? data[length - 1].id : undefined;\n\n  const sync = (event: { data?: ListNotificationsResponse }) => {\n    if (!event.data || (filterRef.current && !isSameFilter(filterRef.current, event.data.filter))) {\n      return;\n    }\n    setData(event.data.notifications);\n    setHasMore(event.data.hasMore);\n  };\n\n  useEffect(() => {\n    const cleanup = on('notifications.list.updated', sync);\n\n    return () => {\n      cleanup();\n    };\n  }, []);\n\n  useEffect(() => {\n    const newFilter = { tags, data: dataFilter, read, archived, snoozed };\n    if (filterRef.current && isSameFilter(filterRef.current, newFilter)) {\n      return;\n    }\n    notifications.clearCache({ filter: filterRef.current });\n    filterRef.current = newFilter;\n\n    fetchNotifications({ refetch: true });\n  }, [tags, dataFilter, read, archived, snoozed]);\n\n  const fetchNotifications = async (options?: { refetch: boolean }) => {\n    if (options?.refetch) {\n      setError(undefined);\n      setIsLoading(true);\n      setIsFetching(false);\n    }\n    setIsFetching(true);\n    const response = await notifications.list({\n      tags,\n      data: dataFilter,\n      read,\n      archived,\n      snoozed,\n      limit,\n      after: options?.refetch ? undefined : after,\n    });\n    if (response.error) {\n      setError(response.error);\n      onError?.(response.error);\n    } else {\n      onSuccess?.(response.data!.notifications);\n      setData(response.data!.notifications);\n      setHasMore(response.data!.hasMore);\n    }\n    setIsLoading(false);\n    setIsFetching(false);\n  };\n\n  const refetch = () => {\n    notifications.clearCache({ filter: { tags, read, archived, snoozed, data: dataFilter } });\n\n    return fetchNotifications({ refetch: true });\n  };\n\n  const fetchMore = async () => {\n    if (!hasMore || isFetching) return;\n\n    return fetchNotifications();\n  };\n\n  const readAll = async () => {\n    return await notifications.readAll({ tags, data: dataFilter });\n  };\n\n  const archiveAll = async () => {\n    return await notifications.archiveAll({ tags, data: dataFilter });\n  };\n\n  const archiveAllRead = async () => {\n    return await notifications.archiveAllRead({ tags, data: dataFilter });\n  };\n\n  return {\n    readAll,\n    archiveAll,\n    archiveAllRead,\n    notifications: data,\n    error,\n    isLoading,\n    isFetching,\n    refetch,\n    fetchMore,\n    hasMore,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAA4C;AAC5C,gBAAqG;AACrG,0BAAwB;AAmCjB,IAAM,mBAAmB,CAAC,UAA0D;AACzF,QAAM,EAAE,MAAM,MAAM,YAAY,MAAM,WAAW,OAAO,UAAU,OAAO,OAAO,WAAW,QAAQ,IAAI,SAAS,CAAC;AACjH,QAAM,gBAAY,qBAAuC,MAAS;AAClE,QAAM,EAAE,eAAe,GAAG,QAAI,6BAAQ;AACtC,QAAM,CAAC,MAAM,OAAO,QAAI,uBAA8B;AACtD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAoB;AAC9C,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,IAAI;AAC/C,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAClD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,SAAS,6BAAM;AACrB,QAAM,QAAQ,SAAS,KAAK,SAAS,CAAC,EAAE,KAAK;AAE7C,QAAM,OAAO,CAAC,UAAgD;AAC5D,QAAI,CAAC,MAAM,QAAS,UAAU,WAAW,KAAC,wBAAa,UAAU,SAAS,MAAM,KAAK,MAAM,GAAI;AAC7F;AAAA,IACF;AACA,YAAQ,MAAM,KAAK,aAAa;AAChC,eAAW,MAAM,KAAK,OAAO;AAAA,EAC/B;AAEA,8BAAU,MAAM;AACd,UAAM,UAAU,GAAG,8BAA8B,IAAI;AAErD,WAAO,MAAM;AACX,cAAQ;AAAA,IACV;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,8BAAU,MAAM;AACd,UAAM,YAAY,EAAE,MAAM,MAAM,YAAY,MAAM,UAAU,QAAQ;AACpE,QAAI,UAAU,eAAW,wBAAa,UAAU,SAAS,SAAS,GAAG;AACnE;AAAA,IACF;AACA,kBAAc,WAAW,EAAE,QAAQ,UAAU,QAAQ,CAAC;AACtD,cAAU,UAAU;AAEpB,uBAAmB,EAAE,SAAS,KAAK,CAAC;AAAA,EACtC,GAAG,CAAC,MAAM,YAAY,MAAM,UAAU,OAAO,CAAC;AAE9C,QAAM,qBAAqB,OAAO,YAAmC;AACnE,QAAI,mCAAS,SAAS;AACpB,eAAS,MAAS;AAClB,mBAAa,IAAI;AACjB,oBAAc,KAAK;AAAA,IACrB;AACA,kBAAc,IAAI;AAClB,UAAM,WAAW,MAAM,cAAc,KAAK;AAAA,MACxC;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAO,mCAAS,WAAU,SAAY;AAAA,IACxC,CAAC;AACD,QAAI,SAAS,OAAO;AAClB,eAAS,SAAS,KAAK;AACvB,yCAAU,SAAS;AAAA,IACrB,OAAO;AACL,6CAAY,SAAS,KAAM;AAC3B,cAAQ,SAAS,KAAM,aAAa;AACpC,iBAAW,SAAS,KAAM,OAAO;AAAA,IACnC;AACA,iBAAa,KAAK;AAClB,kBAAc,KAAK;AAAA,EACrB;AAEA,QAAM,UAAU,MAAM;AACpB,kBAAc,WAAW,EAAE,QAAQ,EAAE,MAAM,MAAM,UAAU,SAAS,MAAM,WAAW,EAAE,CAAC;AAExF,WAAO,mBAAmB,EAAE,SAAS,KAAK,CAAC;AAAA,EAC7C;AAEA,QAAM,YAAY,YAAY;AAC5B,QAAI,CAAC,WAAW,WAAY;AAE5B,WAAO,mBAAmB;AAAA,EAC5B;AAEA,QAAM,UAAU,YAAY;AAC1B,WAAO,MAAM,cAAc,QAAQ,EAAE,MAAM,MAAM,WAAW,CAAC;AAAA,EAC/D;AAEA,QAAM,aAAa,YAAY;AAC7B,WAAO,MAAM,cAAc,WAAW,EAAE,MAAM,MAAM,WAAW,CAAC;AAAA,EAClE;AAEA,QAAM,iBAAiB,YAAY;AACjC,WAAO,MAAM,cAAc,eAAe,EAAE,MAAM,MAAM,WAAW,CAAC;AAAA,EACtE;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}