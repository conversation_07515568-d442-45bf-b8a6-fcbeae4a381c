"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/usePreferences.ts
var usePreferences_exports = {};
__export(usePreferences_exports, {
  usePreferences: () => usePreferences
});
module.exports = __toCommonJS(usePreferences_exports);
var import_react = require("react");
var import_NovuProvider = require("./NovuProvider.cjs");
var usePreferences = (props) => {
  const { onSuccess, onError } = props || {};
  const [data, setData] = (0, import_react.useState)();
  const { preferences, on } = (0, import_NovuProvider.useNovu)();
  const [error, setError] = (0, import_react.useState)();
  const [isLoading, setIsLoading] = (0, import_react.useState)(true);
  const [isFetching, setIsFetching] = (0, import_react.useState)(false);
  const sync = (event) => {
    if (!event.data) {
      return;
    }
    setData(event.data);
  };
  (0, import_react.useEffect)(() => {
    fetchPreferences();
    const listUpdatedCleanup = on("preferences.list.updated", sync);
    const listPendingCleanup = on("preferences.list.pending", sync);
    const listResolvedCleanup = on("preferences.list.resolved", sync);
    return () => {
      listUpdatedCleanup();
      listPendingCleanup();
      listResolvedCleanup();
    };
  }, []);
  const fetchPreferences = async () => {
    setIsFetching(true);
    const response = await preferences.list(props == null ? void 0 : props.filter);
    if (response.error) {
      setError(response.error);
      onError == null ? void 0 : onError(response.error);
    } else {
      onSuccess == null ? void 0 : onSuccess(response.data);
    }
    setIsLoading(false);
    setIsFetching(false);
  };
  const refetch = () => {
    preferences.cache.clearAll();
    return fetchPreferences();
  };
  return {
    preferences: data,
    error,
    isLoading,
    isFetching,
    refetch
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  usePreferences
});
//# sourceMappingURL=usePreferences.cjs.map