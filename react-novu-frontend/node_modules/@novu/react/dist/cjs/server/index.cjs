"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/server/index.tsx
var server_exports = {};
__export(server_exports, {
  Bell: () => Bell,
  Inbox: () => Inbox,
  InboxContent: () => InboxContent,
  Notifications: () => Notifications,
  NovuProvider: () => NovuProvider,
  Preferences: () => Preferences,
  useCounts: () => useCounts,
  useNotifications: () => useNotifications,
  useNovu: () => useNovu,
  usePreferences: () => usePreferences
});
module.exports = __toCommonJS(server_exports);
var import_ShadowRootDetector = require("../components/ShadowRootDetector.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
function Inbox(props) {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_ShadowRootDetector.ShadowRootDetector, {});
}
function InboxContent() {
}
function Notifications() {
}
function Preferences() {
}
function Bell() {
}
function NovuProvider(props) {
}
function useNovu() {
  return null;
}
function useCounts(_) {
  return {
    isLoading: false,
    isFetching: false,
    refetch: () => Promise.resolve()
  };
}
function useNotifications(_) {
  return {
    isLoading: false,
    isFetching: false,
    hasMore: false,
    readAll: () => Promise.resolve({ data: void 0, error: void 0 }),
    archiveAll: () => Promise.resolve({ data: void 0, error: void 0 }),
    archiveAllRead: () => Promise.resolve({ data: void 0, error: void 0 }),
    refetch: () => Promise.resolve(),
    fetchMore: () => Promise.resolve()
  };
}
function usePreferences(_) {
  return {
    isLoading: false,
    isFetching: false,
    refetch: () => Promise.resolve()
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Bell,
  Inbox,
  InboxContent,
  Notifications,
  NovuProvider,
  Preferences,
  useCounts,
  useNotifications,
  useNovu,
  usePreferences
});
//# sourceMappingURL=index.cjs.map