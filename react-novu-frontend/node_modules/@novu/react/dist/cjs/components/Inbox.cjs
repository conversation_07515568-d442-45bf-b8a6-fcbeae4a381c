"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/Inbox.tsx
var Inbox_exports = {};
__export(Inbox_exports, {
  Inbox: () => Inbox
});
module.exports = __toCommonJS(Inbox_exports);
var import_react = __toESM(require("react"), 1);
var import_Mounter = require("./Mounter.cjs");
var import_NovuUIContext = require("../context/NovuUIContext.cjs");
var import_RendererContext = require("../context/RendererContext.cjs");
var import_NovuProvider = require("../hooks/NovuProvider.cjs");
var import_NovuUI = require("./NovuUI.cjs");
var import_Renderer = require("./Renderer.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var DefaultInbox = (props) => {
  const {
    open,
    renderNotification,
    renderSubject,
    renderBody,
    renderBell,
    onNotificationClick,
    onPrimaryActionClick,
    onSecondaryActionClick,
    placement,
    placementOffset
  } = props;
  const { novuUI } = (0, import_NovuUIContext.useNovuUI)();
  const { mountElement } = (0, import_RendererContext.useRenderer)();
  const mount = import_react.default.useCallback(
    (element) => {
      if (renderNotification) {
        return novuUI.mountComponent({
          name: "Inbox",
          props: {
            open,
            renderNotification: renderNotification ? (el, notification) => mountElement(el, renderNotification(notification)) : void 0,
            renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : void 0,
            onNotificationClick,
            onPrimaryActionClick,
            onSecondaryActionClick,
            placementOffset,
            placement
          },
          element
        });
      }
      return novuUI.mountComponent({
        name: "Inbox",
        props: {
          open,
          renderSubject: renderSubject ? (el, notification) => mountElement(el, renderSubject(notification)) : void 0,
          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : void 0,
          renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : void 0,
          onNotificationClick,
          onPrimaryActionClick,
          onSecondaryActionClick,
          placementOffset,
          placement
        },
        element
      });
    },
    [
      open,
      renderNotification,
      renderSubject,
      renderBody,
      renderBell,
      onNotificationClick,
      onPrimaryActionClick,
      onSecondaryActionClick
    ]
  );
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_Mounter.Mounter, { mount });
};
var Inbox = import_react.default.memo((props) => {
  const { subscriberId, ...propsWithoutSubscriberId } = props;
  const subscriber = buildSubscriber(props.subscriber, props.subscriberId);
  const applicationIdentifier = props.applicationIdentifier ? props.applicationIdentifier : "";
  const novu = (0, import_NovuProvider.useUnsafeNovu)();
  if (novu) {
    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(InboxChild, { ...propsWithoutSubscriberId, applicationIdentifier, subscriber });
  }
  const providerProps = {
    applicationIdentifier,
    subscriberHash: props.subscriberHash,
    backendUrl: props.backendUrl,
    socketUrl: props.socketUrl,
    subscriber
  };
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_NovuProvider.InternalNovuProvider, { ...providerProps, userAgentType: "components", children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(InboxChild, { ...propsWithoutSubscriberId, applicationIdentifier, subscriber }) });
});
var InboxChild = (0, import_Renderer.withRenderer)(
  import_react.default.memo((props) => {
    const {
      localization,
      appearance,
      tabs,
      preferencesFilter,
      preferenceGroups,
      routerPush,
      applicationIdentifier = "",
      // for keyless we provide an empty string, the api will generate a identifier
      subscriberId,
      subscriberHash,
      backendUrl,
      socketUrl,
      subscriber
    } = props;
    const novu = (0, import_NovuProvider.useNovu)();
    const options = (0, import_react.useMemo)(() => {
      return {
        localization,
        appearance,
        tabs,
        preferencesFilter,
        preferenceGroups,
        routerPush,
        options: {
          applicationIdentifier,
          subscriberHash,
          backendUrl,
          socketUrl,
          subscriber: buildSubscriber(subscriber, subscriberId)
        }
      };
    }, [
      localization,
      appearance,
      tabs,
      preferencesFilter,
      preferenceGroups,
      applicationIdentifier,
      subscriberId,
      subscriberHash,
      backendUrl,
      socketUrl,
      subscriber
    ]);
    if (isWithChildrenProps(props)) {
      return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_NovuUI.NovuUI, { options, novu, children: props.children });
    }
    const {
      open,
      renderNotification,
      renderSubject,
      renderBody,
      renderBell,
      onNotificationClick,
      onPrimaryActionClick,
      onSecondaryActionClick,
      placementOffset,
      placement
    } = props;
    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_NovuUI.NovuUI, { options, novu, children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      DefaultInbox,
      {
        open,
        renderNotification,
        renderSubject,
        renderBody,
        renderBell,
        onNotificationClick,
        onPrimaryActionClick,
        onSecondaryActionClick,
        placement,
        placementOffset
      }
    ) });
  })
);
function isWithChildrenProps(props) {
  return "children" in props;
}
function buildSubscriber(subscriber, subscriberId) {
  if (subscriber) {
    return typeof subscriber === "string" ? { subscriberId: subscriber } : subscriber;
  }
  if (subscriberId) {
    return { subscriberId };
  }
  return { subscriberId: "" };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Inbox
});
//# sourceMappingURL=Inbox.cjs.map