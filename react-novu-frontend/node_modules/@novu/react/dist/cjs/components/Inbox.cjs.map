{"version": 3, "sources": ["../../../src/components/Inbox.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { Subscriber, StandardNovuOptions } from '@novu/js';\nimport { DefaultProps, DefaultInboxProps, WithChildrenProps } from '../utils/types';\nimport { Mounter } from './Mounter';\nimport { useNovuUI } from '../context/NovuUIContext';\nimport { useRenderer } from '../context/RendererContext';\nimport { InternalNovuProvider, useNovu, useUnsafeNovu } from '../hooks/NovuProvider';\nimport { NovuUI } from './NovuUI';\nimport { withRenderer } from './Renderer';\n\nexport type InboxProps = DefaultProps | WithChildrenProps;\n\nconst DefaultInbox = (props: DefaultInboxProps) => {\n  const {\n    open,\n    renderNotification,\n    renderSubject,\n    renderBody,\n    renderBell,\n    onNotificationClick,\n    onPrimaryActionClick,\n    onSecondaryActionClick,\n    placement,\n    placementOffset,\n  } = props;\n  const { novuUI } = useNovuUI();\n  const { mountElement } = useRenderer();\n\n  const mount = React.useCallback(\n    (element: HTMLElement) => {\n      if (renderNotification) {\n        return novuUI.mountComponent({\n          name: 'Inbox',\n          props: {\n            open,\n            renderNotification: renderNotification\n              ? (el, notification) => mountElement(el, renderNotification(notification))\n              : undefined,\n            renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : undefined,\n            onNotificationClick,\n            onPrimaryActionClick,\n            onSecondaryActionClick,\n            placementOffset,\n            placement,\n          },\n          element,\n        });\n      }\n\n      return novuUI.mountComponent({\n        name: 'Inbox',\n        props: {\n          open,\n          renderSubject: renderSubject\n            ? (el, notification) => mountElement(el, renderSubject(notification))\n            : undefined,\n          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : undefined,\n          renderBell: renderBell ? (el, unreadCount) => mountElement(el, renderBell(unreadCount)) : undefined,\n          onNotificationClick,\n          onPrimaryActionClick,\n          onSecondaryActionClick,\n          placementOffset,\n          placement,\n        },\n        element,\n      });\n    },\n    [\n      open,\n      renderNotification,\n      renderSubject,\n      renderBody,\n      renderBell,\n      onNotificationClick,\n      onPrimaryActionClick,\n      onSecondaryActionClick,\n    ]\n  );\n\n  return <Mounter mount={mount} />;\n};\n\nexport const Inbox = React.memo((props: InboxProps) => {\n  const { subscriberId, ...propsWithoutSubscriberId } = props;\n  const subscriber = buildSubscriber(props.subscriber, props.subscriberId);\n  const applicationIdentifier = props.applicationIdentifier ? props.applicationIdentifier : ''; // for keyless we provide an empty string, the api will generate a identifier\n  const novu = useUnsafeNovu();\n\n  if (novu) {\n    return (\n      <InboxChild {...propsWithoutSubscriberId} applicationIdentifier={applicationIdentifier} subscriber={subscriber} />\n    );\n  }\n\n  const providerProps = {\n    applicationIdentifier,\n    subscriberHash: props.subscriberHash,\n    backendUrl: props.backendUrl,\n    socketUrl: props.socketUrl,\n    subscriber,\n  } satisfies StandardNovuOptions;\n\n  return (\n    <InternalNovuProvider {...providerProps} userAgentType=\"components\">\n      <InboxChild {...propsWithoutSubscriberId} applicationIdentifier={applicationIdentifier} subscriber={subscriber} />\n    </InternalNovuProvider>\n  );\n});\n\nconst InboxChild = withRenderer(\n  React.memo((props: InboxProps) => {\n    const {\n      localization,\n      appearance,\n      tabs,\n      preferencesFilter,\n      preferenceGroups,\n      routerPush,\n      applicationIdentifier = '', // for keyless we provide an empty string, the api will generate a identifier\n      subscriberId,\n      subscriberHash,\n      backendUrl,\n      socketUrl,\n      subscriber,\n    } = props;\n    const novu = useNovu();\n\n    const options = useMemo(() => {\n      return {\n        localization,\n        appearance,\n        tabs,\n        preferencesFilter,\n        preferenceGroups,\n        routerPush,\n        options: {\n          applicationIdentifier,\n          subscriberHash,\n          backendUrl,\n          socketUrl,\n          subscriber: buildSubscriber(subscriber, subscriberId),\n        },\n      };\n    }, [\n      localization,\n      appearance,\n      tabs,\n      preferencesFilter,\n      preferenceGroups,\n      applicationIdentifier,\n      subscriberId,\n      subscriberHash,\n      backendUrl,\n      socketUrl,\n      subscriber,\n    ]);\n\n    if (isWithChildrenProps(props)) {\n      return (\n        <NovuUI options={options} novu={novu}>\n          {props.children}\n        </NovuUI>\n      );\n    }\n\n    const {\n      open,\n      renderNotification,\n      renderSubject,\n      renderBody,\n      renderBell,\n      onNotificationClick,\n      onPrimaryActionClick,\n      onSecondaryActionClick,\n      placementOffset,\n      placement,\n    } = props;\n\n    return (\n      <NovuUI options={options} novu={novu}>\n        <DefaultInbox\n          open={open}\n          renderNotification={renderNotification}\n          renderSubject={renderSubject}\n          renderBody={renderBody}\n          renderBell={renderBell}\n          onNotificationClick={onNotificationClick}\n          onPrimaryActionClick={onPrimaryActionClick}\n          onSecondaryActionClick={onSecondaryActionClick}\n          placement={placement}\n          placementOffset={placementOffset}\n        />\n      </NovuUI>\n    );\n  })\n);\n\nfunction isWithChildrenProps(props: InboxProps): props is WithChildrenProps {\n  return 'children' in props;\n}\n\nfunction buildSubscriber(subscriber?: string | Subscriber | undefined, subscriberId?: string): Subscriber {\n  // subscriber object\n  if (subscriber) {\n    return typeof subscriber === 'string' ? { subscriberId: subscriber } : subscriber;\n  }\n\n  // subscriberId\n  if (subscriberId) {\n    return { subscriberId };\n  }\n\n  // missing - keyless subscriber, the api will generate a subscriberId\n  return { subscriberId: '' };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAA+B;AAG/B,qBAAwB;AACxB,2BAA0B;AAC1B,6BAA4B;AAC5B,0BAA6D;AAC7D,oBAAuB;AACvB,sBAA6B;AAuEpB;AAnET,IAAM,eAAe,CAAC,UAA6B;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,EAAE,OAAO,QAAI,gCAAU;AAC7B,QAAM,EAAE,aAAa,QAAI,oCAAY;AAErC,QAAM,QAAQ,aAAAA,QAAM;AAAA,IAClB,CAAC,YAAyB;AACxB,UAAI,oBAAoB;AACtB,eAAO,OAAO,eAAe;AAAA,UAC3B,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,YACA,oBAAoB,qBAChB,CAAC,IAAI,iBAAiB,aAAa,IAAI,mBAAmB,YAAY,CAAC,IACvE;AAAA,YACJ,YAAY,aAAa,CAAC,IAAI,gBAAgB,aAAa,IAAI,WAAW,WAAW,CAAC,IAAI;AAAA,YAC1F;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,OAAO,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN,OAAO;AAAA,UACL;AAAA,UACA,eAAe,gBACX,CAAC,IAAI,iBAAiB,aAAa,IAAI,cAAc,YAAY,CAAC,IAClE;AAAA,UACJ,YAAY,aAAa,CAAC,IAAI,iBAAiB,aAAa,IAAI,WAAW,YAAY,CAAC,IAAI;AAAA,UAC5F,YAAY,aAAa,CAAC,IAAI,gBAAgB,aAAa,IAAI,WAAW,WAAW,CAAC,IAAI;AAAA,UAC1F;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,4CAAC,0BAAQ,OAAc;AAChC;AAEO,IAAM,QAAQ,aAAAA,QAAM,KAAK,CAAC,UAAsB;AACrD,QAAM,EAAE,cAAc,GAAG,yBAAyB,IAAI;AACtD,QAAM,aAAa,gBAAgB,MAAM,YAAY,MAAM,YAAY;AACvE,QAAM,wBAAwB,MAAM,wBAAwB,MAAM,wBAAwB;AAC1F,QAAM,WAAO,mCAAc;AAE3B,MAAI,MAAM;AACR,WACE,4CAAC,cAAY,GAAG,0BAA0B,uBAA8C,YAAwB;AAAA,EAEpH;AAEA,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,gBAAgB,MAAM;AAAA,IACtB,YAAY,MAAM;AAAA,IAClB,WAAW,MAAM;AAAA,IACjB;AAAA,EACF;AAEA,SACE,4CAAC,4CAAsB,GAAG,eAAe,eAAc,cACrD,sDAAC,cAAY,GAAG,0BAA0B,uBAA8C,YAAwB,GAClH;AAEJ,CAAC;AAED,IAAM,iBAAa;AAAA,EACjB,aAAAA,QAAM,KAAK,CAAC,UAAsB;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,wBAAwB;AAAA;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAO,6BAAQ;AAErB,UAAM,cAAU,sBAAQ,MAAM;AAC5B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,gBAAgB,YAAY,YAAY;AAAA,QACtD;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,oBAAoB,KAAK,GAAG;AAC9B,aACE,4CAAC,wBAAO,SAAkB,MACvB,gBAAM,UACT;AAAA,IAEJ;AAEA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,WACE,4CAAC,wBAAO,SAAkB,MACxB;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,IACF,GACF;AAAA,EAEJ,CAAC;AACH;AAEA,SAAS,oBAAoB,OAA+C;AAC1E,SAAO,cAAc;AACvB;AAEA,SAAS,gBAAgB,YAA8C,cAAmC;AAExG,MAAI,YAAY;AACd,WAAO,OAAO,eAAe,WAAW,EAAE,cAAc,WAAW,IAAI;AAAA,EACzE;AAGA,MAAI,cAAc;AAChB,WAAO,EAAE,aAAa;AAAA,EACxB;AAGA,SAAO,EAAE,cAAc,GAAG;AAC5B;", "names": ["React"]}