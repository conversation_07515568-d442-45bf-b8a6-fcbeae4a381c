"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/Bell.tsx
var Bell_exports = {};
__export(Bell_exports, {
  Bell: () => Bell
});
module.exports = __toCommonJS(Bell_exports);
var import_react = __toESM(require("react"), 1);
var import_Mounter = require("./Mounter.cjs");
var import_Renderer = require("./Renderer.cjs");
var import_NovuUIContext = require("../context/NovuUIContext.cjs");
var import_RendererContext = require("../context/RendererContext.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var _Bell = import_react.default.memo((props) => {
  const { renderBell } = props;
  const { novuUI } = (0, import_NovuUIContext.useNovuUI)();
  const { mountElement } = (0, import_RendererContext.useRenderer)();
  const mount = import_react.default.useCallback(
    (element) => {
      return novuUI.mountComponent({
        name: "Bell",
        element,
        props: renderBell ? { renderBell: (el, unreadCount) => mountElement(el, renderBell(unreadCount)) } : void 0
      });
    },
    [renderBell]
  );
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_Mounter.Mounter, { mount });
});
var Bell = (0, import_Renderer.withRenderer)(_Bell);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Bell
});
//# sourceMappingURL=Bell.cjs.map