{"version": 3, "sources": ["../../../src/components/Preferences.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mounter } from './Mounter';\nimport { useNovuUI } from '../context/NovuUIContext';\n\nexport const Preferences = () => {\n  const { novuUI } = useNovuUI();\n\n  const mount = React.useCallback((element: HTMLElement) => {\n    return novuUI.mountComponent({\n      name: 'Preferences',\n      element,\n    });\n  }, []);\n\n  return <Mounter mount={mount} />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBAAwB;AACxB,2BAA0B;AAYjB;AAVF,IAAM,cAAc,MAAM;AAC/B,QAAM,EAAE,OAAO,QAAI,gCAAU;AAE7B,QAAM,QAAQ,aAAAA,QAAM,YAAY,CAAC,YAAyB;AACxD,WAAO,OAAO,eAAe;AAAA,MAC3B,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,SAAO,4CAAC,0BAAQ,OAAc;AAChC;", "names": ["React"]}