{"version": 3, "sources": ["../../../src/components/NovuUI.tsx"], "sourcesContent": ["import { Novu } from '@novu/js';\nimport type { NovuUIOptions as JsNovuUIOptions } from '@novu/js/ui';\nimport { NovuUI as NovuUIClass } from '@novu/js/ui';\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\nimport { NovuUIProvider } from '../context/NovuUIContext';\nimport { useDataRef } from '../hooks/internal/useDataRef';\nimport type { ReactAppearance } from '../utils/types';\nimport { adaptAppearanceForJs } from '../utils/appearance';\nimport { useRenderer } from '../context/RendererContext';\nimport { ShadowRootDetector } from './ShadowRootDetector';\n\ntype NovuUIProps = Omit<JsNovuUIOptions, 'appearance'> & {\n  appearance?: ReactAppearance;\n};\n\ntype RendererProps = React.PropsWithChildren<{\n  options: NovuUIProps;\n  novu?: Novu;\n}>;\n\nconst findParentShadowRoot = (child?: HTMLDivElement | null): Node | null => {\n  if (!child) {\n    return null;\n  }\n\n  let node: Node | null = child;\n\n  while (node) {\n    if (node instanceof Element && node.shadowRoot) {\n      return node.shadowRoot;\n    }\n\n    if (node instanceof ShadowRoot) {\n      return node;\n    }\n\n    node = node.parentNode;\n\n    if (!node || node === document) {\n      break;\n    }\n  }\n\n  return null;\n};\n\nexport const NovuUI = ({ options, novu, children }: RendererProps) => {\n  const shadowRootDetector = useRef<HTMLDivElement>(null);\n  const { mountElement } = useRenderer();\n\n  const adaptedAppearanceForUpdate = useMemo(\n    () => adaptAppearanceForJs(options.appearance || {}, mountElement),\n    [options.appearance, mountElement]\n  );\n\n  const adaptedOptions = useMemo(() => {\n    return {\n      ...options,\n      appearance: adaptedAppearanceForUpdate,\n      novu,\n    };\n  }, [options, novu, adaptedAppearanceForUpdate]);\n\n  const optionsRef = useDataRef(adaptedOptions);\n  const [novuUI, setNovuUI] = useState<NovuUIClass | undefined>();\n\n  useEffect(() => {\n    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);\n    const instance = new NovuUIClass({\n      ...optionsRef.current,\n      container: optionsRef.current.container ?? parentShadowRoot,\n    });\n    setNovuUI(instance);\n\n    return () => {\n      instance.unmount();\n    };\n  }, []);\n\n  useEffect(() => {\n    if (!novuUI) {\n      return;\n    }\n\n    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);\n    novuUI.updateContainer(options.container ?? parentShadowRoot);\n    novuUI.updateAppearance(adaptedAppearanceForUpdate);\n    novuUI.updateLocalization(options.localization);\n    novuUI.updateTabs(options.tabs);\n    novuUI.updateOptions(options.options);\n    novuUI.updateRouterPush(options.routerPush);\n  }, [\n    shadowRootDetector,\n    novuUI,\n    adaptedAppearanceForUpdate,\n    options.localization,\n    options.tabs,\n    options.options,\n    options.routerPush,\n  ]);\n\n  return (\n    <>\n      <ShadowRootDetector ref={shadowRootDetector} />\n      {novuUI && <NovuUIProvider value={{ novuUI }}>{children}</NovuUIProvider>}\n    </>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAsC;AACtC,mBAA4D;AAC5D,2BAA+B;AAC/B,wBAA2B;AAE3B,wBAAqC;AACrC,6BAA4B;AAC5B,gCAAmC;AA6F/B;AAlFJ,IAAM,uBAAuB,CAAC,UAA+C;AAC3E,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,MAAI,OAAoB;AAExB,SAAO,MAAM;AACX,QAAI,gBAAgB,WAAW,KAAK,YAAY;AAC9C,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,gBAAgB,YAAY;AAC9B,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AAEZ,QAAI,CAAC,QAAQ,SAAS,UAAU;AAC9B;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,IAAM,SAAS,CAAC,EAAE,SAAS,MAAM,SAAS,MAAqB;AACpE,QAAM,yBAAqB,qBAAuB,IAAI;AACtD,QAAM,EAAE,aAAa,QAAI,oCAAY;AAErC,QAAM,iCAA6B;AAAA,IACjC,UAAM,wCAAqB,QAAQ,cAAc,CAAC,GAAG,YAAY;AAAA,IACjE,CAAC,QAAQ,YAAY,YAAY;AAAA,EACnC;AAEA,QAAM,qBAAiB,sBAAQ,MAAM;AACnC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,YAAY;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,MAAM,0BAA0B,CAAC;AAE9C,QAAM,iBAAa,8BAAW,cAAc;AAC5C,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAAkC;AAE9D,8BAAU,MAAM;AACd,UAAM,mBAAmB,qBAAqB,mBAAmB,OAAO;AACxE,UAAM,WAAW,IAAI,UAAAA,OAAY;AAAA,MAC/B,GAAG,WAAW;AAAA,MACd,WAAW,WAAW,QAAQ,aAAa;AAAA,IAC7C,CAAC;AACD,cAAU,QAAQ;AAElB,WAAO,MAAM;AACX,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,8BAAU,MAAM;AACd,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AAEA,UAAM,mBAAmB,qBAAqB,mBAAmB,OAAO;AACxE,WAAO,gBAAgB,QAAQ,aAAa,gBAAgB;AAC5D,WAAO,iBAAiB,0BAA0B;AAClD,WAAO,mBAAmB,QAAQ,YAAY;AAC9C,WAAO,WAAW,QAAQ,IAAI;AAC9B,WAAO,cAAc,QAAQ,OAAO;AACpC,WAAO,iBAAiB,QAAQ,UAAU;AAAA,EAC5C,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV,CAAC;AAED,SACE,4EACE;AAAA,gDAAC,gDAAmB,KAAK,oBAAoB;AAAA,IAC5C,UAAU,4CAAC,uCAAe,OAAO,EAAE,OAAO,GAAI,UAAS;AAAA,KAC1D;AAEJ;", "names": ["NovuUIClass"]}