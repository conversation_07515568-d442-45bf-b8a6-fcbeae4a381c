{"version": 3, "sources": ["../../../src/components/InboxContent.tsx"], "sourcesContent": ["import React from 'react';\nimport type { NotificationClickHandler, NotificationActionClickHandler, InboxPage } from '@novu/js/ui';\nimport { Mounter } from './Mounter';\nimport { NoRendererProps, SubjectBodyRendererProps, NotificationRendererProps } from '../utils/types';\nimport { useRenderer } from '../context/RendererContext';\nimport { useNovuUI } from '../context/NovuUIContext';\nimport { withRenderer } from './Renderer';\n\nexport type InboxContentProps = {\n  onNotificationClick?: NotificationClickHandler;\n  onPrimaryActionClick?: NotificationActionClickHandler;\n  onSecondaryActionClick?: NotificationActionClickHandler;\n  initialPage?: InboxPage;\n  hideNav?: boolean;\n} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);\n\nconst _InboxContent = React.memo((props: InboxContentProps) => {\n  const {\n    onNotificationClick,\n    onPrimaryActionClick,\n    renderNotification,\n    renderSubject,\n    renderBody,\n    onSecondaryActionClick,\n    initialPage,\n    hideNav,\n  } = props;\n  const { novuUI } = useNovuUI();\n  const { mountElement } = useRenderer();\n\n  const mount = React.useCallback(\n    (element: HTMLElement) => {\n      if (renderNotification) {\n        return novuUI.mountComponent({\n          name: 'InboxContent',\n          element,\n          props: {\n            renderNotification: renderNotification\n              ? (el, notification) => mountElement(el, renderNotification(notification))\n              : undefined,\n            onNotificationClick,\n            onPrimaryActionClick,\n            onSecondaryActionClick,\n            initialPage,\n            hideNav,\n          },\n        });\n      }\n\n      return novuUI.mountComponent({\n        name: 'InboxContent',\n        element,\n        props: {\n          renderSubject: renderSubject\n            ? (el, notification) => mountElement(el, renderSubject(notification))\n            : undefined,\n          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : undefined,\n          onNotificationClick,\n          onPrimaryActionClick,\n          onSecondaryActionClick,\n          initialPage,\n          hideNav,\n        },\n      });\n    },\n    [renderNotification, renderSubject, renderBody, onNotificationClick, onPrimaryActionClick, onSecondaryActionClick]\n  );\n\n  return <Mounter mount={mount} />;\n});\n\nexport const InboxContent = withRenderer(_InboxContent);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAElB,qBAAwB;AAExB,6BAA4B;AAC5B,2BAA0B;AAC1B,sBAA6B;AA8DpB;AApDT,IAAM,gBAAgB,aAAAA,QAAM,KAAK,CAAC,UAA6B;AAC7D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,EAAE,OAAO,QAAI,gCAAU;AAC7B,QAAM,EAAE,aAAa,QAAI,oCAAY;AAErC,QAAM,QAAQ,aAAAA,QAAM;AAAA,IAClB,CAAC,YAAyB;AACxB,UAAI,oBAAoB;AACtB,eAAO,OAAO,eAAe;AAAA,UAC3B,MAAM;AAAA,UACN;AAAA,UACA,OAAO;AAAA,YACL,oBAAoB,qBAChB,CAAC,IAAI,iBAAiB,aAAa,IAAI,mBAAmB,YAAY,CAAC,IACvE;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,OAAO,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN;AAAA,QACA,OAAO;AAAA,UACL,eAAe,gBACX,CAAC,IAAI,iBAAiB,aAAa,IAAI,cAAc,YAAY,CAAC,IAClE;AAAA,UACJ,YAAY,aAAa,CAAC,IAAI,iBAAiB,aAAa,IAAI,WAAW,YAAY,CAAC,IAAI;AAAA,UAC5F;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,oBAAoB,eAAe,YAAY,qBAAqB,sBAAsB,sBAAsB;AAAA,EACnH;AAEA,SAAO,4CAAC,0BAAQ,OAAc;AAChC,CAAC;AAEM,IAAM,mBAAe,8BAAa,aAAa;", "names": ["React"]}