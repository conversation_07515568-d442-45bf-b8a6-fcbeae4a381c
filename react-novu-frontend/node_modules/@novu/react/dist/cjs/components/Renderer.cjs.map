{"version": 3, "sources": ["../../../src/components/Renderer.tsx"], "sourcesContent": ["import { ComponentType, PropsWithChildren, useCallback, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { MountedElement, RendererProvider } from '../context/RendererContext';\n\ntype RendererProps = PropsWithChildren;\nexport const Renderer = (props: RendererProps) => {\n  const { children } = props;\n  const [mountedElements, setMountedElements] = useState(new Map<HTMLElement, MountedElement>());\n\n  const mountElement = useCallback(\n    (el: HTMLElement, mountedElement: MountedElement) => {\n      setMountedElements((prev) => {\n        const newMountedElements = new Map(prev);\n        newMountedElements.set(el, mountedElement);\n\n        return newMountedElements;\n      });\n\n      return () => {\n        setMountedElements((prev) => {\n          const newMountedElements = new Map(prev);\n          newMountedElements.delete(el);\n\n          return newMountedElements;\n        });\n      };\n    },\n    [setMountedElements]\n  );\n\n  return (\n    <RendererProvider value={{ mountElement }}>\n      {[...mountedElements].map(([element, mountedElement]) => {\n        return createPortal(mountedElement, element);\n      })}\n\n      {children}\n    </RendererProvider>\n  );\n};\n\nexport const withRenderer = <P extends object>(\n  WrappedComponent: ComponentType<P>\n): ComponentType<P & PropsWithChildren<{}>> => {\n  const HOC = (props: P) => {\n    return (\n      <Renderer>\n        <WrappedComponent {...props} />\n      </Renderer>\n    );\n  };\n\n  HOC.displayName = `WithRenderer(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;\n\n  return HOC;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwE;AACxE,uBAA6B;AAC7B,6BAAiD;AA6B7C;AA1BG,IAAM,WAAW,CAAC,UAAyB;AAChD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,uBAAS,oBAAI,IAAiC,CAAC;AAE7F,QAAM,mBAAe;AAAA,IACnB,CAAC,IAAiB,mBAAmC;AACnD,yBAAmB,CAAC,SAAS;AAC3B,cAAM,qBAAqB,IAAI,IAAI,IAAI;AACvC,2BAAmB,IAAI,IAAI,cAAc;AAEzC,eAAO;AAAA,MACT,CAAC;AAED,aAAO,MAAM;AACX,2BAAmB,CAAC,SAAS;AAC3B,gBAAM,qBAAqB,IAAI,IAAI,IAAI;AACvC,6BAAmB,OAAO,EAAE;AAE5B,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,CAAC,kBAAkB;AAAA,EACrB;AAEA,SACE,6CAAC,2CAAiB,OAAO,EAAE,aAAa,GACrC;AAAA,KAAC,GAAG,eAAe,EAAE,IAAI,CAAC,CAAC,SAAS,cAAc,MAAM;AACvD,iBAAO,+BAAa,gBAAgB,OAAO;AAAA,IAC7C,CAAC;AAAA,IAEA;AAAA,KACH;AAEJ;AAEO,IAAM,eAAe,CAC1B,qBAC6C;AAC7C,QAAM,MAAM,CAAC,UAAa;AACxB,WACE,4CAAC,YACC,sDAAC,oBAAkB,GAAG,OAAO,GAC/B;AAAA,EAEJ;AAEA,MAAI,cAAc,gBAAgB,iBAAiB,eAAe,iBAAiB,QAAQ,WAAW;AAEtG,SAAO;AACT;", "names": []}