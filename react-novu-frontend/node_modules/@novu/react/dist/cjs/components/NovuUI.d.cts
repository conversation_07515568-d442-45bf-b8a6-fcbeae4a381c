import * as react_jsx_runtime from 'react/jsx-runtime';
import { Novu } from '@novu/js';
import { NovuUIOptions } from '@novu/js/ui';
import React__default from 'react';
import { ReactAppearance } from '../utils/types.cjs';

type NovuUIProps = Omit<NovuUIOptions, 'appearance'> & {
    appearance?: ReactAppearance;
};
type RendererProps = React__default.PropsWithChildren<{
    options: NovuUIProps;
    novu?: Novu;
}>;
declare const NovuUI: ({ options, novu, children }: RendererProps) => react_jsx_runtime.JSX.Element;

export { NovuUI };
