"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/Mounter.tsx
var Mounter_exports = {};
__export(Mounter_exports, {
  Mounter: () => Mounter
});
module.exports = __toCommonJS(Mounter_exports);
var import_react = require("react");
var import_jsx_runtime = require("react/jsx-runtime");
function Mounter({ mount }) {
  const ref = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(() => {
    let unmount;
    const element = ref.current;
    if (element && mount) {
      const possibleUnmount = mount(element);
      if (possibleUnmount) {
        unmount = possibleUnmount;
      }
    }
    return () => {
      if (element && unmount) {
        unmount(element);
      }
    };
  }, [ref, mount]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { ref });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Mounter
});
//# sourceMappingURL=Mounter.cjs.map