{"version": 3, "sources": ["../../../src/components/Bell.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mounter } from './Mounter';\nimport { BellRenderer } from '../utils/types';\nimport { withRender<PERSON> } from './Renderer';\nimport { useNovuUI } from '../context/NovuUIContext';\nimport { useRenderer } from '../context/RendererContext';\n\nexport type BellProps = {\n  renderBell?: BellRenderer;\n};\n\nconst _Bell = React.memo((props: BellProps) => {\n  const { renderBell } = props;\n  const { novuUI } = useNovuUI();\n  const { mountElement } = useRenderer();\n\n  const mount = React.useCallback(\n    (element: HTMLElement) => {\n      return novuUI.mountComponent({\n        name: 'Bell',\n        element,\n        props: renderBell ? { renderBell: (el, unreadCount) => mountElement(el, renderBell(unreadCount)) } : undefined,\n      });\n    },\n    [renderBell]\n  );\n\n  return <Mounter mount={mount} />;\n});\n\nexport const Bell = withRenderer(_Bell);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBAAwB;AAExB,sBAA6B;AAC7B,2BAA0B;AAC1B,6BAA4B;AAsBnB;AAhBT,IAAM,QAAQ,aAAAA,QAAM,KAAK,CAAC,UAAqB;AAC7C,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,OAAO,QAAI,gCAAU;AAC7B,QAAM,EAAE,aAAa,QAAI,oCAAY;AAErC,QAAM,QAAQ,aAAAA,QAAM;AAAA,IAClB,CAAC,YAAyB;AACxB,aAAO,OAAO,eAAe;AAAA,QAC3B,MAAM;AAAA,QACN;AAAA,QACA,OAAO,aAAa,EAAE,YAAY,CAAC,IAAI,gBAAgB,aAAa,IAAI,WAAW,WAAW,CAAC,EAAE,IAAI;AAAA,MACvG,CAAC;AAAA,IACH;AAAA,IACA,CAAC,UAAU;AAAA,EACb;AAEA,SAAO,4CAAC,0BAAQ,OAAc;AAChC,CAAC;AAEM,IAAM,WAAO,8BAAa,KAAK;", "names": ["React"]}