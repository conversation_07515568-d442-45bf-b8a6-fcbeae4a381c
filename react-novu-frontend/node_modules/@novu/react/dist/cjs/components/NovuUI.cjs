"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/NovuUI.tsx
var NovuUI_exports = {};
__export(NovuUI_exports, {
  NovuUI: () => NovuUI
});
module.exports = __toCommonJS(NovuUI_exports);
var import_ui = require("@novu/js/ui");
var import_react = require("react");
var import_NovuUIContext = require("../context/NovuUIContext.cjs");
var import_useDataRef = require("../hooks/internal/useDataRef.cjs");
var import_appearance = require("../utils/appearance.cjs");
var import_RendererContext = require("../context/RendererContext.cjs");
var import_ShadowRootDetector = require("./ShadowRootDetector.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var findParentShadowRoot = (child) => {
  if (!child) {
    return null;
  }
  let node = child;
  while (node) {
    if (node instanceof Element && node.shadowRoot) {
      return node.shadowRoot;
    }
    if (node instanceof ShadowRoot) {
      return node;
    }
    node = node.parentNode;
    if (!node || node === document) {
      break;
    }
  }
  return null;
};
var NovuUI = ({ options, novu, children }) => {
  const shadowRootDetector = (0, import_react.useRef)(null);
  const { mountElement } = (0, import_RendererContext.useRenderer)();
  const adaptedAppearanceForUpdate = (0, import_react.useMemo)(
    () => (0, import_appearance.adaptAppearanceForJs)(options.appearance || {}, mountElement),
    [options.appearance, mountElement]
  );
  const adaptedOptions = (0, import_react.useMemo)(() => {
    return {
      ...options,
      appearance: adaptedAppearanceForUpdate,
      novu
    };
  }, [options, novu, adaptedAppearanceForUpdate]);
  const optionsRef = (0, import_useDataRef.useDataRef)(adaptedOptions);
  const [novuUI, setNovuUI] = (0, import_react.useState)();
  (0, import_react.useEffect)(() => {
    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);
    const instance = new import_ui.NovuUI({
      ...optionsRef.current,
      container: optionsRef.current.container ?? parentShadowRoot
    });
    setNovuUI(instance);
    return () => {
      instance.unmount();
    };
  }, []);
  (0, import_react.useEffect)(() => {
    if (!novuUI) {
      return;
    }
    const parentShadowRoot = findParentShadowRoot(shadowRootDetector.current);
    novuUI.updateContainer(options.container ?? parentShadowRoot);
    novuUI.updateAppearance(adaptedAppearanceForUpdate);
    novuUI.updateLocalization(options.localization);
    novuUI.updateTabs(options.tabs);
    novuUI.updateOptions(options.options);
    novuUI.updateRouterPush(options.routerPush);
  }, [
    shadowRootDetector,
    novuUI,
    adaptedAppearanceForUpdate,
    options.localization,
    options.tabs,
    options.options,
    options.routerPush
  ]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_ShadowRootDetector.ShadowRootDetector, { ref: shadowRootDetector }),
    novuUI && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_NovuUIContext.NovuUIProvider, { value: { novuUI }, children })
  ] });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  NovuUI
});
//# sourceMappingURL=NovuUI.cjs.map