"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/Renderer.tsx
var Renderer_exports = {};
__export(Renderer_exports, {
  Renderer: () => Renderer,
  withRenderer: () => withRenderer
});
module.exports = __toCommonJS(Renderer_exports);
var import_react = require("react");
var import_react_dom = require("react-dom");
var import_RendererContext = require("../context/RendererContext.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var Renderer = (props) => {
  const { children } = props;
  const [mountedElements, setMountedElements] = (0, import_react.useState)(/* @__PURE__ */ new Map());
  const mountElement = (0, import_react.useCallback)(
    (el, mountedElement) => {
      setMountedElements((prev) => {
        const newMountedElements = new Map(prev);
        newMountedElements.set(el, mountedElement);
        return newMountedElements;
      });
      return () => {
        setMountedElements((prev) => {
          const newMountedElements = new Map(prev);
          newMountedElements.delete(el);
          return newMountedElements;
        });
      };
    },
    [setMountedElements]
  );
  return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_RendererContext.RendererProvider, { value: { mountElement }, children: [
    [...mountedElements].map(([element, mountedElement]) => {
      return (0, import_react_dom.createPortal)(mountedElement, element);
    }),
    children
  ] });
};
var withRenderer = (WrappedComponent) => {
  const HOC = (props) => {
    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Renderer, { children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(WrappedComponent, { ...props }) });
  };
  HOC.displayName = `WithRenderer(${WrappedComponent.displayName || WrappedComponent.name || "Component"})`;
  return HOC;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Renderer,
  withRenderer
});
//# sourceMappingURL=Renderer.cjs.map