"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/InboxContent.tsx
var InboxContent_exports = {};
__export(InboxContent_exports, {
  InboxContent: () => InboxContent
});
module.exports = __toCommonJS(InboxContent_exports);
var import_react = __toESM(require("react"), 1);
var import_Mounter = require("./Mounter.cjs");
var import_RendererContext = require("../context/RendererContext.cjs");
var import_NovuUIContext = require("../context/NovuUIContext.cjs");
var import_Renderer = require("./Renderer.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var _InboxContent = import_react.default.memo((props) => {
  const {
    onNotificationClick,
    onPrimaryActionClick,
    renderNotification,
    renderSubject,
    renderBody,
    onSecondaryActionClick,
    initialPage,
    hideNav
  } = props;
  const { novuUI } = (0, import_NovuUIContext.useNovuUI)();
  const { mountElement } = (0, import_RendererContext.useRenderer)();
  const mount = import_react.default.useCallback(
    (element) => {
      if (renderNotification) {
        return novuUI.mountComponent({
          name: "InboxContent",
          element,
          props: {
            renderNotification: renderNotification ? (el, notification) => mountElement(el, renderNotification(notification)) : void 0,
            onNotificationClick,
            onPrimaryActionClick,
            onSecondaryActionClick,
            initialPage,
            hideNav
          }
        });
      }
      return novuUI.mountComponent({
        name: "InboxContent",
        element,
        props: {
          renderSubject: renderSubject ? (el, notification) => mountElement(el, renderSubject(notification)) : void 0,
          renderBody: renderBody ? (el, notification) => mountElement(el, renderBody(notification)) : void 0,
          onNotificationClick,
          onPrimaryActionClick,
          onSecondaryActionClick,
          initialPage,
          hideNav
        }
      });
    },
    [renderNotification, renderSubject, renderBody, onNotificationClick, onPrimaryActionClick, onSecondaryActionClick]
  );
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_Mounter.Mounter, { mount });
});
var InboxContent = (0, import_Renderer.withRenderer)(_InboxContent);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  InboxContent
});
//# sourceMappingURL=InboxContent.cjs.map