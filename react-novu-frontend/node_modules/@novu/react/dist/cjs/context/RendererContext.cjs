"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/context/RendererContext.tsx
var RendererContext_exports = {};
__export(RendererContext_exports, {
  RendererProvider: () => RendererProvider,
  useRenderer: () => useRendererContext,
  useUnsafeRenderer: () => useUnsafeRendererContext
});
module.exports = __toCommonJS(RendererContext_exports);
var import_createContextAndHook = require("../utils/createContextAndHook.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var [RendererContext, useRendererContext, useUnsafeRendererContext] = (0, import_createContextAndHook.createContextAndHook)("RendererContext");
var RendererProvider = (props) => {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(RendererContext.Provider, { value: { value: props.value }, children: props.children });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  RendererProvider,
  useRenderer,
  useUnsafeRenderer
});
//# sourceMappingURL=RendererContext.cjs.map