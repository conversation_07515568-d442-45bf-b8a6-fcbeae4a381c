import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default from 'react';
import { NovuUI } from '@novu/js/ui';

type NovuUIContextValue = {
    novuUI: NovuUI;
};
declare const useNovuUIContext: () => NovuUIContextValue;
declare const useUnsafeNovuUIContext: () => NovuUIContextValue | Partial<NovuUIContextValue>;
declare const NovuUIProvider: (props: React__default.PropsWithChildren<{
    value: NovuUIContextValue;
}>) => react_jsx_runtime.JSX.Element;

export { NovuUIProvider, useNovuUIContext as useNovuUI, useUnsafeNovuUIContext as useUnsafeNovuUI };
