"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/context/NovuUIContext.tsx
var NovuUIContext_exports = {};
__export(NovuUIContext_exports, {
  NovuUIProvider: () => NovuUIProvider,
  useNovuUI: () => useNovuUIContext,
  useUnsafeNovuUI: () => useUnsafeNovuUIContext
});
module.exports = __toCommonJS(NovuUIContext_exports);
var import_createContextAndHook = require("../utils/createContextAndHook.cjs");
var import_jsx_runtime = require("react/jsx-runtime");
var [NovuUIContext, useNovuUIContext, useUnsafeNovuUIContext] = (0, import_createContextAndHook.createContextAndHook)("NovuUIContext");
var NovuUIProvider = (props) => {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(NovuUIContext.Provider, { value: { value: props.value }, children: props.children });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  NovuUIProvider,
  useNovuUI,
  useUnsafeNovuUI
});
//# sourceMappingURL=NovuUIContext.cjs.map