"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  Bell: () => import_components.Bell,
  Inbox: () => import_components.Inbox,
  InboxContent: () => import_components.InboxContent,
  Notifications: () => import_components.Notifications,
  NovuProvider: () => import_components.NovuProvider,
  Preferences: () => import_components.Preferences,
  useCounts: () => import_hooks.useCounts,
  useNotifications: () => import_hooks.useNotifications,
  useNovu: () => import_hooks.useNovu,
  usePreferences: () => import_hooks.usePreferences
});
module.exports = __toCommonJS(src_exports);
var import_components = require("./components/index.cjs");
var import_hooks = require("./hooks/index.cjs");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Bell,
  Inbox,
  InboxContent,
  Notifications,
  NovuProvider,
  Preferences,
  useCounts,
  useNotifications,
  useNovu,
  usePreferences
});
//# sourceMappingURL=index.cjs.map