"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/utils/requestLock.ts
var requestLock_exports = {};
__export(requestLock_exports, {
  requestLock: () => requestLock
});
module.exports = __toCommonJS(requestLock_exports);
function requestLock(id, cb) {
  if (!("locks" in navigator)) {
    cb(id);
    return () => {
    };
  }
  let isFulfilled = false;
  let promiseResolve;
  const promise = new Promise((resolve) => {
    promiseResolve = resolve;
  });
  navigator.locks.request(id, () => {
    if (!isFulfilled) {
      cb(id);
    }
    return promise;
  });
  return () => {
    isFulfilled = true;
    promiseResolve();
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  requestLock
});
//# sourceMappingURL=requestLock.cjs.map