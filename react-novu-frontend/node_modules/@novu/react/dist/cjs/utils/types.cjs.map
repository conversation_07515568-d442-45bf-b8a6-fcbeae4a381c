{"version": 3, "sources": ["../../../src/utils/types.ts"], "sourcesContent": ["import type {\n  Notification,\n  NotificationClickHandler,\n  NotificationAction<PERSON>lick<PERSON><PERSON><PERSON>,\n  Tab,\n  Appearance as Js<PERSON><PERSON>arance,\n  Theme as Js<PERSON>heme,\n  IconKey,\n  Localization,\n  RouterPush,\n  PreferencesFilter,\n  PreferenceGroups,\n  InboxProps,\n  InboxPage,\n} from '@novu/js/ui';\nimport type { Subscriber } from '@novu/js';\nimport type { ReactNode } from 'react';\n\nexport type NotificationsRenderer = (notification: Notification) => React.ReactNode;\nexport type SubjectRenderer = (notification: Notification) => React.ReactNode;\nexport type BodyRenderer = (notification: Notification) => React.ReactNode;\nexport type BellRenderer = (unreadCount: number) => React.ReactNode;\n\nexport type ReactIconRendererProps = { class?: string };\nexport type ReactIconRenderer = (props: ReactIconRendererProps) => ReactNode;\n\nexport type ReactIconOverrides = {\n  [key in IconKey]?: ReactIconRenderer;\n};\n\nexport type ReactTheme = Omit<JsTheme, 'icons'> & {\n  icons?: ReactIconOverrides;\n};\n\nexport type ReactAppearance = ReactTheme & {\n  baseTheme?: JsTheme | JsTheme[];\n};\n\nexport type DefaultInboxProps = {\n  open?: boolean;\n  renderNotification?: NotificationsRenderer;\n  renderSubject?: SubjectRenderer;\n  renderBody?: BodyRenderer;\n  renderBell?: BellRenderer;\n  onNotificationClick?: NotificationClickHandler;\n  onPrimaryActionClick?: NotificationActionClickHandler;\n  onSecondaryActionClick?: NotificationActionClickHandler;\n  placement?: InboxProps['placement'];\n  placementOffset?: InboxProps['placementOffset'];\n};\n\ntype KeylessBaseProps = {} & { [K in string]?: never }; // empty object,disallows all unknown keys\n\ntype StandardBaseProps = {\n  applicationIdentifier: string;\n  subscriberHash?: string;\n  backendUrl?: string;\n  socketUrl?: string;\n  appearance?: ReactAppearance;\n  localization?: Localization;\n  tabs?: Array<Tab>;\n  preferencesFilter?: PreferencesFilter;\n  preferenceGroups?: PreferenceGroups;\n  routerPush?: RouterPush;\n} & (\n  | {\n      // TODO: Backward compatibility support - remove in future versions (see NV-5801)\n      /** @deprecated Use subscriber prop instead */\n      subscriberId: string;\n      subscriber?: never;\n    }\n  | {\n      subscriber: Subscriber | string;\n      subscriberId?: never;\n    }\n);\n\nexport type BaseProps = KeylessBaseProps | StandardBaseProps;\n\nexport type NotificationRendererProps = {\n  renderNotification: NotificationsRenderer;\n  renderSubject?: never;\n  renderBody?: never;\n};\n\nexport type SubjectBodyRendererProps = {\n  renderNotification?: never;\n  renderSubject?: SubjectRenderer;\n  renderBody?: BodyRenderer;\n};\n\nexport type NoRendererProps = {\n  renderNotification?: undefined;\n  renderSubject?: undefined;\n  renderBody?: undefined;\n};\n\nexport type DefaultProps = BaseProps &\n  DefaultInboxProps & {\n    children?: never;\n  } & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);\n\nexport type WithChildrenProps = BaseProps & {\n  children: React.ReactNode;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}