import { Notification, IconKey, Theme, NotificationClickHandler, NotificationActionClickHandler, InboxProps, Localization, Tab, PreferencesFilter, PreferenceGroups, RouterPush } from '@novu/js/ui';
import { Subscriber } from '@novu/js';
import { ReactNode } from 'react';

type NotificationsRenderer = (notification: Notification) => React.ReactNode;
type SubjectRenderer = (notification: Notification) => React.ReactNode;
type BodyRenderer = (notification: Notification) => React.ReactNode;
type BellRenderer = (unreadCount: number) => React.ReactNode;
type ReactIconRendererProps = {
    class?: string;
};
type ReactIconRenderer = (props: ReactIconRendererProps) => ReactNode;
type ReactIconOverrides = {
    [key in IconKey]?: ReactIconRenderer;
};
type ReactTheme = Omit<Theme, 'icons'> & {
    icons?: ReactIconOverrides;
};
type ReactAppearance = ReactTheme & {
    baseTheme?: Theme | Theme[];
};
type DefaultInboxProps = {
    open?: boolean;
    renderNotification?: NotificationsRenderer;
    renderSubject?: SubjectRenderer;
    renderBody?: BodyRenderer;
    renderBell?: BellRenderer;
    onNotificationClick?: NotificationClickHandler;
    onPrimaryActionClick?: NotificationActionClickHandler;
    onSecondaryActionClick?: NotificationActionClickHandler;
    placement?: InboxProps['placement'];
    placementOffset?: InboxProps['placementOffset'];
};
type KeylessBaseProps = {} & {
    [K in string]?: never;
};
type StandardBaseProps = {
    applicationIdentifier: string;
    subscriberHash?: string;
    backendUrl?: string;
    socketUrl?: string;
    appearance?: ReactAppearance;
    localization?: Localization;
    tabs?: Array<Tab>;
    preferencesFilter?: PreferencesFilter;
    preferenceGroups?: PreferenceGroups;
    routerPush?: RouterPush;
} & ({
    /** @deprecated Use subscriber prop instead */
    subscriberId: string;
    subscriber?: never;
} | {
    subscriber: Subscriber | string;
    subscriberId?: never;
});
type BaseProps = KeylessBaseProps | StandardBaseProps;
type NotificationRendererProps = {
    renderNotification: NotificationsRenderer;
    renderSubject?: never;
    renderBody?: never;
};
type SubjectBodyRendererProps = {
    renderNotification?: never;
    renderSubject?: SubjectRenderer;
    renderBody?: BodyRenderer;
};
type NoRendererProps = {
    renderNotification?: undefined;
    renderSubject?: undefined;
    renderBody?: undefined;
};
type DefaultProps = BaseProps & DefaultInboxProps & {
    children?: never;
} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);
type WithChildrenProps = BaseProps & {
    children: React.ReactNode;
};

export type { BaseProps, BellRenderer, BodyRenderer, DefaultInboxProps, DefaultProps, NoRendererProps, NotificationRendererProps, NotificationsRenderer, ReactAppearance, ReactIconOverrides, ReactIconRenderer, ReactIconRendererProps, ReactTheme, SubjectBodyRendererProps, SubjectRenderer, WithChildrenProps };
