"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/utils/createContextAndHook.ts
var createContextAndHook_exports = {};
__export(createContextAndHook_exports, {
  assertContextExists: () => assertContextExists,
  createContextAndHook: () => createContextAndHook
});
module.exports = __toCommonJS(createContextAndHook_exports);
var import_react = __toESM(require("react"), 1);
function assertContextExists(contextVal, msgOrCtx) {
  if (!contextVal) {
    throw typeof msgOrCtx === "string" ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);
  }
}
var createContextAndHook = (displayName, options) => {
  const { assertCtxFn = assertContextExists } = options || {};
  const Ctx = import_react.default.createContext(void 0);
  Ctx.displayName = displayName;
  const useCtx = () => {
    const ctx = import_react.default.useContext(Ctx);
    assertCtxFn(ctx, `Component must be wrapped with ${Ctx.displayName}`);
    return ctx.value;
  };
  const useCtxWithoutGuarantee = () => {
    const ctx = import_react.default.useContext(Ctx);
    return ctx ? ctx.value : {};
  };
  return [Ctx, useCtx, useCtxWithoutGuarantee];
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  assertContextExists,
  createContextAndHook
});
//# sourceMappingURL=createContextAndHook.cjs.map