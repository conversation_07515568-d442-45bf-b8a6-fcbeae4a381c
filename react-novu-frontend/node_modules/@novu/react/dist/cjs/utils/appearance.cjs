"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/utils/appearance.ts
var appearance_exports = {};
__export(appearance_exports, {
  adaptAppearanceForJs: () => adaptAppearanceForJs
});
module.exports = __toCommonJS(appearance_exports);
function adaptAppearanceForJs(appearance, mountElement) {
  if (!appearance) {
    return void 0;
  }
  const jsAppearance = JSON.parse(JSON.stringify(appearance));
  if (appearance.icons) {
    const jsIcons = {};
    const reactIcons = appearance.icons;
    const iconKeys = Object.keys(reactIcons);
    for (const iconKey of iconKeys) {
      const reactRenderer = reactIcons[iconKey];
      if (reactRenderer) {
        jsIcons[iconKey] = (el, props) => {
          return mountElement(el, reactRenderer(props));
        };
      }
    }
    jsAppearance.icons = jsIcons;
  } else {
    delete jsAppearance.icons;
  }
  return jsAppearance;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  adaptAppearanceForJs
});
//# sourceMappingURL=appearance.cjs.map