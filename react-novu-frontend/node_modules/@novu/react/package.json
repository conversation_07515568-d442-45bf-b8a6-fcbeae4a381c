{"name": "@novu/react", "version": "3.6.0", "repository": {"type": "git", "url": "https://github.com/novuhq/novu", "directory": "packages/react"}, "homepage": "https://novu.co", "description": "Novu <Inbox /> React SDK", "author": "Novu", "license": "ISC", "type": "module", "main": "./dist/cjs/server/index.cjs", "browser": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "exports": {".": {"browser": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/server/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/server/index.cjs"}}, "./hooks": {"import": {"types": "./dist/esm/hooks/index.d.ts", "default": "./dist/esm/hooks/index.js"}, "require": {"types": "./dist/cjs/hooks/index.d.cts", "default": "./dist/cjs/hooks/index.cjs"}}, "./themes": {"import": {"types": "./dist/esm/themes/index.d.ts", "default": "./dist/esm/themes/index.js"}, "require": {"types": "./dist/cjs/themes/index.d.cts", "default": "./dist/cjs/themes/index.cjs"}}, "./server": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/server/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/server/index.cjs"}}}, "files": ["dist", "dist/esm/**/*", "dist/cjs/**/*", "hooks/**/*", "themes/**/*", "server/**/*"], "sideEffects": false, "private": false, "publishConfig": {"access": "public"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@types/node": "^20.14.12", "@types/react": "*", "@types/react-dom": "*", "esbuild-plugin-file-path-extensions": "^2.1.4", "tsup": "^8.2.1", "typescript": "5.6.2"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-0", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}, "dependencies": {"@novu/js": "3.6.0"}, "nx": {"tags": ["type:package"]}, "scripts": {"build:watch": "tsup --watch", "build": "tsup && pnpm run check-exports", "lint": "eslint src", "check-exports": "attw --pack .", "publish:rc": "pnpm publish --tag rc"}}