import { __privateAdd, __privateSet, __privateGet, __async, __objRest, __spreadValues, __spreadProps, __privateMethod } from './chunk-STZMOEWR.mjs';
import mitt from 'mitt';
import io from 'socket.io-client';
import { WebSocket } from 'partysocket';

// src/types.ts
var NotificationStatus = /* @__PURE__ */ ((NotificationStatus2) => {
  NotificationStatus2["READ"] = "read";
  NotificationStatus2["SEEN"] = "seen";
  NotificationStatus2["SNOOZED"] = "snoozed";
  NotificationStatus2["UNREAD"] = "unread";
  NotificationStatus2["UNSEEN"] = "unseen";
  NotificationStatus2["UNSNOOZED"] = "unsnoozed";
  return NotificationStatus2;
})(NotificationStatus || {});
var PreferenceLevel = /* @__PURE__ */ ((PreferenceLevel2) => {
  PreferenceLevel2["GLOBAL"] = "global";
  PreferenceLevel2["TEMPLATE"] = "template";
  return PreferenceLevel2;
})(PreferenceLevel || {});
var ChannelType = /* @__PURE__ */ ((ChannelType2) => {
  ChannelType2["IN_APP"] = "in_app";
  ChannelType2["EMAIL"] = "email";
  ChannelType2["SMS"] = "sms";
  ChannelType2["CHAT"] = "chat";
  ChannelType2["PUSH"] = "push";
  return ChannelType2;
})(ChannelType || {});
var WebSocketEvent = /* @__PURE__ */ ((WebSocketEvent2) => {
  WebSocketEvent2["RECEIVED"] = "notification_received";
  WebSocketEvent2["UNREAD"] = "unread_count_changed";
  WebSocketEvent2["UNSEEN"] = "unseen_count_changed";
  return WebSocketEvent2;
})(WebSocketEvent || {});

// src/utils/arrays.ts
var arrayValuesEqual = (arr1, arr2) => {
  if (arr1 === arr2) {
    return true;
  }
  if (!arr1 || !arr2) {
    return false;
  }
  if (arr1.length !== arr2.length) {
    return false;
  }
  return arr1.every((value, index) => value === arr2[index]);
};
var areTagsEqual = (tags1, tags2) => {
  return arrayValuesEqual(tags1, tags2) || !tags1 && (tags2 == null ? void 0 : tags2.length) === 0 || (tags1 == null ? void 0 : tags1.length) === 0 && !tags2;
};
var areDataEqual = (data1, data2) => {
  if (!data1 && !data2) {
    return true;
  }
  if (!data1 || !data2) {
    return false;
  }
  try {
    return JSON.stringify(data1) === JSON.stringify(data2);
  } catch (e) {
    return false;
  }
};
var isSameFilter = (filter1, filter2) => {
  return areDataEqual(filter1.data, filter2.data) && areTagsEqual(filter1.tags, filter2.tags) && filter1.read === filter2.read && filter1.archived === filter2.archived && filter1.snoozed === filter2.snoozed;
};

// src/api/http-client.ts
var DEFAULT_API_VERSION = "v1";
var DEFAULT_USER_AGENT = `${"@novu/js"}@${"3.6.0"}`;
var HttpClient = class {
  constructor(options = {}) {
    // Environment variable for local development that overrides the default API endpoint without affecting the Inbox DX
    this.DEFAULT_BACKEND_URL = typeof window !== "undefined" && window.NOVU_LOCAL_BACKEND_URL || "https://api.novu.co";
    const {
      apiVersion = DEFAULT_API_VERSION,
      apiUrl = this.DEFAULT_BACKEND_URL,
      userAgent = DEFAULT_USER_AGENT,
      headers = {}
    } = options || {};
    this.apiVersion = apiVersion;
    this.apiUrl = `${apiUrl}/${apiVersion}`;
    this.headers = __spreadValues({
      "Novu-API-Version": "2024-06-26",
      "Content-Type": "application/json",
      "User-Agent": userAgent
    }, headers);
  }
  setAuthorizationToken(token) {
    this.headers.Authorization = `Bearer ${token}`;
  }
  setKeylessHeader(identifier) {
    var _a;
    const keylessAppIdentifier = identifier || typeof window !== "undefined" && ((_a = window.localStorage) == null ? void 0 : _a.getItem("novu_keyless_application_identifier"));
    if (!keylessAppIdentifier || !keylessAppIdentifier.startsWith("pk_keyless_")) {
      return;
    }
    this.headers["Novu-Application-Identifier"] = keylessAppIdentifier;
  }
  setHeaders(headers) {
    this.headers = __spreadValues(__spreadValues({}, this.headers), headers);
  }
  get(path, searchParams, unwrapEnvelope = true) {
    return __async(this, null, function* () {
      return this.doFetch({
        path,
        searchParams,
        options: {
          method: "GET"
        },
        unwrapEnvelope
      });
    });
  }
  post(path, body, options) {
    return __async(this, null, function* () {
      return this.doFetch({
        path,
        options: {
          method: "POST",
          body,
          headers: options == null ? void 0 : options.headers
        }
      });
    });
  }
  patch(path, body) {
    return __async(this, null, function* () {
      return this.doFetch({
        path,
        options: {
          method: "PATCH",
          body
        }
      });
    });
  }
  delete(path, body) {
    return __async(this, null, function* () {
      return this.doFetch({
        path,
        options: {
          method: "DELETE",
          body
        }
      });
    });
  }
  doFetch(_0) {
    return __async(this, arguments, function* ({
      path,
      searchParams,
      options,
      unwrapEnvelope = true
    }) {
      const fullUrl = combineUrl(this.apiUrl, path, searchParams ? `?${searchParams.toString()}` : "");
      const reqInit = {
        method: (options == null ? void 0 : options.method) || "GET",
        headers: __spreadValues(__spreadValues({}, this.headers), (options == null ? void 0 : options.headers) || {}),
        body: (options == null ? void 0 : options.body) ? JSON.stringify(options.body) : void 0
      };
      const response = yield fetch(fullUrl, reqInit);
      if (!response.ok) {
        const errorData = yield response.json();
        throw new Error(`${this.headers["User-Agent"]} error. Status: ${response.status}, Message: ${errorData.message}`);
      }
      if (response.status === 204) {
        return void 0;
      }
      const res = yield response.json();
      return unwrapEnvelope ? res.data : res;
    });
  }
};
function combineUrl(...args) {
  return args.reduce((acc, part) => {
    if (part) {
      acc.push(part.replace(new RegExp("(?<!https?:)\\/+", "g"), "/").replace(/^\/+|\/+$/g, ""));
    }
    return acc;
  }, []).join("/").replace(/\/\?/, "?");
}

// src/api/inbox-service.ts
var INBOX_ROUTE = "/inbox";
var INBOX_NOTIFICATIONS_ROUTE = `${INBOX_ROUTE}/notifications`;
var _httpClient;
var InboxService = class {
  constructor(options = {}) {
    this.isSessionInitialized = false;
    __privateAdd(this, _httpClient);
    __privateSet(this, _httpClient, new HttpClient(options));
  }
  initializeSession(_0) {
    return __async(this, arguments, function* ({
      applicationIdentifier,
      subscriberHash,
      subscriber
    }) {
      const response = yield __privateGet(this, _httpClient).post(`${INBOX_ROUTE}/session`, {
        applicationIdentifier,
        subscriberHash,
        subscriber
      });
      __privateGet(this, _httpClient).setAuthorizationToken(response.token);
      __privateGet(this, _httpClient).setKeylessHeader(response.applicationIdentifier);
      this.isSessionInitialized = true;
      return response;
    });
  }
  fetchNotifications({
    after,
    archived,
    limit = 10,
    offset,
    read: read2,
    tags,
    snoozed,
    data
  }) {
    const searchParams = new URLSearchParams(`limit=${limit}`);
    if (after) {
      searchParams.append("after", after);
    }
    if (offset) {
      searchParams.append("offset", `${offset}`);
    }
    if (tags) {
      tags.forEach((tag) => searchParams.append("tags[]", tag));
    }
    if (read2 !== void 0) {
      searchParams.append("read", `${read2}`);
    }
    if (archived !== void 0) {
      searchParams.append("archived", `${archived}`);
    }
    if (snoozed !== void 0) {
      searchParams.append("snoozed", `${snoozed}`);
    }
    if (data !== void 0) {
      searchParams.append("data", JSON.stringify(data));
    }
    return __privateGet(this, _httpClient).get(INBOX_NOTIFICATIONS_ROUTE, searchParams, false);
  }
  count({
    filters
  }) {
    return __privateGet(this, _httpClient).get(
      `${INBOX_NOTIFICATIONS_ROUTE}/count`,
      new URLSearchParams({
        filters: JSON.stringify(filters)
      }),
      false
    );
  }
  read(notificationId) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/read`);
  }
  unread(notificationId) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/unread`);
  }
  archive(notificationId) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/archive`);
  }
  unarchive(notificationId) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/unarchive`);
  }
  snooze(notificationId, snoozeUntil) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/snooze`, { snoozeUntil });
  }
  unsnooze(notificationId) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/unsnooze`);
  }
  readAll({ tags, data }) {
    return __privateGet(this, _httpClient).post(`${INBOX_NOTIFICATIONS_ROUTE}/read`, {
      tags,
      data: data ? JSON.stringify(data) : void 0
    });
  }
  archiveAll({ tags, data }) {
    return __privateGet(this, _httpClient).post(`${INBOX_NOTIFICATIONS_ROUTE}/archive`, {
      tags,
      data: data ? JSON.stringify(data) : void 0
    });
  }
  archiveAllRead({ tags, data }) {
    return __privateGet(this, _httpClient).post(`${INBOX_NOTIFICATIONS_ROUTE}/read-archive`, {
      tags,
      data: data ? JSON.stringify(data) : void 0
    });
  }
  completeAction({
    actionType,
    notificationId
  }) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/complete`, {
      actionType
    });
  }
  revertAction({
    actionType,
    notificationId
  }) {
    return __privateGet(this, _httpClient).patch(`${INBOX_NOTIFICATIONS_ROUTE}/${notificationId}/revert`, {
      actionType
    });
  }
  fetchPreferences(tags) {
    const queryParams = new URLSearchParams();
    if (tags) {
      tags.forEach((tag) => queryParams.append("tags[]", tag));
    }
    const query = queryParams.size ? `?${queryParams.toString()}` : "";
    return __privateGet(this, _httpClient).get(`${INBOX_ROUTE}/preferences${query}`);
  }
  bulkUpdatePreferences(preferences) {
    return __privateGet(this, _httpClient).patch(`${INBOX_ROUTE}/preferences/bulk`, { preferences });
  }
  updateGlobalPreferences(channels) {
    return __privateGet(this, _httpClient).patch(`${INBOX_ROUTE}/preferences`, channels);
  }
  updateWorkflowPreferences({
    workflowId,
    channels
  }) {
    return __privateGet(this, _httpClient).patch(`${INBOX_ROUTE}/preferences/${workflowId}`, channels);
  }
  triggerHelloWorldEvent() {
    const payload = {
      name: "hello-world",
      to: {
        subscriberId: "keyless-subscriber-id"
      },
      payload: {
        subject: "Novu Keyless Environment",
        body: "You're using a keyless demo environment. For full access to Novu features and cloud integration, obtain your API key.",
        primaryActionText: "Obtain API Key",
        primaryActionUrl: "https://go.novu.co/keyless",
        secondaryActionText: "Explore Documentation",
        secondaryActionUrl: "https://go.novu.co/keyless-docs"
      }
    };
    return __privateGet(this, _httpClient).post("/inbox/events", payload);
  }
};
_httpClient = new WeakMap();
var _mittEmitter;
var NovuEventEmitter = class {
  constructor() {
    __privateAdd(this, _mittEmitter);
    __privateSet(this, _mittEmitter, mitt());
  }
  on(eventName, listener) {
    __privateGet(this, _mittEmitter).on(eventName, listener);
    return () => {
      this.off(eventName, listener);
    };
  }
  off(eventName, listener) {
    __privateGet(this, _mittEmitter).off(eventName, listener);
  }
  emit(type, event) {
    __privateGet(this, _mittEmitter).emit(type, event);
  }
};
_mittEmitter = new WeakMap();

// src/utils/errors.ts
var NovuError = class extends Error {
  constructor(message, originalError) {
    super(message);
    this.originalError = originalError;
  }
};

// src/base-module.ts
var _callsQueue, _sessionError;
var BaseModule = class {
  constructor({
    inboxServiceInstance,
    eventEmitterInstance
  }) {
    __privateAdd(this, _callsQueue, []);
    __privateAdd(this, _sessionError);
    this._emitter = eventEmitterInstance;
    this._inboxService = inboxServiceInstance;
    this._emitter.on("session.initialize.resolved", ({ error, data }) => {
      if (data) {
        this.onSessionSuccess(data);
        __privateGet(this, _callsQueue).forEach((_0) => __async(this, [_0], function* ({ fn, resolve }) {
          resolve(yield fn());
        }));
        __privateSet(this, _callsQueue, []);
      } else if (error) {
        this.onSessionError(error);
        __privateSet(this, _sessionError, error);
        __privateGet(this, _callsQueue).forEach(({ resolve }) => {
          resolve({ error: new NovuError("Failed to initialize session, please contact the support", error) });
        });
        __privateSet(this, _callsQueue, []);
      }
    });
  }
  onSessionSuccess(_) {
  }
  onSessionError(_) {
  }
  callWithSession(fn) {
    return __async(this, null, function* () {
      if (this._inboxService.isSessionInitialized) {
        return fn();
      }
      if (__privateGet(this, _sessionError)) {
        return Promise.resolve({
          error: new NovuError("Failed to initialize session, please contact the support", __privateGet(this, _sessionError))
        });
      }
      return new Promise((resolve, reject) => {
        __privateGet(this, _callsQueue).push({ fn, resolve, reject });
      });
    });
  }
};
_callsQueue = new WeakMap();
_sessionError = new WeakMap();

// src/notifications/notification.ts
var _emitter, _inboxService;
var Notification = class {
  constructor(notification, emitter, inboxService) {
    __privateAdd(this, _emitter);
    __privateAdd(this, _inboxService);
    __privateSet(this, _emitter, emitter);
    __privateSet(this, _inboxService, inboxService);
    this.id = notification.id;
    this.subject = notification.subject;
    this.body = notification.body;
    this.to = notification.to;
    this.isRead = notification.isRead;
    this.isArchived = notification.isArchived;
    this.isSnoozed = notification.isSnoozed;
    this.snoozedUntil = notification.snoozedUntil;
    this.deliveredAt = notification.deliveredAt;
    this.createdAt = notification.createdAt;
    this.readAt = notification.readAt;
    this.archivedAt = notification.archivedAt;
    this.avatar = notification.avatar;
    this.primaryAction = notification.primaryAction;
    this.secondaryAction = notification.secondaryAction;
    this.channelType = notification.channelType;
    this.tags = notification.tags;
    this.redirect = notification.redirect;
    this.data = notification.data;
    this.workflow = notification.workflow;
  }
  read() {
    return read({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      }
    });
  }
  unread() {
    return unread({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      }
    });
  }
  archive() {
    return archive({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      }
    });
  }
  unarchive() {
    return unarchive({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      }
    });
  }
  snooze(snoozeUntil) {
    return snooze({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this,
        snoozeUntil
      }
    });
  }
  unsnooze() {
    return unsnooze({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: { notification: this }
    });
  }
  completePrimary() {
    if (!this.primaryAction) {
      throw new Error("Primary action is not available");
    }
    return completeAction({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      },
      actionType: "primary" /* PRIMARY */
    });
  }
  completeSecondary() {
    if (!this.primaryAction) {
      throw new Error("Secondary action is not available");
    }
    return completeAction({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      },
      actionType: "secondary" /* SECONDARY */
    });
  }
  revertPrimary() {
    if (!this.primaryAction) {
      throw new Error("Primary action is not available");
    }
    return revertAction({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      },
      actionType: "primary" /* PRIMARY */
    });
  }
  revertSecondary() {
    if (!this.primaryAction) {
      throw new Error("Secondary action is not available");
    }
    return revertAction({
      emitter: __privateGet(this, _emitter),
      apiService: __privateGet(this, _inboxService),
      args: {
        notification: this
      },
      actionType: "secondary" /* SECONDARY */
    });
  }
  on(eventName, listener) {
    const cleanup = __privateGet(this, _emitter).on(eventName, listener);
    return () => {
      cleanup();
    };
  }
  /**
   * @deprecated
   * Use the cleanup function returned by the "on" method instead.
   */
  off(eventName, listener) {
    __privateGet(this, _emitter).off(eventName, listener);
  }
};
_emitter = new WeakMap();
_inboxService = new WeakMap();

// src/notifications/helpers.ts
var read = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isRead: true,
      readAt: (/* @__PURE__ */ new Date()).toISOString(),
      isArchived: false,
      archivedAt: void 0
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.read.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.read(notificationId);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.read.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.read.resolved", { args, error });
    return { error: new NovuError("Failed to read notification", error) };
  }
});
var unread = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isRead: false,
      readAt: null,
      isArchived: false,
      archivedAt: void 0
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.unread.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.unread(notificationId);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.unread.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.unread.resolved", { args, error });
    return { error: new NovuError("Failed to unread notification", error) };
  }
});
var archive = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isArchived: true,
      archivedAt: (/* @__PURE__ */ new Date()).toISOString(),
      isRead: true,
      readAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.archive.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.archive(notificationId);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.archive.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.archive.resolved", { args, error });
    return { error: new NovuError("Failed to archive notification", error) };
  }
});
var unarchive = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isArchived: false,
      archivedAt: null,
      isRead: true,
      readAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.unarchive.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.unarchive(notificationId);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.unarchive.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.unarchive.resolved", { args, error });
    return { error: new NovuError("Failed to unarchive notification", error) };
  }
});
var snooze = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isSnoozed: true,
      snoozedUntil: args.snoozeUntil
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.snooze.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.snooze(notificationId, args.snoozeUntil);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.snooze.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.snooze.resolved", { args, error });
    return { error: new NovuError("Failed to snooze notification", error) };
  }
});
var unsnooze = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args
}) {
  const { notificationId, optimisticValue } = getNotificationDetails(
    args,
    {
      isSnoozed: false,
      snoozedUntil: null
    },
    {
      emitter,
      apiService
    }
  );
  try {
    emitter.emit("notification.unsnooze.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.unsnooze(notificationId);
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.unsnooze.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.unsnooze.resolved", { args, error });
    return { error: new NovuError("Failed to unsnooze notification", error) };
  }
});
var completeAction = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args,
  actionType
}) {
  const optimisticUpdate = actionType === "primary" /* PRIMARY */ ? {
    primaryAction: __spreadProps(__spreadValues({}, "notification" in args ? args.notification.primaryAction : {}), {
      isCompleted: true
    })
  } : {
    secondaryAction: __spreadProps(__spreadValues({}, "notification" in args ? args.notification.secondaryAction : {}), {
      isCompleted: true
    })
  };
  const { notificationId, optimisticValue } = getNotificationDetails(args, optimisticUpdate, {
    emitter,
    apiService
  });
  try {
    emitter.emit("notification.complete_action.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.completeAction({ actionType, notificationId });
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.complete_action.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.complete_action.resolved", { args, error });
    return { error: new NovuError(`Failed to complete ${actionType} action on the notification`, error) };
  }
});
var revertAction = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  args,
  actionType
}) {
  const optimisticUpdate = actionType === "primary" /* PRIMARY */ ? {
    primaryAction: __spreadProps(__spreadValues({}, "notification" in args ? args.notification.primaryAction : {}), {
      isCompleted: false
    })
  } : {
    secondaryAction: __spreadProps(__spreadValues({}, "notification" in args ? args.notification.secondaryAction : {}), {
      isCompleted: false
    })
  };
  const { notificationId, optimisticValue } = getNotificationDetails(args, optimisticUpdate, {
    emitter,
    apiService
  });
  try {
    emitter.emit("notification.revert_action.pending", {
      args,
      data: optimisticValue
    });
    const response = yield apiService.revertAction({ actionType, notificationId });
    const updatedNotification = new Notification(response, emitter, apiService);
    emitter.emit("notification.revert_action.resolved", { args, data: updatedNotification });
    return { data: updatedNotification };
  } catch (error) {
    emitter.emit("notification.revert_action.resolved", { args, error });
    return { error: new NovuError("Failed to fetch notifications", error) };
  }
});
var getNotificationDetails = (args, update, dependencies) => {
  if ("notification" in args) {
    return {
      notificationId: args.notification.id,
      optimisticValue: new Notification(
        __spreadValues(__spreadValues({}, args.notification), update),
        dependencies.emitter,
        dependencies.apiService
      )
    };
  } else {
    return {
      notificationId: args.notificationId
    };
  }
};
var readAll = (_0) => __async(void 0, [_0], function* ({
  emitter,
  inboxService,
  notificationsCache,
  tags,
  data
}) {
  try {
    const notifications = notificationsCache.getUniqueNotifications({ tags, data });
    const optimisticNotifications = notifications.map(
      (notification) => new Notification(
        __spreadProps(__spreadValues({}, notification), {
          isRead: true,
          readAt: (/* @__PURE__ */ new Date()).toISOString(),
          isArchived: false,
          archivedAt: void 0
        }),
        emitter,
        inboxService
      )
    );
    emitter.emit("notifications.read_all.pending", { args: { tags, data }, data: optimisticNotifications });
    yield inboxService.readAll({ tags, data });
    emitter.emit("notifications.read_all.resolved", { args: { tags, data }, data: optimisticNotifications });
    return {};
  } catch (error) {
    emitter.emit("notifications.read_all.resolved", { args: { tags, data }, error });
    return { error: new NovuError("Failed to read all notifications", error) };
  }
});
var archiveAll = (_0) => __async(void 0, [_0], function* ({
  emitter,
  inboxService,
  notificationsCache,
  tags,
  data
}) {
  try {
    const notifications = notificationsCache.getUniqueNotifications({ tags, data });
    const optimisticNotifications = notifications.map(
      (notification) => new Notification(
        __spreadProps(__spreadValues({}, notification), {
          isRead: true,
          readAt: (/* @__PURE__ */ new Date()).toISOString(),
          isArchived: true,
          archivedAt: (/* @__PURE__ */ new Date()).toISOString()
        }),
        emitter,
        inboxService
      )
    );
    emitter.emit("notifications.archive_all.pending", { args: { tags, data }, data: optimisticNotifications });
    yield inboxService.archiveAll({ tags, data });
    emitter.emit("notifications.archive_all.resolved", { args: { tags, data }, data: optimisticNotifications });
    return {};
  } catch (error) {
    emitter.emit("notifications.archive_all.resolved", { args: { tags, data }, error });
    return { error: new NovuError("Failed to archive all notifications", error) };
  }
});
var archiveAllRead = (_0) => __async(void 0, [_0], function* ({
  emitter,
  inboxService,
  notificationsCache,
  tags,
  data
}) {
  try {
    const notifications = notificationsCache.getUniqueNotifications({ tags, data, read: true });
    const optimisticNotifications = notifications.map(
      (notification) => new Notification(
        __spreadProps(__spreadValues({}, notification), { isArchived: true, archivedAt: (/* @__PURE__ */ new Date()).toISOString() }),
        emitter,
        inboxService
      )
    );
    emitter.emit("notifications.archive_all_read.pending", { args: { tags, data }, data: optimisticNotifications });
    yield inboxService.archiveAllRead({ tags, data });
    emitter.emit("notifications.archive_all_read.resolved", { args: { tags, data }, data: optimisticNotifications });
    return {};
  } catch (error) {
    emitter.emit("notifications.archive_all_read.resolved", { args: { tags, data }, error });
    return { error: new NovuError("Failed to archive all read notifications", error) };
  }
});

// src/cache/in-memory-cache.ts
var _cache;
var InMemoryCache = class {
  constructor() {
    __privateAdd(this, _cache);
    __privateSet(this, _cache, /* @__PURE__ */ new Map());
  }
  get(key) {
    return __privateGet(this, _cache).get(key);
  }
  getValues() {
    return Array.from(__privateGet(this, _cache).values());
  }
  entries() {
    return Array.from(__privateGet(this, _cache).entries());
  }
  keys() {
    return Array.from(__privateGet(this, _cache).keys());
  }
  set(key, value) {
    __privateGet(this, _cache).set(key, value);
  }
  remove(key) {
    __privateGet(this, _cache).delete(key);
  }
  clear() {
    __privateGet(this, _cache).clear();
  }
};
_cache = new WeakMap();

// src/cache/notifications-cache.ts
var excludeEmpty = ({ tags, data, read: read2, archived, snoozed, limit, offset, after }) => Object.entries({ tags, data, read: read2, archived, snoozed, limit, offset, after }).filter(([_, value]) => value !== null && value !== void 0 && !(Array.isArray(value) && value.length === 0)).reduce((acc, [key, value]) => {
  acc[key] = value;
  return acc;
}, {});
var getCacheKey = ({ tags, data, read: read2, archived, snoozed, limit, offset, after }) => {
  return JSON.stringify(excludeEmpty({ tags, data, read: read2, archived, snoozed, limit, offset, after }));
};
var getFilterKey = ({
  tags,
  data,
  read: read2,
  archived,
  snoozed
}) => {
  return JSON.stringify(excludeEmpty({ tags, data, read: read2, archived, snoozed }));
};
var getFilter = (key) => {
  return JSON.parse(key);
};
var updateEvents = [
  "notification.read.pending",
  "notification.read.resolved",
  "notification.unread.pending",
  "notification.unread.resolved",
  "notification.complete_action.pending",
  "notification.complete_action.resolved",
  "notification.revert_action.pending",
  "notification.revert_action.resolved",
  "notifications.read_all.pending",
  "notifications.read_all.resolved"
];
var removeEvents = [
  "notification.archive.pending",
  "notification.unarchive.pending",
  "notification.snooze.pending",
  "notification.unsnooze.pending",
  "notifications.archive_all.pending",
  "notifications.archive_all_read.pending"
];
var _emitter2, _cache2;
var NotificationsCache = class {
  constructor({ emitter }) {
    __privateAdd(this, _emitter2);
    /**
     * The key is the stringified notifications filter, the values are the paginated notifications.
     */
    __privateAdd(this, _cache2);
    this.updateNotification = (key, data) => {
      const notificationsResponse = __privateGet(this, _cache2).get(key);
      if (!notificationsResponse) {
        return false;
      }
      const index = notificationsResponse.notifications.findIndex((el) => el.id === data.id);
      if (index === -1) {
        return false;
      }
      const updatedNotifications = [...notificationsResponse.notifications];
      updatedNotifications[index] = data;
      __privateGet(this, _cache2).set(key, __spreadProps(__spreadValues({}, notificationsResponse), { notifications: updatedNotifications }));
      return true;
    };
    this.removeNotification = (key, data) => {
      const notificationsResponse = __privateGet(this, _cache2).get(key);
      if (!notificationsResponse) {
        return false;
      }
      const index = notificationsResponse.notifications.findIndex((el) => el.id === data.id);
      if (index === -1) {
        return false;
      }
      const newNotifications = [...notificationsResponse.notifications];
      newNotifications.splice(index, 1);
      __privateGet(this, _cache2).set(key, __spreadProps(__spreadValues({}, notificationsResponse), {
        notifications: newNotifications
      }));
      return true;
    };
    this.handleNotificationEvent = ({ remove } = { remove: false }) => ({ data }) => {
      if (!data) {
        return;
      }
      const notifications = Array.isArray(data) ? data : [data];
      const uniqueFilterKeys = /* @__PURE__ */ new Set();
      __privateGet(this, _cache2).keys().forEach((key) => {
        notifications.forEach((notification) => {
          let isNotificationFound = false;
          if (remove) {
            isNotificationFound = this.removeNotification(key, notification);
          } else {
            isNotificationFound = this.updateNotification(key, notification);
          }
          if (isNotificationFound) {
            uniqueFilterKeys.add(getFilterKey(getFilter(key)));
          }
        });
      });
      uniqueFilterKeys.forEach((key) => {
        const notificationsResponse = this.getAggregated(getFilter(key));
        __privateGet(this, _emitter2).emit("notifications.list.updated", {
          data: notificationsResponse
        });
      });
    };
    __privateSet(this, _emitter2, emitter);
    updateEvents.forEach((event) => {
      __privateGet(this, _emitter2).on(event, this.handleNotificationEvent());
    });
    removeEvents.forEach((event) => {
      __privateGet(this, _emitter2).on(event, this.handleNotificationEvent({ remove: true }));
    });
    __privateSet(this, _cache2, new InMemoryCache());
  }
  getAggregated(filter) {
    const cacheKeys = __privateGet(this, _cache2).keys().filter((key) => {
      const parsedFilter = getFilter(key);
      return isSameFilter(parsedFilter, filter);
    });
    return cacheKeys.map((key) => __privateGet(this, _cache2).get(key)).reduce(
      (acc, el) => {
        if (!el) {
          return acc;
        }
        return {
          hasMore: el.hasMore,
          filter: el.filter,
          notifications: [...acc.notifications, ...el.notifications]
        };
      },
      { hasMore: false, filter: {}, notifications: [] }
    );
  }
  has(args) {
    return __privateGet(this, _cache2).get(getCacheKey(args)) !== void 0;
  }
  set(args, data) {
    __privateGet(this, _cache2).set(getCacheKey(args), data);
  }
  update(args, data) {
    this.set(args, data);
    const notificationsResponse = this.getAggregated(getFilter(getCacheKey(args)));
    __privateGet(this, _emitter2).emit("notifications.list.updated", {
      data: notificationsResponse
    });
  }
  getAll(args) {
    if (this.has(args)) {
      return this.getAggregated({
        tags: args.tags,
        data: args.data,
        read: args.read,
        snoozed: args.snoozed,
        archived: args.archived
      });
    }
  }
  /**
   * Get unique notifications based on specified filter fields.
   * The same tags and data can be applied to multiple filters which means that the same notification can be duplicated.
   */
  getUniqueNotifications({
    tags,
    read: read2,
    data
  }) {
    const keys = __privateGet(this, _cache2).keys();
    const uniqueNotifications = /* @__PURE__ */ new Map();
    keys.forEach((key) => {
      const filter = getFilter(key);
      if (areTagsEqual(tags, filter.tags) && areDataEqual(data, filter.data)) {
        const value = __privateGet(this, _cache2).get(key);
        if (!value) {
          return;
        }
        value.notifications.filter((el) => typeof read2 === "undefined" || read2 === el.isRead).forEach((notification) => uniqueNotifications.set(notification.id, notification));
      }
    });
    return Array.from(uniqueNotifications.values());
  }
  clear(filter) {
    const keys = __privateGet(this, _cache2).keys();
    keys.forEach((key) => {
      if (isSameFilter(getFilter(key), filter)) {
        __privateGet(this, _cache2).remove(key);
      }
    });
  }
  clearAll() {
    __privateGet(this, _cache2).clear();
  }
};
_emitter2 = new WeakMap();
_cache2 = new WeakMap();

// src/notifications/notifications.ts
var _useCache;
var Notifications = class extends BaseModule {
  constructor({
    useCache,
    inboxServiceInstance,
    eventEmitterInstance
  }) {
    super({
      eventEmitterInstance,
      inboxServiceInstance
    });
    __privateAdd(this, _useCache);
    this.cache = new NotificationsCache({
      emitter: eventEmitterInstance
    });
    __privateSet(this, _useCache, useCache);
  }
  list() {
    return __async(this, arguments, function* (_a = {}) {
      var _b = _a, { limit = 10 } = _b, restOptions = __objRest(_b, ["limit"]);
      return this.callWithSession(() => __async(this, null, function* () {
        const args = __spreadValues({ limit }, restOptions);
        try {
          const shouldUseCache = "useCache" in args ? args.useCache : __privateGet(this, _useCache);
          let data = shouldUseCache ? this.cache.getAll(args) : void 0;
          this._emitter.emit("notifications.list.pending", { args, data });
          if (!data) {
            const response = yield this._inboxService.fetchNotifications(__spreadValues({
              limit
            }, restOptions));
            data = {
              hasMore: response.hasMore,
              filter: response.filter,
              notifications: response.data.map((el) => new Notification(el, this._emitter, this._inboxService))
            };
            if (shouldUseCache) {
              this.cache.set(args, data);
              data = this.cache.getAll(args);
            }
          }
          this._emitter.emit("notifications.list.resolved", { args, data });
          return { data };
        } catch (error) {
          this._emitter.emit("notifications.list.resolved", { args, error });
          return { error: new NovuError("Failed to fetch notifications", error) };
        }
      }));
    });
  }
  count(args) {
    return __async(this, null, function* () {
      return this.callWithSession(() => __async(this, null, function* () {
        const filters = args && "filters" in args ? args.filters : [__spreadValues({}, args)];
        try {
          this._emitter.emit("notifications.count.pending", { args });
          const response = yield this._inboxService.count({
            filters
          });
          const data = args && "filters" in args ? { counts: response.data } : response.data[0];
          this._emitter.emit("notifications.count.resolved", {
            args,
            data
          });
          return { data };
        } catch (error) {
          this._emitter.emit("notifications.count.resolved", { args, error });
          return { error: new NovuError("Failed to count notifications", error) };
        }
      }));
    });
  }
  read(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return read({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  unread(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return unread({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  archive(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return archive({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  unarchive(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return unarchive({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  snooze(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return snooze({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  unsnooze(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return unsnooze({
            emitter: this._emitter,
            apiService: this._inboxService,
            args
          });
        })
      );
    });
  }
  completePrimary(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return completeAction({
            emitter: this._emitter,
            apiService: this._inboxService,
            args,
            actionType: "primary" /* PRIMARY */
          });
        })
      );
    });
  }
  completeSecondary(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return completeAction({
            emitter: this._emitter,
            apiService: this._inboxService,
            args,
            actionType: "secondary" /* SECONDARY */
          });
        })
      );
    });
  }
  revertPrimary(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return revertAction({
            emitter: this._emitter,
            apiService: this._inboxService,
            args,
            actionType: "primary" /* PRIMARY */
          });
        })
      );
    });
  }
  revertSecondary(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return revertAction({
            emitter: this._emitter,
            apiService: this._inboxService,
            args,
            actionType: "secondary" /* SECONDARY */
          });
        })
      );
    });
  }
  readAll() {
    return __async(this, arguments, function* ({
      tags,
      data
    } = {}) {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return readAll({
            emitter: this._emitter,
            inboxService: this._inboxService,
            notificationsCache: this.cache,
            tags,
            data
          });
        })
      );
    });
  }
  archiveAll() {
    return __async(this, arguments, function* ({
      tags,
      data
    } = {}) {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return archiveAll({
            emitter: this._emitter,
            inboxService: this._inboxService,
            notificationsCache: this.cache,
            tags,
            data
          });
        })
      );
    });
  }
  archiveAllRead() {
    return __async(this, arguments, function* ({
      tags,
      data
    } = {}) {
      return this.callWithSession(
        () => __async(this, null, function* () {
          return archiveAllRead({
            emitter: this._emitter,
            inboxService: this._inboxService,
            notificationsCache: this.cache,
            tags,
            data
          });
        })
      );
    });
  }
  clearCache({ filter } = {}) {
    if (filter) {
      return this.cache.clear(filter != null ? filter : {});
    }
    return this.cache.clearAll();
  }
  triggerHelloWorldEvent() {
    return __async(this, null, function* () {
      return this._inboxService.triggerHelloWorldEvent();
    });
  }
};
_useCache = new WeakMap();

// src/preferences/helpers.ts
var updatePreference = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  cache,
  useCache,
  args
}) {
  var _a;
  const { channels } = args;
  const workflowId = "workflowId" in args ? args.workflowId : (_a = args.preference.workflow) == null ? void 0 : _a.id;
  try {
    emitter.emit("preference.update.pending", {
      args,
      data: "preference" in args ? new Preference(
        __spreadProps(__spreadValues({}, args.preference), {
          channels: __spreadValues(__spreadValues({}, args.preference.channels), channels)
        }),
        {
          emitterInstance: emitter,
          inboxServiceInstance: apiService,
          cache,
          useCache
        }
      ) : void 0
    });
    let response;
    if (workflowId) {
      response = yield apiService.updateWorkflowPreferences({ workflowId, channels });
    } else {
      optimisticUpdateWorkflowPreferences({ emitter, apiService, cache, useCache, args });
      response = yield apiService.updateGlobalPreferences(channels);
    }
    const preference = new Preference(response, {
      emitterInstance: emitter,
      inboxServiceInstance: apiService,
      cache,
      useCache
    });
    emitter.emit("preference.update.resolved", { args, data: preference });
    return { data: preference };
  } catch (error) {
    emitter.emit("preference.update.resolved", { args, error });
    return { error: new NovuError("Failed to update preference", error) };
  }
});
var bulkUpdatePreference = (_0) => __async(void 0, [_0], function* ({
  emitter,
  apiService,
  cache,
  useCache,
  args
}) {
  const globalPreference = args.find((arg) => "preference" in arg && arg.preference.level === "global" /* GLOBAL */);
  if (globalPreference) {
    return { error: new NovuError("Global preference is not supported in bulk update", "") };
  }
  try {
    const optimisticallyUpdatedPreferences = args.map(
      (arg) => "preference" in arg ? new Preference(
        __spreadProps(__spreadValues({}, arg.preference), {
          channels: __spreadValues(__spreadValues({}, arg.preference.channels), arg.channels)
        }),
        {
          emitterInstance: emitter,
          inboxServiceInstance: apiService,
          cache,
          useCache
        }
      ) : void 0
    ).filter((el) => el !== void 0);
    emitter.emit("preferences.bulk_update.pending", {
      args,
      data: optimisticallyUpdatedPreferences
    });
    const preferencesToUpdate = args.map((arg) => {
      var _a, _b, _c, _d;
      return __spreadValues({
        workflowId: "workflowId" in arg ? arg.workflowId : (_d = (_c = (_a = arg.preference.workflow) == null ? void 0 : _a.id) != null ? _c : (_b = arg.preference.workflow) == null ? void 0 : _b.identifier) != null ? _d : ""
      }, arg.channels);
    });
    const response = yield apiService.bulkUpdatePreferences(preferencesToUpdate);
    const preferences = response.map(
      (el) => new Preference(el, {
        emitterInstance: emitter,
        inboxServiceInstance: apiService,
        cache,
        useCache
      })
    );
    emitter.emit("preferences.bulk_update.resolved", { args, data: preferences });
    return { data: preferences };
  } catch (error) {
    emitter.emit("preferences.bulk_update.resolved", { args, error });
    return { error: new NovuError("Failed to bulk update preferences", error) };
  }
});
var optimisticUpdateWorkflowPreferences = ({
  emitter,
  apiService,
  cache,
  useCache,
  args
}) => {
  const allPreferences = useCache ? cache == null ? void 0 : cache.getAll({}) : void 0;
  allPreferences == null ? void 0 : allPreferences.forEach((el) => {
    var _a, _b;
    if (el.level === "template" /* TEMPLATE */) {
      const mergedPreference = __spreadProps(__spreadValues({}, el), {
        channels: Object.entries(el.channels).reduce((acc, [key, value]) => {
          var _a2;
          const channelType = key;
          acc[channelType] = (_a2 = args.channels[channelType]) != null ? _a2 : value;
          return acc;
        }, {})
      });
      const updatedPreference = "preference" in args ? new Preference(mergedPreference, {
        emitterInstance: emitter,
        inboxServiceInstance: apiService,
        cache,
        useCache
      }) : void 0;
      if (updatedPreference) {
        emitter.emit("preference.update.pending", {
          args: {
            workflowId: (_b = (_a = el.workflow) == null ? void 0 : _a.id) != null ? _b : "",
            channels: updatedPreference.channels
          },
          data: updatedPreference
        });
      }
    }
  });
};

// src/preferences/preference.ts
var _emitter3, _apiService, _cache3, _useCache2;
var Preference = class {
  constructor(preference, {
    emitterInstance,
    inboxServiceInstance,
    cache,
    useCache
  }) {
    __privateAdd(this, _emitter3);
    __privateAdd(this, _apiService);
    __privateAdd(this, _cache3);
    __privateAdd(this, _useCache2);
    __privateSet(this, _emitter3, emitterInstance);
    __privateSet(this, _apiService, inboxServiceInstance);
    __privateSet(this, _cache3, cache);
    __privateSet(this, _useCache2, useCache);
    this.level = preference.level;
    this.enabled = preference.enabled;
    this.channels = preference.channels;
    this.workflow = preference.workflow;
  }
  update({
    channels,
    channelPreferences
  }) {
    var _a;
    return updatePreference({
      emitter: __privateGet(this, _emitter3),
      apiService: __privateGet(this, _apiService),
      cache: __privateGet(this, _cache3),
      useCache: __privateGet(this, _useCache2),
      args: {
        workflowId: (_a = this.workflow) == null ? void 0 : _a.id,
        channels: channels || channelPreferences,
        preference: this
      }
    });
  }
};
_emitter3 = new WeakMap();
_apiService = new WeakMap();
_cache3 = new WeakMap();
_useCache2 = new WeakMap();

// src/cache/preferences-cache.ts
var updateEvents2 = [
  "preference.update.pending",
  "preference.update.resolved",
  "preferences.bulk_update.pending",
  "preferences.bulk_update.resolved"
];
var excludeEmpty2 = ({ tags }) => Object.entries({ tags }).reduce((acc, [key, value]) => {
  if (value === null || value === void 0 || Array.isArray(value) && value.length === 0) {
    return acc;
  }
  acc[key] = value;
  return acc;
}, {});
var getCacheKey2 = ({ tags }) => {
  return JSON.stringify(excludeEmpty2({ tags }));
};
var _emitter4, _cache4;
var PreferencesCache = class {
  constructor({ emitterInstance }) {
    __privateAdd(this, _emitter4);
    __privateAdd(this, _cache4);
    this.updatePreference = (key, data) => {
      const preferences = __privateGet(this, _cache4).get(key);
      if (!preferences) {
        return false;
      }
      const index = preferences.findIndex(
        (el) => {
          var _a, _b;
          return ((_a = el.workflow) == null ? void 0 : _a.id) === ((_b = data.workflow) == null ? void 0 : _b.id) || el.level === data.level && data.level === "global" /* GLOBAL */;
        }
      );
      if (index === -1) {
        return false;
      }
      const updatedPreferences = [...preferences];
      updatedPreferences[index] = data;
      __privateGet(this, _cache4).set(key, updatedPreferences);
      return true;
    };
    this.handlePreferenceEvent = ({ data }) => {
      if (!data) {
        return;
      }
      const preferences = Array.isArray(data) ? data : [data];
      const uniqueFilterKeys = /* @__PURE__ */ new Set();
      __privateGet(this, _cache4).keys().forEach((key) => {
        preferences.forEach((preference) => {
          const hasUpdatedPreference = this.updatePreference(key, preference);
          const updatedPreference = __privateGet(this, _cache4).get(key);
          if (!hasUpdatedPreference || !updatedPreference) {
            return;
          }
          uniqueFilterKeys.add(key);
        });
      });
      uniqueFilterKeys.forEach((key) => {
        var _a;
        __privateGet(this, _emitter4).emit("preferences.list.updated", {
          data: (_a = __privateGet(this, _cache4).get(key)) != null ? _a : []
        });
      });
    };
    __privateSet(this, _emitter4, emitterInstance);
    updateEvents2.forEach((event) => {
      __privateGet(this, _emitter4).on(event, this.handlePreferenceEvent);
    });
    __privateSet(this, _cache4, new InMemoryCache());
  }
  has(args) {
    return __privateGet(this, _cache4).get(getCacheKey2(args)) !== void 0;
  }
  set(args, data) {
    __privateGet(this, _cache4).set(getCacheKey2(args), data);
  }
  getAll(args) {
    if (this.has(args)) {
      return __privateGet(this, _cache4).get(getCacheKey2(args));
    }
  }
  clearAll() {
    __privateGet(this, _cache4).clear();
  }
};
_emitter4 = new WeakMap();
_cache4 = new WeakMap();

// src/preferences/preferences.ts
var _useCache3;
var Preferences = class extends BaseModule {
  constructor({
    useCache,
    inboxServiceInstance,
    eventEmitterInstance
  }) {
    super({
      eventEmitterInstance,
      inboxServiceInstance
    });
    __privateAdd(this, _useCache3);
    this.cache = new PreferencesCache({
      emitterInstance: this._emitter
    });
    __privateSet(this, _useCache3, useCache);
  }
  list() {
    return __async(this, arguments, function* (args = {}) {
      return this.callWithSession(() => __async(this, null, function* () {
        try {
          let data = __privateGet(this, _useCache3) ? this.cache.getAll(args) : void 0;
          this._emitter.emit("preferences.list.pending", { args, data });
          if (!data) {
            const response = yield this._inboxService.fetchPreferences(args.tags);
            data = response.map(
              (el) => new Preference(el, {
                emitterInstance: this._emitter,
                inboxServiceInstance: this._inboxService,
                cache: this.cache,
                useCache: __privateGet(this, _useCache3)
              })
            );
            if (__privateGet(this, _useCache3)) {
              this.cache.set(args, data);
              data = this.cache.getAll(args);
            }
          }
          this._emitter.emit("preferences.list.resolved", { args, data });
          return { data };
        } catch (error) {
          this._emitter.emit("preferences.list.resolved", { args, error });
          throw error;
        }
      }));
    });
  }
  update(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => updatePreference({
          emitter: this._emitter,
          apiService: this._inboxService,
          cache: this.cache,
          useCache: __privateGet(this, _useCache3),
          args
        })
      );
    });
  }
  bulkUpdate(args) {
    return __async(this, null, function* () {
      return this.callWithSession(
        () => bulkUpdatePreference({
          emitter: this._emitter,
          apiService: this._inboxService,
          cache: this.cache,
          useCache: __privateGet(this, _useCache3),
          args
        })
      );
    });
  }
};
_useCache3 = new WeakMap();

// src/session/session.ts
var _emitter5, _inboxService2, _options;
var Session = class {
  constructor(options, inboxServiceInstance, eventEmitterInstance) {
    __privateAdd(this, _emitter5);
    __privateAdd(this, _inboxService2);
    __privateAdd(this, _options);
    __privateSet(this, _emitter5, eventEmitterInstance);
    __privateSet(this, _inboxService2, inboxServiceInstance);
    __privateSet(this, _options, options);
  }
  get applicationIdentifier() {
    return __privateGet(this, _options).applicationIdentifier;
  }
  get subscriberId() {
    var _a;
    return (_a = __privateGet(this, _options).subscriber) == null ? void 0 : _a.subscriberId;
  }
  handleApplicationIdentifier(method, identifier) {
    if (typeof window === "undefined" || !window.localStorage) {
      return null;
    }
    const key = "novu_keyless_application_identifier";
    switch (method) {
      case "get": {
        return window.localStorage.getItem(key);
      }
      case "store": {
        if (identifier) {
          window.localStorage.setItem(key, identifier);
        }
        return null;
      }
      case "delete": {
        window.localStorage.removeItem(key);
        return null;
      }
      default:
        return null;
    }
  }
  initialize(options) {
    return __async(this, null, function* () {
      var _a, _b;
      try {
        if (options) {
          __privateSet(this, _options, options);
        }
        const { subscriber, subscriberHash, applicationIdentifier } = __privateGet(this, _options);
        let finalApplicationIdentifier = applicationIdentifier;
        if (!finalApplicationIdentifier) {
          const storedAppId = this.handleApplicationIdentifier("get");
          if (storedAppId) {
            finalApplicationIdentifier = storedAppId;
          }
        } else {
          this.handleApplicationIdentifier("delete");
        }
        __privateGet(this, _emitter5).emit("session.initialize.pending", { args: __privateGet(this, _options) });
        const response = yield __privateGet(this, _inboxService2).initializeSession({
          applicationIdentifier: finalApplicationIdentifier,
          subscriberHash,
          subscriber
        });
        if ((_a = response == null ? void 0 : response.applicationIdentifier) == null ? void 0 : _a.startsWith("pk_keyless_")) {
          this.handleApplicationIdentifier("store", response.applicationIdentifier);
        }
        if (!((_b = response == null ? void 0 : response.applicationIdentifier) == null ? void 0 : _b.startsWith("pk_keyless_"))) {
          this.handleApplicationIdentifier("delete");
        }
        __privateGet(this, _emitter5).emit("session.initialize.resolved", { args: __privateGet(this, _options), data: response });
      } catch (error) {
        __privateGet(this, _emitter5).emit("session.initialize.resolved", { args: __privateGet(this, _options), error });
      }
    });
  }
};
_emitter5 = new WeakMap();
_inboxService2 = new WeakMap();
_options = new WeakMap();
var PRODUCTION_SOCKET_URL = "https://ws.novu.co";
var NOTIFICATION_RECEIVED = "notifications.notification_received";
var UNSEEN_COUNT_CHANGED = "notifications.unseen_count_changed";
var UNREAD_COUNT_CHANGED = "notifications.unread_count_changed";
var mapToNotification = ({
  _id,
  content,
  read: read2,
  archived,
  snoozedUntil,
  deliveredAt,
  createdAt,
  lastReadDate,
  archivedAt,
  channel,
  subscriber,
  subject,
  avatar,
  cta,
  tags,
  data,
  workflow
}) => {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  const to = {
    id: subscriber == null ? void 0 : subscriber._id,
    subscriberId: subscriber == null ? void 0 : subscriber.subscriberId,
    firstName: subscriber == null ? void 0 : subscriber.firstName,
    lastName: subscriber == null ? void 0 : subscriber.lastName,
    avatar: subscriber == null ? void 0 : subscriber.avatar,
    locale: subscriber == null ? void 0 : subscriber.locale,
    data: subscriber == null ? void 0 : subscriber.data,
    timezone: subscriber == null ? void 0 : subscriber.timezone,
    email: subscriber == null ? void 0 : subscriber.email,
    phone: subscriber == null ? void 0 : subscriber.phone
  };
  const primaryCta = (_b = (_a = cta.action) == null ? void 0 : _a.buttons) == null ? void 0 : _b.find((button) => button.type === "primary" /* PRIMARY */);
  const secondaryCta = (_d = (_c = cta.action) == null ? void 0 : _c.buttons) == null ? void 0 : _d.find((button) => button.type === "secondary" /* SECONDARY */);
  const actionType = (_f = (_e = cta.action) == null ? void 0 : _e.result) == null ? void 0 : _f.type;
  const actionStatus = (_g = cta.action) == null ? void 0 : _g.status;
  return __spreadProps(__spreadValues(__spreadValues({
    id: _id,
    subject,
    body: content,
    to,
    isRead: read2,
    isArchived: archived,
    isSnoozed: !!snoozedUntil
  }, deliveredAt && {
    deliveredAt
  }), snoozedUntil && {
    snoozedUntil
  }), {
    createdAt,
    readAt: lastReadDate,
    archivedAt,
    avatar,
    primaryAction: primaryCta && {
      label: primaryCta.content,
      isCompleted: actionType === "primary" /* PRIMARY */ && actionStatus === "done" /* DONE */,
      redirect: primaryCta.url ? {
        target: primaryCta.target,
        url: primaryCta.url
      } : void 0
    },
    secondaryAction: secondaryCta && {
      label: secondaryCta.content,
      isCompleted: actionType === "secondary" /* SECONDARY */ && actionStatus === "done" /* DONE */,
      redirect: secondaryCta.url ? {
        target: secondaryCta.target,
        url: secondaryCta.url
      } : void 0
    },
    channelType: channel,
    tags,
    redirect: ((_h = cta.data) == null ? void 0 : _h.url) ? {
      url: cta.data.url,
      target: cta.data.target
    } : void 0,
    data,
    workflow
  });
};
var _token, _emitter6, _socketIo, _socketUrl, _notificationReceived, _unseenCountChanged, _unreadCountChanged, _Socket_instances, initializeSocket_fn, handleConnectSocket_fn, handleDisconnectSocket_fn;
var Socket = class extends BaseModule {
  constructor({
    socketUrl,
    inboxServiceInstance,
    eventEmitterInstance
  }) {
    super({
      eventEmitterInstance,
      inboxServiceInstance
    });
    __privateAdd(this, _Socket_instances);
    __privateAdd(this, _token);
    __privateAdd(this, _emitter6);
    __privateAdd(this, _socketIo);
    __privateAdd(this, _socketUrl);
    __privateAdd(this, _notificationReceived, ({ message }) => {
      __privateGet(this, _emitter6).emit(NOTIFICATION_RECEIVED, {
        result: new Notification(mapToNotification(message), __privateGet(this, _emitter6), this._inboxService)
      });
    });
    __privateAdd(this, _unseenCountChanged, ({ unseenCount }) => {
      __privateGet(this, _emitter6).emit(UNSEEN_COUNT_CHANGED, {
        result: unseenCount
      });
    });
    __privateAdd(this, _unreadCountChanged, ({ unreadCount }) => {
      __privateGet(this, _emitter6).emit(UNREAD_COUNT_CHANGED, {
        result: unreadCount
      });
    });
    __privateSet(this, _emitter6, eventEmitterInstance);
    __privateSet(this, _socketUrl, socketUrl != null ? socketUrl : PRODUCTION_SOCKET_URL);
  }
  onSessionSuccess({ token }) {
    __privateSet(this, _token, token);
  }
  isSocketEvent(eventName) {
    return eventName === NOTIFICATION_RECEIVED || eventName === UNSEEN_COUNT_CHANGED || eventName === UNREAD_COUNT_CHANGED;
  }
  connect() {
    return __async(this, null, function* () {
      if (__privateGet(this, _token)) {
        return __privateMethod(this, _Socket_instances, handleConnectSocket_fn).call(this);
      }
      return this.callWithSession(__privateMethod(this, _Socket_instances, handleConnectSocket_fn).bind(this));
    });
  }
  disconnect() {
    return __async(this, null, function* () {
      if (__privateGet(this, _socketIo)) {
        return __privateMethod(this, _Socket_instances, handleDisconnectSocket_fn).call(this);
      }
      return this.callWithSession(__privateMethod(this, _Socket_instances, handleDisconnectSocket_fn).bind(this));
    });
  }
};
_token = new WeakMap();
_emitter6 = new WeakMap();
_socketIo = new WeakMap();
_socketUrl = new WeakMap();
_notificationReceived = new WeakMap();
_unseenCountChanged = new WeakMap();
_unreadCountChanged = new WeakMap();
_Socket_instances = new WeakSet();
initializeSocket_fn = function() {
  return __async(this, null, function* () {
    var _a, _b, _c;
    if (!!__privateGet(this, _socketIo)) {
      return;
    }
    const args = { socketUrl: __privateGet(this, _socketUrl) };
    __privateGet(this, _emitter6).emit("socket.connect.pending", { args });
    __privateSet(this, _socketIo, io(__privateGet(this, _socketUrl), {
      reconnectionDelayMax: 1e4,
      transports: ["websocket"],
      query: {
        token: `${__privateGet(this, _token)}`
      }
    }));
    __privateGet(this, _socketIo).on("connect", () => {
      __privateGet(this, _emitter6).emit("socket.connect.resolved", { args });
    });
    __privateGet(this, _socketIo).on("connect_error", (error) => {
      __privateGet(this, _emitter6).emit("socket.connect.resolved", { args, error });
    });
    (_a = __privateGet(this, _socketIo)) == null ? void 0 : _a.on("notification_received" /* RECEIVED */, __privateGet(this, _notificationReceived));
    (_b = __privateGet(this, _socketIo)) == null ? void 0 : _b.on("unseen_count_changed" /* UNSEEN */, __privateGet(this, _unseenCountChanged));
    (_c = __privateGet(this, _socketIo)) == null ? void 0 : _c.on("unread_count_changed" /* UNREAD */, __privateGet(this, _unreadCountChanged));
  });
};
handleConnectSocket_fn = function() {
  return __async(this, null, function* () {
    try {
      yield __privateMethod(this, _Socket_instances, initializeSocket_fn).call(this);
      return {};
    } catch (error) {
      return { error: new NovuError("Failed to initialize the socket", error) };
    }
  });
};
handleDisconnectSocket_fn = function() {
  return __async(this, null, function* () {
    var _a;
    try {
      (_a = __privateGet(this, _socketIo)) == null ? void 0 : _a.disconnect();
      __privateSet(this, _socketIo, void 0);
      return {};
    } catch (error) {
      return { error: new NovuError("Failed to disconnect from the socket", error) };
    }
  });
};
var PRODUCTION_SOCKET_URL2 = "wss://socket.novu.co";
var NOTIFICATION_RECEIVED2 = "notifications.notification_received";
var UNSEEN_COUNT_CHANGED2 = "notifications.unseen_count_changed";
var UNREAD_COUNT_CHANGED2 = "notifications.unread_count_changed";
var mapToNotification2 = ({
  _id,
  content,
  read: read2,
  archived,
  snoozedUntil,
  deliveredAt,
  createdAt,
  lastReadDate,
  archivedAt,
  channel,
  subscriber,
  subject,
  avatar,
  cta,
  tags,
  data,
  workflow
}) => {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  const to = {
    id: subscriber == null ? void 0 : subscriber._id,
    subscriberId: subscriber == null ? void 0 : subscriber.subscriberId,
    firstName: subscriber == null ? void 0 : subscriber.firstName,
    lastName: subscriber == null ? void 0 : subscriber.lastName,
    avatar: subscriber == null ? void 0 : subscriber.avatar,
    locale: subscriber == null ? void 0 : subscriber.locale,
    data: subscriber == null ? void 0 : subscriber.data,
    timezone: subscriber == null ? void 0 : subscriber.timezone,
    email: subscriber == null ? void 0 : subscriber.email,
    phone: subscriber == null ? void 0 : subscriber.phone
  };
  const primaryCta = (_b = (_a = cta.action) == null ? void 0 : _a.buttons) == null ? void 0 : _b.find((button) => button.type === "primary" /* PRIMARY */);
  const secondaryCta = (_d = (_c = cta.action) == null ? void 0 : _c.buttons) == null ? void 0 : _d.find((button) => button.type === "secondary" /* SECONDARY */);
  const actionType = (_f = (_e = cta.action) == null ? void 0 : _e.result) == null ? void 0 : _f.type;
  const actionStatus = (_g = cta.action) == null ? void 0 : _g.status;
  return __spreadProps(__spreadValues(__spreadValues({
    id: _id,
    subject,
    body: content,
    to,
    isRead: read2,
    isArchived: archived,
    isSnoozed: !!snoozedUntil
  }, deliveredAt && {
    deliveredAt
  }), snoozedUntil && {
    snoozedUntil
  }), {
    createdAt,
    readAt: lastReadDate,
    archivedAt,
    avatar,
    primaryAction: primaryCta && {
      label: primaryCta.content,
      isCompleted: actionType === "primary" /* PRIMARY */ && actionStatus === "done" /* DONE */,
      redirect: primaryCta.url ? {
        target: primaryCta.target,
        url: primaryCta.url
      } : void 0
    },
    secondaryAction: secondaryCta && {
      label: secondaryCta.content,
      isCompleted: actionType === "secondary" /* SECONDARY */ && actionStatus === "done" /* DONE */,
      redirect: secondaryCta.url ? {
        target: secondaryCta.target,
        url: secondaryCta.url
      } : void 0
    },
    channelType: channel,
    tags,
    redirect: ((_h = cta.data) == null ? void 0 : _h.url) ? {
      url: cta.data.url,
      target: cta.data.target
    } : void 0,
    data,
    workflow
  });
};
var _token2, _emitter7, _partySocket, _socketUrl2, _notificationReceived2, _unseenCountChanged2, _unreadCountChanged2, _handleMessage, _PartySocketClient_instances, initializeSocket_fn2, handleConnectSocket_fn2, handleDisconnectSocket_fn2;
var PartySocketClient = class extends BaseModule {
  constructor({
    socketUrl,
    inboxServiceInstance,
    eventEmitterInstance
  }) {
    super({
      eventEmitterInstance,
      inboxServiceInstance
    });
    __privateAdd(this, _PartySocketClient_instances);
    __privateAdd(this, _token2);
    __privateAdd(this, _emitter7);
    __privateAdd(this, _partySocket);
    __privateAdd(this, _socketUrl2);
    __privateAdd(this, _notificationReceived2, (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.event === "notification_received" /* RECEIVED */) {
          __privateGet(this, _emitter7).emit(NOTIFICATION_RECEIVED2, {
            result: new Notification(mapToNotification2(data.data.message), __privateGet(this, _emitter7), this._inboxService)
          });
        }
      } catch (error) {
        console.log("error", error);
      }
    });
    __privateAdd(this, _unseenCountChanged2, (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.event === "unseen_count_changed" /* UNSEEN */) {
          __privateGet(this, _emitter7).emit(UNSEEN_COUNT_CHANGED2, {
            result: data.data.unseenCount
          });
        }
      } catch (error) {
      }
    });
    __privateAdd(this, _unreadCountChanged2, (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.event === "unread_count_changed" /* UNREAD */) {
          __privateGet(this, _emitter7).emit(UNREAD_COUNT_CHANGED2, {
            result: data.data.unreadCount
          });
        }
      } catch (error) {
      }
    });
    __privateAdd(this, _handleMessage, (event) => {
      try {
        const data = JSON.parse(event.data);
        switch (data.event) {
          case "notification_received" /* RECEIVED */:
            __privateGet(this, _notificationReceived2).call(this, event);
            break;
          case "unseen_count_changed" /* UNSEEN */:
            __privateGet(this, _unseenCountChanged2).call(this, event);
            break;
          case "unread_count_changed" /* UNREAD */:
            __privateGet(this, _unreadCountChanged2).call(this, event);
            break;
          default:
        }
      } catch (error) {
      }
    });
    __privateSet(this, _emitter7, eventEmitterInstance);
    __privateSet(this, _socketUrl2, socketUrl != null ? socketUrl : PRODUCTION_SOCKET_URL2);
  }
  onSessionSuccess({ token }) {
    __privateSet(this, _token2, token);
  }
  isSocketEvent(eventName) {
    return eventName === NOTIFICATION_RECEIVED2 || eventName === UNSEEN_COUNT_CHANGED2 || eventName === UNREAD_COUNT_CHANGED2;
  }
  connect() {
    return __async(this, null, function* () {
      if (__privateGet(this, _token2)) {
        return __privateMethod(this, _PartySocketClient_instances, handleConnectSocket_fn2).call(this);
      }
      return this.callWithSession(__privateMethod(this, _PartySocketClient_instances, handleConnectSocket_fn2).bind(this));
    });
  }
  disconnect() {
    return __async(this, null, function* () {
      if (__privateGet(this, _partySocket)) {
        return __privateMethod(this, _PartySocketClient_instances, handleDisconnectSocket_fn2).call(this);
      }
      return this.callWithSession(__privateMethod(this, _PartySocketClient_instances, handleDisconnectSocket_fn2).bind(this));
    });
  }
};
_token2 = new WeakMap();
_emitter7 = new WeakMap();
_partySocket = new WeakMap();
_socketUrl2 = new WeakMap();
_notificationReceived2 = new WeakMap();
_unseenCountChanged2 = new WeakMap();
_unreadCountChanged2 = new WeakMap();
_handleMessage = new WeakMap();
_PartySocketClient_instances = new WeakSet();
initializeSocket_fn2 = function() {
  return __async(this, null, function* () {
    if (__privateGet(this, _partySocket)) {
      return;
    }
    const args = { socketUrl: __privateGet(this, _socketUrl2) };
    __privateGet(this, _emitter7).emit("socket.connect.pending", { args });
    const url = new URL(__privateGet(this, _socketUrl2));
    url.searchParams.set("token", __privateGet(this, _token2));
    __privateSet(this, _partySocket, new WebSocket(url.toString()));
    __privateGet(this, _partySocket).addEventListener("open", () => {
      __privateGet(this, _emitter7).emit("socket.connect.resolved", { args });
    });
    __privateGet(this, _partySocket).addEventListener("error", (error) => {
      __privateGet(this, _emitter7).emit("socket.connect.resolved", { args, error });
    });
    __privateGet(this, _partySocket).addEventListener("message", __privateGet(this, _handleMessage));
  });
};
handleConnectSocket_fn2 = function() {
  return __async(this, null, function* () {
    try {
      yield __privateMethod(this, _PartySocketClient_instances, initializeSocket_fn2).call(this);
      return {};
    } catch (error) {
      return { error: new NovuError("Failed to initialize the PartySocket", error) };
    }
  });
};
handleDisconnectSocket_fn2 = function() {
  return __async(this, null, function* () {
    var _a;
    try {
      (_a = __privateGet(this, _partySocket)) == null ? void 0 : _a.close();
      __privateSet(this, _partySocket, void 0);
      return {};
    } catch (error) {
      return { error: new NovuError("Failed to disconnect from the PartySocket", error) };
    }
  });
};

// src/ws/socket-factory.ts
var PARTY_SOCKET_URLS = [
  "wss://eu.socket.novu.co",
  PRODUCTION_SOCKET_URL2,
  "wss://socket.novu-staging.co",
  "wss://socket-worker-local.cli-shortener.workers.dev"
];
var URL_TRANSFORMATIONS = {
  "https://eu.ws.novu.co": "wss://eu.socket.novu.co",
  "https://ws.novu.co": PRODUCTION_SOCKET_URL2,
  "https://dev.ws.novu.co": "wss://socket.novu-staging.co"
};
function transformSocketUrl(socketUrl) {
  if (!socketUrl) return PRODUCTION_SOCKET_URL2;
  return URL_TRANSFORMATIONS[socketUrl] || socketUrl;
}
function shouldUsePartySocket(socketUrl) {
  return !socketUrl || PARTY_SOCKET_URLS.includes(socketUrl);
}
function createSocket({
  socketUrl,
  inboxServiceInstance,
  eventEmitterInstance
}) {
  const transformedSocketUrl = transformSocketUrl(socketUrl);
  const socketType = shouldUsePartySocket(transformedSocketUrl) ? "partysocket" /* PARTY_SOCKET */ : "socket.io" /* SOCKET_IO */;
  switch (socketType) {
    case "partysocket" /* PARTY_SOCKET */:
      return new PartySocketClient({
        socketUrl: transformedSocketUrl,
        inboxServiceInstance,
        eventEmitterInstance
      });
    case "socket.io" /* SOCKET_IO */:
    default:
      return new Socket({
        socketUrl: transformedSocketUrl,
        inboxServiceInstance,
        eventEmitterInstance
      });
  }
}

// src/novu.ts
var _emitter8, _session, _inboxService3, _currentSubscriberId;
var Novu = class {
  constructor(options) {
    __privateAdd(this, _emitter8);
    __privateAdd(this, _session);
    __privateAdd(this, _inboxService3);
    __privateAdd(this, _currentSubscriberId);
    var _a, _b;
    __privateSet(this, _inboxService3, new InboxService({
      apiUrl: options.apiUrl || options.backendUrl,
      userAgent: options.__userAgent
    }));
    __privateSet(this, _emitter8, new NovuEventEmitter());
    __privateSet(this, _session, new Session(
      {
        applicationIdentifier: options.applicationIdentifier || "",
        subscriberHash: options.subscriberHash,
        subscriber: buildSubscriber(options)
      },
      __privateGet(this, _inboxService3),
      __privateGet(this, _emitter8)
    ));
    const initialSubscriber = buildSubscriber(options);
    __privateSet(this, _currentSubscriberId, initialSubscriber.subscriberId);
    __privateGet(this, _session).initialize();
    this.notifications = new Notifications({
      useCache: (_a = options.useCache) != null ? _a : true,
      inboxServiceInstance: __privateGet(this, _inboxService3),
      eventEmitterInstance: __privateGet(this, _emitter8)
    });
    this.preferences = new Preferences({
      useCache: (_b = options.useCache) != null ? _b : true,
      inboxServiceInstance: __privateGet(this, _inboxService3),
      eventEmitterInstance: __privateGet(this, _emitter8)
    });
    this.socket = createSocket({
      socketUrl: options.socketUrl,
      eventEmitterInstance: __privateGet(this, _emitter8),
      inboxServiceInstance: __privateGet(this, _inboxService3)
    });
    this.on = (eventName, listener) => {
      if (this.socket.isSocketEvent(eventName)) {
        this.socket.connect();
      }
      const cleanup = __privateGet(this, _emitter8).on(eventName, listener);
      return () => {
        cleanup();
      };
    };
    this.off = (eventName, listener) => {
      __privateGet(this, _emitter8).off(eventName, listener);
    };
  }
  get applicationIdentifier() {
    return __privateGet(this, _session).applicationIdentifier;
  }
  get subscriberId() {
    return __privateGet(this, _session).subscriberId;
  }
  changeSubscriber(options) {
    return __async(this, null, function* () {
      if (__privateGet(this, _currentSubscriberId) === options.subscriber.subscriberId) {
        return;
      }
      yield __privateGet(this, _session).initialize({
        applicationIdentifier: __privateGet(this, _session).applicationIdentifier || "",
        subscriberHash: options.subscriberHash,
        subscriber: options.subscriber
      });
      __privateSet(this, _currentSubscriberId, options.subscriber.subscriberId);
    });
  }
};
_emitter8 = new WeakMap();
_session = new WeakMap();
_inboxService3 = new WeakMap();
_currentSubscriberId = new WeakMap();
function buildSubscriber(options) {
  if (options.subscriber) {
    return typeof options.subscriber === "string" ? { subscriberId: options.subscriber } : options.subscriber;
  }
  if (options.subscriberId) {
    return { subscriberId: options.subscriberId };
  }
  return { subscriberId: "" };
}

export { ChannelType, DEFAULT_API_VERSION, NotificationStatus, Novu, PreferenceLevel, WebSocketEvent, areTagsEqual, isSameFilter };
