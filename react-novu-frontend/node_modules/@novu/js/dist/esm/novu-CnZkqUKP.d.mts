type HttpClientOptions = {
    apiVersion?: string;
    apiUrl?: string;
    userAgent?: string;
    headers?: Record<string, string>;
};

declare class NovuError extends Error {
    originalError: Error;
    constructor(message: string, originalError: unknown);
}

type ListPreferencesArgs = {
    tags?: string[];
};
type BasePreferenceArgs = {
    workflowId: string;
    channels: ChannelPreference;
};
type InstancePreferenceArgs = {
    preference: Preference;
    channels: ChannelPreference;
};
type UpdatePreferenceArgs = BasePreferenceArgs | InstancePreferenceArgs;

declare class PreferencesCache {
    #private;
    constructor({ emitterInstance }: {
        emitterInstance: NovuEventEmitter;
    });
    private updatePreference;
    private handlePreferenceEvent;
    has(args: ListPreferencesArgs): boolean;
    set(args: ListPreferencesArgs, data: Preference[]): void;
    getAll(args: ListPreferencesArgs): Preference[] | undefined;
    clearAll(): void;
}

type PreferenceLike = Pick<Preference, 'level' | 'enabled' | 'channels' | 'workflow'>;
declare class Preference {
    #private;
    readonly level: PreferenceLevel;
    readonly enabled: boolean;
    readonly channels: ChannelPreference;
    readonly workflow?: Workflow;
    constructor(preference: PreferenceLike, { emitterInstance, inboxServiceInstance, cache, useCache, }: {
        emitterInstance: NovuEventEmitter;
        inboxServiceInstance: InboxService;
        cache: PreferencesCache;
        useCache: boolean;
    });
    update({ channels, channelPreferences, }: Prettify<Pick<UpdatePreferenceArgs, 'channels'> & {
        /** @deprecated Use channels instead */
        channelPreferences?: ChannelPreference;
    }>): Result<Preference>;
}

declare global {
    /**
     * If you want to provide custom types for the notification.data object,
     * simply redeclare this rule in the global namespace.
     * Every notification object will use the provided type.
     */
    interface NotificationData {
        [k: string]: unknown;
    }
}
declare enum NotificationStatus {
    READ = "read",
    SEEN = "seen",
    SNOOZED = "snoozed",
    UNREAD = "unread",
    UNSEEN = "unseen",
    UNSNOOZED = "unsnoozed"
}
declare enum PreferenceLevel {
    GLOBAL = "global",
    TEMPLATE = "template"
}
declare enum ChannelType {
    IN_APP = "in_app",
    EMAIL = "email",
    SMS = "sms",
    CHAT = "chat",
    PUSH = "push"
}
declare enum WebSocketEvent {
    RECEIVED = "notification_received",
    UNREAD = "unread_count_changed",
    UNSEEN = "unseen_count_changed"
}
type Session = {
    token: string;
    totalUnreadCount: number;
    removeNovuBranding: boolean;
    isDevelopmentMode: boolean;
    maxSnoozeDurationHours: number;
    applicationIdentifier?: string;
};
type Subscriber = {
    id?: string;
    subscriberId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    avatar?: string;
    locale?: string;
    data?: Record<string, unknown>;
    timezone?: string;
};
type Redirect = {
    url: string;
    target?: '_self' | '_blank' | '_parent' | '_top' | '_unfencedTop';
};
declare enum ActionTypeEnum {
    PRIMARY = "primary",
    SECONDARY = "secondary"
}
type Action = {
    label: string;
    isCompleted: boolean;
    redirect?: Redirect;
};
type Workflow = {
    id: string;
    identifier: string;
    name: string;
    critical: boolean;
    tags?: string[];
};
type InboxNotification = {
    id: string;
    subject?: string;
    body: string;
    to: Subscriber;
    isRead: boolean;
    isArchived: boolean;
    isSnoozed: boolean;
    snoozedUntil?: string | null;
    deliveredAt?: string[];
    createdAt: string;
    readAt?: string | null;
    archivedAt?: string | null;
    avatar?: string;
    primaryAction?: Action;
    secondaryAction?: Action;
    channelType: ChannelType;
    tags?: string[];
    data?: NotificationData;
    redirect?: Redirect;
    workflow?: Workflow;
};
type NotificationFilter = {
    tags?: string[];
    read?: boolean;
    archived?: boolean;
    snoozed?: boolean;
    data?: Record<string, unknown>;
};
type ChannelPreference = {
    email?: boolean;
    sms?: boolean;
    in_app?: boolean;
    chat?: boolean;
    push?: boolean;
};
type PreferencesResponse = {
    level: PreferenceLevel;
    enabled: boolean;
    channels: ChannelPreference;
    overrides?: IPreferenceOverride[];
    workflow?: Workflow;
};
declare enum PreferenceOverrideSourceEnum {
    SUBSCRIBER = "subscriber",
    TEMPLATE = "template",
    WORKFLOW_OVERRIDE = "workflowOverride"
}
type IPreferenceOverride = {
    channel: ChannelType;
    source: PreferenceOverrideSourceEnum;
};
type Result<D = undefined, E = NovuError> = Promise<{
    data?: D;
    error?: E;
}>;
type KeylessNovuOptions = {} & {
    [K in string]?: never;
};
type StandardNovuOptions = {
    /** @deprecated Use apiUrl instead  */
    backendUrl?: string;
    /** @internal Should be used internally for testing purposes */
    __userAgent?: string;
    applicationIdentifier: string;
    subscriberHash?: string;
    apiUrl?: string;
    socketUrl?: string;
    useCache?: boolean;
} & ({
    /** @deprecated Use subscriber prop instead */
    subscriberId: string;
    subscriber?: never;
} | {
    subscriber: Subscriber | string;
    subscriberId?: never;
});
type NovuOptions = KeylessNovuOptions | StandardNovuOptions;
type Prettify<T> = {
    [K in keyof T]: T[K];
} & {};

type InboxServiceOptions = HttpClientOptions;
declare class InboxService {
    #private;
    isSessionInitialized: boolean;
    constructor(options?: InboxServiceOptions);
    initializeSession({ applicationIdentifier, subscriberHash, subscriber, }: {
        applicationIdentifier?: string;
        subscriberHash?: string;
        subscriber?: Subscriber;
    }): Promise<Session>;
    fetchNotifications({ after, archived, limit, offset, read, tags, snoozed, data, }: {
        tags?: string[];
        read?: boolean;
        archived?: boolean;
        snoozed?: boolean;
        limit?: number;
        after?: string;
        offset?: number;
        data?: Record<string, unknown>;
    }): Promise<{
        data: InboxNotification[];
        hasMore: boolean;
        filter: NotificationFilter;
    }>;
    count({ filters, }: {
        filters: Array<{
            tags?: string[];
            read?: boolean;
            archived?: boolean;
            data?: Record<string, unknown>;
        }>;
    }): Promise<{
        data: Array<{
            count: number;
            filter: NotificationFilter;
        }>;
    }>;
    read(notificationId: string): Promise<InboxNotification>;
    unread(notificationId: string): Promise<InboxNotification>;
    archive(notificationId: string): Promise<InboxNotification>;
    unarchive(notificationId: string): Promise<InboxNotification>;
    snooze(notificationId: string, snoozeUntil: string): Promise<InboxNotification>;
    unsnooze(notificationId: string): Promise<InboxNotification>;
    readAll({ tags, data }: {
        tags?: string[];
        data?: Record<string, unknown>;
    }): Promise<void>;
    archiveAll({ tags, data }: {
        tags?: string[];
        data?: Record<string, unknown>;
    }): Promise<void>;
    archiveAllRead({ tags, data }: {
        tags?: string[];
        data?: Record<string, unknown>;
    }): Promise<void>;
    completeAction({ actionType, notificationId, }: {
        notificationId: string;
        actionType: ActionTypeEnum;
    }): Promise<InboxNotification>;
    revertAction({ actionType, notificationId, }: {
        notificationId: string;
        actionType: ActionTypeEnum;
    }): Promise<InboxNotification>;
    fetchPreferences(tags?: string[]): Promise<PreferencesResponse[]>;
    bulkUpdatePreferences(preferences: Array<{
        workflowId: string;
    } & ChannelPreference>): Promise<PreferencesResponse[]>;
    updateGlobalPreferences(channels: ChannelPreference): Promise<PreferencesResponse>;
    updateWorkflowPreferences({ workflowId, channels, }: {
        workflowId: string;
        channels: ChannelPreference;
    }): Promise<PreferencesResponse>;
    triggerHelloWorldEvent(): Promise<any>;
}

declare class BaseModule {
    #private;
    protected _inboxService: InboxService;
    protected _emitter: NovuEventEmitter;
    constructor({ inboxServiceInstance, eventEmitterInstance, }: {
        inboxServiceInstance: InboxService;
        eventEmitterInstance: NovuEventEmitter;
    });
    protected onSessionSuccess(_: Session): void;
    protected onSessionError(_: unknown): void;
    callWithSession<T>(fn: () => Result<T>): Result<T>;
}

declare class Notification implements Pick<NovuEventEmitter, 'on'>, InboxNotification {
    #private;
    readonly id: InboxNotification['id'];
    readonly subject?: InboxNotification['subject'];
    readonly body: InboxNotification['body'];
    readonly to: InboxNotification['to'];
    readonly isRead: InboxNotification['isRead'];
    readonly isArchived: InboxNotification['isArchived'];
    readonly isSnoozed: InboxNotification['isSnoozed'];
    readonly snoozedUntil?: InboxNotification['snoozedUntil'];
    readonly deliveredAt?: InboxNotification['deliveredAt'];
    readonly createdAt: InboxNotification['createdAt'];
    readonly readAt?: InboxNotification['readAt'];
    readonly archivedAt?: InboxNotification['archivedAt'];
    readonly avatar?: InboxNotification['avatar'];
    readonly primaryAction?: InboxNotification['primaryAction'];
    readonly secondaryAction?: InboxNotification['secondaryAction'];
    readonly channelType: InboxNotification['channelType'];
    readonly tags: InboxNotification['tags'];
    readonly redirect: InboxNotification['redirect'];
    readonly data?: InboxNotification['data'];
    readonly workflow?: InboxNotification['workflow'];
    constructor(notification: InboxNotification, emitter: NovuEventEmitter, inboxService: InboxService);
    read(): Result<Notification>;
    unread(): Result<Notification>;
    archive(): Result<Notification>;
    unarchive(): Result<Notification>;
    snooze(snoozeUntil: string): Result<Notification>;
    unsnooze(): Result<Notification>;
    completePrimary(): Result<Notification>;
    completeSecondary(): Result<Notification>;
    revertPrimary(): Result<Notification>;
    revertSecondary(): Result<Notification>;
    on<Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>): () => void;
    /**
     * @deprecated
     * Use the cleanup function returned by the "on" method instead.
     */
    off<Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>): void;
}

type ListNotificationsArgs = {
    tags?: string[];
    read?: boolean;
    data?: Record<string, unknown>;
    archived?: boolean;
    snoozed?: boolean;
    limit?: number;
    after?: string;
    offset?: number;
    useCache?: boolean;
};
type ListNotificationsResponse = {
    notifications: Notification[];
    hasMore: boolean;
    filter: NotificationFilter;
};
type FilterCountArgs = {
    tags?: string[];
    data?: Record<string, unknown>;
    read?: boolean;
    archived?: boolean;
    snoozed?: boolean;
};
type FiltersCountArgs = {
    filters: Array<{
        tags?: string[];
        read?: boolean;
        archived?: boolean;
        snoozed?: boolean;
        data?: Record<string, unknown>;
    }>;
};
type CountArgs = undefined | FilterCountArgs | FiltersCountArgs;
type FilterCountResponse = {
    count: number;
    filter: NotificationFilter;
};
type FiltersCountResponse = {
    counts: Array<{
        count: number;
        filter: NotificationFilter;
    }>;
};
type CountResponse = FilterCountResponse | FiltersCountResponse;
type BaseArgs = {
    notificationId: string;
};
type InstanceArgs = {
    notification: Notification;
};
type ReadArgs = BaseArgs | InstanceArgs;
type UnreadArgs = BaseArgs | InstanceArgs;
type ArchivedArgs = BaseArgs | InstanceArgs;
type UnarchivedArgs = BaseArgs | InstanceArgs;
type SnoozeArgs = (BaseArgs | InstanceArgs) & {
    snoozeUntil: string;
};
type UnsnoozeArgs = BaseArgs | InstanceArgs;
type CompleteArgs = BaseArgs | InstanceArgs;
type RevertArgs = BaseArgs | InstanceArgs;

declare class NotificationsCache {
    #private;
    constructor({ emitter }: {
        emitter: NovuEventEmitter;
    });
    private updateNotification;
    private removeNotification;
    private handleNotificationEvent;
    private getAggregated;
    has(args: ListNotificationsArgs): boolean;
    set(args: ListNotificationsArgs, data: ListNotificationsResponse): void;
    update(args: ListNotificationsArgs, data: ListNotificationsResponse): void;
    getAll(args: ListNotificationsArgs): ListNotificationsResponse | undefined;
    /**
     * Get unique notifications based on specified filter fields.
     * The same tags and data can be applied to multiple filters which means that the same notification can be duplicated.
     */
    getUniqueNotifications({ tags, read, data, }: Pick<ListNotificationsArgs, 'tags' | 'read' | 'data'>): Array<Notification>;
    clear(filter: NotificationFilter): void;
    clearAll(): void;
}

declare class Notifications extends BaseModule {
    #private;
    readonly cache: NotificationsCache;
    constructor({ useCache, inboxServiceInstance, eventEmitterInstance, }: {
        useCache: boolean;
        inboxServiceInstance: InboxService;
        eventEmitterInstance: NovuEventEmitter;
    });
    list({ limit, ...restOptions }?: ListNotificationsArgs): Result<ListNotificationsResponse>;
    count(args?: FilterCountArgs): Result<FilterCountResponse>;
    count(args?: FiltersCountArgs): Result<FiltersCountResponse>;
    read(args: BaseArgs): Result<Notification>;
    read(args: InstanceArgs): Result<Notification>;
    unread(args: BaseArgs): Result<Notification>;
    unread(args: InstanceArgs): Result<Notification>;
    archive(args: BaseArgs): Result<Notification>;
    archive(args: InstanceArgs): Result<Notification>;
    unarchive(args: BaseArgs): Result<Notification>;
    unarchive(args: InstanceArgs): Result<Notification>;
    snooze(args: SnoozeArgs): Result<Notification>;
    unsnooze(args: BaseArgs): Result<Notification>;
    unsnooze(args: InstanceArgs): Result<Notification>;
    completePrimary(args: BaseArgs): Result<Notification>;
    completePrimary(args: InstanceArgs): Result<Notification>;
    completeSecondary(args: BaseArgs): Result<Notification>;
    completeSecondary(args: InstanceArgs): Result<Notification>;
    revertPrimary(args: BaseArgs): Result<Notification>;
    revertPrimary(args: InstanceArgs): Result<Notification>;
    revertSecondary(args: BaseArgs): Result<Notification>;
    revertSecondary(args: InstanceArgs): Result<Notification>;
    readAll({ tags, data, }?: {
        tags?: NotificationFilter['tags'];
        data?: Record<string, unknown>;
    }): Result<void>;
    archiveAll({ tags, data, }?: {
        tags?: NotificationFilter['tags'];
        data?: Record<string, unknown>;
    }): Result<void>;
    archiveAllRead({ tags, data, }?: {
        tags?: NotificationFilter['tags'];
        data?: Record<string, unknown>;
    }): Result<void>;
    clearCache({ filter }?: {
        filter?: NotificationFilter;
    }): void;
    triggerHelloWorldEvent(): Promise<any>;
}

type KeylessInitializeSessionArgs = {} & {
    [K in string]?: never;
};
type InitializeSessionArgs = KeylessInitializeSessionArgs | {
    applicationIdentifier: string;
    subscriber: Subscriber;
    subscriberHash?: string;
};

type NovuPendingEvent<A, D = undefined> = {
    args: A;
    data?: D;
};
type NovuResolvedEvent<A, D> = NovuPendingEvent<A, D> & {
    error?: unknown;
};
type EventName<T extends string> = `${T}.pending` | `${T}.resolved`;
type EventStatus<T extends string> = `${T extends `${infer _}.${infer __}.${infer V}` ? V : never}`;
type EventObject<K extends string, ARGS, DATA, EVENT_STATUS = EventStatus<K>> = EVENT_STATUS extends 'pending' ? NovuPendingEvent<ARGS, DATA> : NovuResolvedEvent<ARGS, DATA>;
type BaseEvents<T extends string, ARGS, DATA> = {
    [key in `${EventName<T>}`]: EventObject<key, ARGS, DATA>;
};
type SessionInitializeEvents = BaseEvents<'session.initialize', InitializeSessionArgs, Session>;
type NotificationsFetchEvents = BaseEvents<'notifications.list', ListNotificationsArgs, ListNotificationsResponse>;
type NotificationsFetchCountEvents = BaseEvents<'notifications.count', CountArgs, CountResponse>;
type NotificationReadEvents = BaseEvents<'notification.read', ReadArgs, Notification>;
type NotificationUnreadEvents = BaseEvents<'notification.unread', UnreadArgs, Notification>;
type NotificationArchiveEvents = BaseEvents<'notification.archive', ArchivedArgs, Notification>;
type NotificationUnarchiveEvents = BaseEvents<'notification.unarchive', UnarchivedArgs, Notification>;
type NotificationSnoozeEvents = BaseEvents<'notification.snooze', SnoozeArgs, Notification>;
type NotificationUnsnoozeEvents = BaseEvents<'notification.unsnooze', UnsnoozeArgs, Notification>;
type NotificationCompleteActionEvents = BaseEvents<'notification.complete_action', CompleteArgs, Notification>;
type NotificationRevertActionEvents = BaseEvents<'notification.revert_action', RevertArgs, Notification>;
type NotificationsReadAllEvents = BaseEvents<'notifications.read_all', {
    tags?: string[];
    data?: Record<string, unknown>;
}, Notification[]>;
type NotificationsArchivedAllEvents = BaseEvents<'notifications.archive_all', {
    tags?: string[];
    data?: Record<string, unknown>;
}, Notification[]>;
type NotificationsReadArchivedAllEvents = BaseEvents<'notifications.archive_all_read', {
    tags?: string[];
    data?: Record<string, unknown>;
}, Notification[]>;
type PreferencesFetchEvents = BaseEvents<'preferences.list', ListPreferencesArgs, Preference[]>;
type PreferenceUpdateEvents = BaseEvents<'preference.update', UpdatePreferenceArgs, Preference>;
type PreferencesBulkUpdateEvents = BaseEvents<'preferences.bulk_update', Array<UpdatePreferenceArgs>, Preference[]>;
type SocketConnectEvents = BaseEvents<'socket.connect', {
    socketUrl: string;
}, undefined>;
type NotificationReceivedEvent = `notifications.${WebSocketEvent.RECEIVED}`;
type NotificationUnseenEvent = `notifications.${WebSocketEvent.UNSEEN}`;
type NotificationUnreadEvent = `notifications.${WebSocketEvent.UNREAD}`;
type SocketEvents = {
    [key in NotificationReceivedEvent]: {
        result: Notification;
    };
} & {
    [key in NotificationUnseenEvent]: {
        result: number;
    };
} & {
    [key in NotificationUnreadEvent]: {
        result: number;
    };
};
/**
 * Events that are emitted by Novu Event Emitter.
 *
 * The event name consists of second pattern: module.action.status
 * - module: the name of the module
 * - action: the action that is being performed
 * - status: the status of the action, could be pending or resolved
 *
 * Each event has a corresponding payload that is associated with the event:
 * - pending: the args that are passed to the action and the optional optimistic value
 * - resolved: the args that are passed to the action and the result of the action or the error that is thrown
 */
type Events = SessionInitializeEvents & NotificationsFetchEvents & {
    'notifications.list.updated': {
        data: ListNotificationsResponse;
    };
} & NotificationsFetchCountEvents & PreferencesFetchEvents & {
    'preferences.list.updated': {
        data: Preference[];
    };
} & PreferenceUpdateEvents & PreferencesBulkUpdateEvents & SocketConnectEvents & SocketEvents & NotificationReadEvents & NotificationUnreadEvents & NotificationArchiveEvents & NotificationUnarchiveEvents & NotificationSnoozeEvents & NotificationUnsnoozeEvents & NotificationCompleteActionEvents & NotificationRevertActionEvents & NotificationsReadAllEvents & NotificationsArchivedAllEvents & NotificationsReadArchivedAllEvents;
type EventNames = keyof Events;
type SocketEventNames = keyof SocketEvents;
type EventHandler<T = unknown> = (event: T) => void;

declare class NovuEventEmitter {
    #private;
    constructor();
    on<Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>): () => void;
    off<Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>): void;
    emit<Key extends EventNames>(type: Key, event?: Events[Key]): void;
}

declare class Preferences extends BaseModule {
    #private;
    readonly cache: PreferencesCache;
    constructor({ useCache, inboxServiceInstance, eventEmitterInstance, }: {
        useCache: boolean;
        inboxServiceInstance: InboxService;
        eventEmitterInstance: NovuEventEmitter;
    });
    list(args?: ListPreferencesArgs): Result<Preference[]>;
    update(args: BasePreferenceArgs): Result<Preference>;
    update(args: InstancePreferenceArgs): Result<Preference>;
    bulkUpdate(args: Array<BasePreferenceArgs>): Result<Preference[]>;
    bulkUpdate(args: Array<InstancePreferenceArgs>): Result<Preference[]>;
}

interface BaseSocketInterface {
    isSocketEvent(eventName: string): eventName is SocketEventNames;
    connect(): Result<void>;
    disconnect(): Result<void>;
}

declare class Novu implements Pick<NovuEventEmitter, 'on'> {
    #private;
    readonly notifications: Notifications;
    readonly preferences: Preferences;
    readonly socket: BaseSocketInterface;
    on: <Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>) => () => void;
    /**
     * @deprecated
     * Use the cleanup function returned by the "on" method instead.
     */
    off: <Key extends EventNames>(eventName: Key, listener: EventHandler<Events[Key]>) => void;
    get applicationIdentifier(): string | undefined;
    get subscriberId(): string | undefined;
    constructor(options: NovuOptions);
    changeSubscriber(options: {
        subscriber: Subscriber;
        subscriberHash?: string;
    }): Promise<void>;
}

export { type ChannelPreference as C, type EventHandler as E, type FiltersCountResponse as F, type InboxNotification as I, type ListNotificationsResponse as L, type NotificationFilter as N, Preference as P, type SocketEventNames as S, WebSocketEvent as W, type Events as a, Novu as b, ChannelType as c, Notification as d, NotificationStatus as e, NovuError as f, type NovuOptions as g, type StandardNovuOptions as h, PreferenceLevel as i, type PreferencesResponse as j, type Subscriber as k };
