import { d as Notification, N as NotificationFilter, g as NovuOptions, b as Novu, P as Preference } from './novu-CnZkqUKP.js';

declare const appearanceKeys: readonly ["button", "input", "icon", "badge", "popoverContent", "popoverTrigger", "popoverClose", "dropdownContent", "dropdownTrigger", "dropdownItem", "dropdownItemLabel", "dropdownItemLabelContainer", "dropdownItemLeft__icon", "dropdownItemRight__icon", "dropdownItem__icon", "collapsible", "tooltipContent", "tooltipTrigger", "datePicker", "datePickerGrid", "datePickerGridRow", "datePickerGridCell", "datePickerGridCellTrigger", "datePickerTrigger", "datePickerGridHeader", "datePickerControl", "datePickerControlPrevTrigger", "datePickerControlNextTrigger", "datePickerControlPrevTrigger__icon", "datePickerControlNextTrigger__icon", "datePickerCalendar", "datePickerHeaderMonth", "datePickerCalendarDay__button", "timePicker", "timePicker__hourSelect", "timePicker__minuteSelect", "timePicker__periodSelect", "timePicker__separator", "timePickerHour__input", "timePickerMinute__input", "snoozeDatePicker", "snoozeDatePicker__actions", "snoozeDatePickerCancel__button", "snoozeDatePickerApply__button", "snoozeDatePicker__timePickerContainer", "snoozeDatePicker__timePickerLabel", "back__button", "skeletonText", "skeletonAvatar", "skeletonSwitch", "skeletonSwitchThumb", "tabsRoot", "tabsList", "tabsContent", "tabsTrigger", "dots", "root", "bellIcon", "lockIcon", "bellContainer", "bellDot", "preferences__button", "preferencesContainer", "inboxHeader", "loading", "inboxContent", "inbox__popoverTrigger", "inbox__popoverContent", "notificationListContainer", "notificationList", "notificationListEmptyNoticeContainer", "notificationListEmptyNoticeOverlay", "notificationListEmptyNoticeIcon", "notificationListEmptyNotice", "notificationList__skeleton", "notificationList__skeletonContent", "notificationList__skeletonItem", "notificationList__skeletonAvatar", "notificationList__skeletonText", "notificationListNewNotificationsNotice__button", "notification", "notificationContent", "notificationTextContainer", "notificationDot", "notificationSubject", "notificationSubject__strong", "notificationBody", "notificationBody__strong", "notificationBodyContainer", "notificationImage", "notificationImageLoadingFallback", "notificationDate", "notificationDateActionsContainer", "notificationDefaultActions", "notificationCustomActions", "notificationPrimaryAction__button", "notificationSecondaryAction__button", "notificationRead__button", "notificationUnread__button", "notificationArchive__button", "notificationUnarchive__button", "notificationSnooze__button", "notificationUnsnooze__button", "notificationRead__icon", "notificationUnread__icon", "notificationArchive__icon", "notificationUnarchive__icon", "notificationSnooze__icon", "notificationUnsnooze__icon", "notificationsTabs__tabsRoot", "notificationsTabs__tabsList", "notificationsTabs__tabsContent", "notificationsTabs__tabsTrigger", "notificationsTabsTriggerLabel", "notificationsTabsTriggerCount", "inboxStatus__title", "inboxStatus__dropdownTrigger", "inboxStatus__dropdownContent", "inboxStatus__dropdownItem", "inboxStatus__dropdownItemLabel", "inboxStatus__dropdownItemLabelContainer", "inboxStatus__dropdownItemLeft__icon", "inboxStatus__dropdownItemRight__icon", "inboxStatus__dropdownItem__icon", "inboxStatus__dropdownItemCheck__icon", "moreActionsContainer", "moreActions__dropdownTrigger", "moreActions__dropdownContent", "moreActions__dropdownItem", "moreActions__dropdownItemLabel", "moreActions__dropdownItemLeft__icon", "moreActions__dots", "moreTabs__button", "moreTabs__icon", "moreTabs__dropdownTrigger", "moreTabs__dropdownContent", "moreTabs__dropdownItem", "moreTabs__dropdownItemLabel", "moreTabs__dropdownItemRight__icon", "workflowContainer", "workflowLabel", "workflowLabelHeader", "workflowLabelHeaderContainer", "workflowLabelIcon", "workflowLabelContainer", "workflowContainerDisabledNotice", "workflowLabelDisabled__icon", "workflowContainerRight__icon", "workflowArrow__icon", "workflowDescription", "preferencesGroupContainer", "preferencesGroupHeader", "preferencesGroupLabelContainer", "preferencesGroupLabelIcon", "preferencesGroupLabel", "preferencesGroupActionsContainer", "preferencesGroupActionsContainerRight__icon", "preferencesGroupBody", "preferencesGroupChannels", "preferencesGroupInfo", "preferencesGroupInfoIcon", "preferencesGroupWorkflows", "channelContainer", "channelIconContainer", "channel__icon", "channelsContainerCollapsible", "channelsContainer", "channelLabel", "channelLabelContainer", "channelName", "channelSwitchContainer", "channelSwitch", "channelSwitchThumb", "preferencesHeader", "preferencesHeader__back__button", "preferencesHeader__back__button__icon", "preferencesHeader__title", "preferencesHeader__icon", "preferencesListEmptyNoticeContainer", "preferencesListEmptyNotice", "preferencesList__skeleton", "preferencesList__skeletonContent", "preferencesList__skeletonItem", "preferencesList__skeletonIcon", "preferencesList__skeletonSwitch", "preferencesList__skeletonSwitchThumb", "preferencesList__skeletonText", "notificationSnooze__dropdownContent", "notificationSnooze__dropdownItem", "notificationSnooze__dropdownItem__icon", "notificationSnoozeCustomTime_popoverContent", "notificationDeliveredAt__badge", "notificationDeliveredAt__icon", "notificationSnoozedUntil__icon", "strong"];

declare const defaultLocalization: {
    readonly locale: "en-US";
    readonly 'inbox.filters.dropdownOptions.unread': "Unread only";
    readonly 'inbox.filters.dropdownOptions.default': "Unread & read";
    readonly 'inbox.filters.dropdownOptions.archived': "Archived";
    readonly 'inbox.filters.dropdownOptions.snoozed': "Snoozed";
    readonly 'inbox.filters.labels.unread': "Unread";
    readonly 'inbox.filters.labels.default': "Inbox";
    readonly 'inbox.filters.labels.archived': "Archived";
    readonly 'inbox.filters.labels.snoozed': "Snoozed";
    readonly 'notifications.emptyNotice': "Quiet for now. Check back later.";
    readonly 'notifications.actions.readAll': "Mark all as read";
    readonly 'notifications.actions.archiveAll': "Archive all";
    readonly 'notifications.actions.archiveRead': "Archive read";
    readonly 'notifications.newNotifications': ({ notificationCount }: {
        notificationCount: number;
    }) => string;
    readonly 'notification.actions.read.tooltip': "Mark as read";
    readonly 'notification.actions.unread.tooltip': "Mark as unread";
    readonly 'notification.actions.archive.tooltip': "Archive";
    readonly 'notification.actions.unarchive.tooltip': "Unarchive";
    readonly 'notification.actions.snooze.tooltip': "Snooze";
    readonly 'notification.actions.unsnooze.tooltip': "Unsnooze";
    readonly 'notification.snoozedUntil': "Snoozed until";
    readonly 'preferences.title': "Preferences";
    readonly 'preferences.emptyNotice': "No notification specific preferences yet.";
    readonly 'preferences.global': "Global Preferences";
    readonly 'preferences.workflow.disabled.notice': "Contact admin to enable subscription management for this critical notification.";
    readonly 'preferences.workflow.disabled.tooltip': "Contact admin to edit";
    readonly 'preferences.group.info': "Applies to all notifications under this group.";
    readonly 'snooze.datePicker.timePickerLabel': "Time";
    readonly 'snooze.datePicker.apply': "Apply";
    readonly 'snooze.datePicker.cancel': "Cancel";
    readonly 'snooze.options.anHourFromNow': "An hour from now";
    readonly 'snooze.datePicker.pastDateTooltip': "Selected time must be at least 3 minutes in the future";
    readonly 'snooze.datePicker.noDateSelectedTooltip': "Please select a date";
    readonly 'snooze.datePicker.exceedingLimitTooltip': ({ days }: {
        days: number;
    }) => string;
    readonly 'snooze.options.customTime': "Custom time...";
    readonly 'snooze.options.inOneDay': "Tomorrow";
    readonly 'snooze.options.inOneWeek': "Next week";
};

type LocalizationKey = keyof typeof defaultLocalization;
type Localization = {
    [K in LocalizationKey]?: (typeof defaultLocalization)[K] extends (...args: infer P) => any ? ((...args: P) => ReturnType<(typeof defaultLocalization)[K]>) | string : string;
} & {
    dynamic?: Record<string, string>;
};

type NotificationClickHandler = (notification: Notification) => void;
type NotificationActionClickHandler = (notification: Notification) => void;
type NotificationRenderer = (el: HTMLDivElement, notification: Notification) => () => void;
type SubjectRenderer = (el: HTMLDivElement, notification: Notification) => () => void;
type BodyRenderer = (el: HTMLDivElement, notification: Notification) => () => void;
type BellRenderer = (el: HTMLDivElement, unreadCount: number) => () => void;
type RouterPush = (path: string) => void;
type Tab = {
    label: string;
    /**
     * @deprecated Use `filter` instead
     */
    value?: Array<string>;
    filter?: Pick<NotificationFilter, 'tags' | 'data'>;
};
type CSSProperties = {
    [key: string]: string | number;
};
type ElementStyles = string | CSSProperties;
type Variables = {
    colorBackground?: string;
    colorForeground?: string;
    colorPrimary?: string;
    colorPrimaryForeground?: string;
    colorSecondary?: string;
    colorSecondaryForeground?: string;
    colorCounter?: string;
    colorCounterForeground?: string;
    colorNeutral?: string;
    colorShadow?: string;
    colorRing?: string;
    fontSize?: string;
    borderRadius?: string;
    colorStripes?: string;
};
type AppearanceKey = (typeof appearanceKeys)[number];
type Elements = Partial<Record<AppearanceKey, ElementStyles>>;
type IconKey = 'bell' | 'clock' | 'arrowDropDown' | 'dots' | 'markAsRead' | 'cogs' | 'trash' | 'markAsArchived' | 'markAsArchivedRead' | 'markAsUnread' | 'markAsUnarchived' | 'unsnooze' | 'arrowRight' | 'arrowLeft' | 'unread' | 'sms' | 'inApp' | 'email' | 'push' | 'chat' | 'check' | 'arrowDown' | 'routeFill' | 'info' | 'nodeTree';
type IconRenderer = (el: HTMLDivElement, props: {
    class?: string;
}) => () => void;
type IconOverrides = {
    [key in IconKey]?: IconRenderer;
};
type Theme = {
    variables?: Variables;
    elements?: Elements;
    animations?: boolean;
    icons?: IconOverrides;
};
type Appearance = Theme & {
    baseTheme?: Theme | Theme[];
};
type BaseNovuProviderProps = {
    container?: Node | string | null;
    appearance?: Appearance;
    localization?: Localization;
    options: NovuOptions;
    tabs?: Array<Tab>;
    preferencesFilter?: PreferencesFilter;
    preferenceGroups?: PreferenceGroups;
    routerPush?: RouterPush;
    novu?: Novu;
};
type NovuProviderProps = BaseNovuProviderProps & {
    renderNotification?: NotificationRenderer;
    renderBell?: BellRenderer;
};
declare enum NotificationStatus {
    UNREAD_READ = "unreadRead",
    UNREAD = "unread",
    ARCHIVED = "archived",
    SNOOZED = "snoozed"
}
type PreferencesFilter = Pick<NotificationFilter, 'tags'>;
type PreferenceFilterFunction = (args: {
    preferences: Preference[];
}) => Preference[];
type PreferenceGroupFilter = (PreferencesFilter & {
    workflowIds?: string[];
}) | PreferenceFilterFunction;
type PreferenceGroups = Array<{
    name: string;
    filter: PreferenceGroupFilter;
}>;

export { type Appearance as A, type BellRenderer as B, type Elements as E, type IconKey as I, type Localization as L, type NotificationClickHandler as N, type PreferencesFilter as P, type RouterPush as R, type SubjectRenderer as S, type Tab as T, type Variables as V, type NotificationActionClickHandler as a, type NotificationRenderer as b, type BodyRenderer as c, type NovuProviderProps as d, type BaseNovuProviderProps as e, type PreferenceGroups as f, type AppearanceKey as g, type ElementStyles as h, type IconOverrides as i, type IconRenderer as j, type LocalizationKey as k, NotificationStatus as l, type Theme as m };
