'use strict';

var chunkRN7LHLHM_js = require('../chunk-RN7LHLHM.js');
var chunkERC62PGI_js = require('../chunk-ERC62PGI.js');
var chunk7B52C2XE_js = require('../chunk-7B52C2XE.js');
var web = require('solid-js/web');
var solidJs = require('solid-js');
var store = require('solid-js/store');
var clsx = require('clsx');
var tailwindMerge = require('tailwind-merge');
var dom = require('@floating-ui/dom');
var solidFloatingUi = require('solid-floating-ui');
var solidMotionone = require('solid-motionone');
var classVarianceAuthority = require('class-variance-authority');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var clsx__default = /*#__PURE__*/_interopDefault(clsx);

// _hbh5yyuou:/home/<USER>/work/novu/novu/packages/js/src/ui/index.directcss
var ui_default = `.novu{scrollbar-color:var(--nv-color-secondary-foreground-alpha-300) #0000;:where(*),:where(*) :after,:where(*) :before,:where(*):after,:where(*):before{border:0 solid #e5e7eb;box-sizing:border-box}:where(html,:host){line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;tab-size:4;-webkit-tap-highlight-color:transparent}:where(body){line-height:inherit;margin:0}:where(hr){border-top-width:1px;color:inherit;height:0}:where(abbr:where([title])){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}:where(h1,h2,h3,h4,h5,h6){font-size:inherit;font-weight:inherit}:where(a){color:inherit;text-decoration:inherit}:where(b,strong){font-weight:bolder}:where(code,kbd,samp,pre){font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal}:where(small){font-size:80%}:where(sub,sup){font-size:75%;line-height:0;position:relative;vertical-align:initial}:where(sub){bottom:-.25em}:where(sup){top:-.5em}:where(table){border-collapse:collapse;border-color:inherit;text-indent:0}:where(button,input,optgroup,select,textarea){color:inherit;font-family:inherit;font-feature-settings:inherit;font-size:100%;font-variation-settings:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;margin:0;padding:0}:where(button,select){text-transform:none}:where(button,input:where([type=button]),input:where([type=reset]),input:where([type=submit])){-webkit-appearance:button;background-color:initial;background-image:none}:where(:-moz-focusring){outline:auto}:where(:-moz-ui-invalid){box-shadow:none}:where(progress){vertical-align:initial}:where(*)::-webkit-inner-spin-button,:where(*)::-webkit-outer-spin-button{height:auto}:where([type=search]){-webkit-appearance:textfield;outline-offset:-2px}:where(*)::-webkit-search-decoration{-webkit-appearance:none}:where(*)::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}:where(summary){display:list-item}:where(blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre){margin:0}:where(fieldset){margin:0;padding:0}:where(legend){padding:0}:where(ol,ul,menu){list-style:none;margin:0;padding:0}:where(dialog){padding:0}:where(textarea){resize:vertical}:where(input)::placeholder,:where(textarea)::placeholder{color:#9ca3af;opacity:1}:where(button,[role=button]){cursor:pointer}:where(:disabled){cursor:default}:where(img,svg,video,canvas,audio,iframe,embed,object){display:block;vertical-align:middle}:where(img,video){height:auto;max-width:100%}:where([hidden]){display:none}:where(*),:where(*) :after,:where(*) :before,:where(*):after,:where(*):before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#3b82f680;--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }:where(*) ::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#3b82f680;--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::-webkit-scrollbar{height:.5rem;width:.5rem}::-webkit-scrollbar-thumb{background-clip:"padding-box";background-color:var(--nv-color-secondary-foreground-alpha-300);border-radius:.25rem}::-webkit-scrollbar-corner,::-webkit-scrollbar-track{background-color:initial}input::-webkit-inner-spin-button,input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}input[type=number]{-moz-appearance:textfield}}.nt-sr-only{height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;clip:rect(0,0,0,0);border-width:0;white-space:nowrap}.\\!nt-pointer-events-auto{pointer-events:auto!important}.nt-invisible{visibility:hidden}.nt-absolute{position:absolute}.nt-relative{position:relative}.nt-inset-0{inset:0}.nt-inset-2{inset:.5rem}.nt-left-0\\.5{left:.125rem}.nt-right-0{right:0}.nt-right-3{right:.75rem}.nt-top-0{top:0}.nt-top-0\\.5{top:.125rem}.nt-top-3{top:.75rem}.nt-z-10{z-index:10}.nt-z-\\[-1\\]{z-index:-1}.nt-mx-auto{margin-left:auto;margin-right:auto}.nt--mt-\\[50px\\]{margin-top:-50px}.nt-mb-1{margin-bottom:.25rem}.nt-mb-2{margin-bottom:.5rem}.nt-mb-4{margin-bottom:1rem}.nt-mb-\\[0\\.625rem\\]{margin-bottom:.625rem}.nt-ml-1{margin-left:.25rem}.nt-ml-2{margin-left:.5rem}.nt-ml-auto{margin-left:auto}.nt-mr-2{margin-right:.5rem}.nt-mr-auto{margin-right:auto}.nt-mt-1{margin-top:.25rem}.nt-mt-auto{margin-top:auto}.nt-block{display:block}.nt-flex{display:flex}.nt-inline-flex{display:inline-flex}.nt-grid{display:grid}.nt-hidden{display:none}.nt-aspect-square{aspect-ratio:1/1}.nt-size-1\\.5{height:.375rem;width:.375rem}.nt-size-2{height:.5rem;width:.5rem}.nt-size-2\\.5{height:.625rem;width:.625rem}.nt-size-3{height:.75rem;width:.75rem}.nt-size-3\\.5{height:.875rem;width:.875rem}.nt-size-4{height:1rem;width:1rem}.nt-size-5{height:1.25rem;width:1.25rem}.nt-size-8{height:2rem;width:2rem}.nt-size-fit{height:fit-content;width:fit-content}.nt-size-full{height:100%;width:100%}.nt-h-2{height:.5rem}.nt-h-3{height:.75rem}.nt-h-4{height:1rem}.nt-h-5{height:1.25rem}.nt-h-7{height:1.75rem}.nt-h-8{height:2rem}.nt-h-9{height:2.25rem}.nt-h-\\[600px\\]{height:600px}.nt-h-fit{height:fit-content}.nt-h-full{height:100%}.nt-min-h-0{min-height:0}.nt-w-1\\/3{width:33.333333%}.nt-w-2\\/3{width:66.666667%}.nt-w-5{width:1.25rem}.nt-w-7{width:1.75rem}.nt-w-8{width:2rem}.nt-w-\\[260px\\]{width:260px}.nt-w-\\[400px\\]{width:400px}.nt-w-\\[60px\\]{width:60px}.nt-w-\\[calc\\(2ch\\+2rem\\)\\]{width:calc(2ch + 2rem)}.nt-w-fit{width:fit-content}.nt-w-full{width:100%}.nt-w-max{width:max-content}.nt-min-w-52{min-width:13rem}.nt-flex-1{flex:1 1 0%}.nt-shrink-0{flex-shrink:0}.nt-translate-x-1\\/2{--tw-translate-x:50%}.nt-transform,.nt-translate-x-1\\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.nt-cursor-default{cursor:default}.nt-cursor-not-allowed{cursor:not-allowed}.nt-cursor-pointer{cursor:pointer}.nt-grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.nt-flex-row{flex-direction:row}.nt-flex-col{flex-direction:column}.nt-flex-wrap{flex-wrap:wrap}.nt-flex-nowrap{flex-wrap:nowrap}.nt-items-start{align-items:flex-start}.nt-items-center{align-items:center}.nt-justify-end{justify-content:flex-end}.nt-justify-center{justify-content:center}.nt-justify-between{justify-content:space-between}.nt-gap-0\\.5{gap:.125rem}.nt-gap-1{gap:.25rem}.nt-gap-1\\.5{gap:.375rem}.nt-gap-2{gap:.5rem}.nt-gap-3{gap:.75rem}.nt-gap-4{gap:1rem}.nt-gap-6{gap:1.5rem}.nt-gap-8{gap:2rem}.nt-self-stretch{align-self:stretch}.nt-overflow-auto{overflow:auto}.nt-overflow-hidden{overflow:hidden}.nt-overflow-y-auto{overflow-y:auto}.nt-truncate{overflow:hidden;text-overflow:ellipsis}.nt-truncate,.nt-whitespace-nowrap{white-space:nowrap}.nt-whitespace-pre-wrap{white-space:pre-wrap}.nt-rounded{border-radius:var(--nv-radius-base)}.nt-rounded-full{border-radius:var(--nv-radius-full)}.nt-rounded-lg{border-radius:var(--nv-radius-lg)}.nt-rounded-md{border-radius:var(--nv-radius-md)}.nt-rounded-sm{border-radius:var(--nv-radius-sm)}.nt-rounded-xl{border-radius:var(--nv-radius-xl)}.nt-border{border-width:1px}.nt-border-b{border-bottom-width:1px}.nt-border-t{border-top-width:1px}.nt-border-background{border-color:var(--nv-color-background)}.nt-border-border{border-color:var(--nv-color-neutral-alpha-100)}.nt-border-neutral-200{--tw-border-opacity:1;border-color:rgb(229 229 229/var(--tw-border-opacity,1))}.nt-border-neutral-alpha-100{border-color:var(--nv-color-neutral-alpha-100)}.nt-border-neutral-alpha-200{border-color:var(--nv-color-neutral-alpha-200)}.nt-border-neutral-alpha-400{border-color:var(--nv-color-neutral-alpha-400)}.nt-border-neutral-alpha-50{border-color:var(--nv-color-neutral-alpha-50)}.nt-border-t-neutral-alpha-200{border-top-color:var(--nv-color-neutral-alpha-200)}.nt-bg-\\[oklch\\(from_var\\(--nv-color-stripes\\)_l_c_h_\\/_0\\.1\\)\\]{background-color:oklch(from var(--nv-color-stripes) l c h/.1)}.nt-bg-background{background-color:var(--nv-color-background)}.nt-bg-counter{background-color:var(--nv-color-counter)}.nt-bg-foreground{background-color:var(--nv-color-foreground)}.nt-bg-neutral-900{--tw-bg-opacity:1;background-color:rgb(23 23 23/var(--tw-bg-opacity,1))}.nt-bg-neutral-alpha-100{background-color:var(--nv-color-neutral-alpha-100)}.nt-bg-neutral-alpha-25{background-color:var(--nv-color-neutral-alpha-25)}.nt-bg-neutral-alpha-300{background-color:var(--nv-color-neutral-alpha-300)}.nt-bg-neutral-alpha-50{background-color:var(--nv-color-neutral-alpha-50)}.nt-bg-primary{background-color:var(--nv-color-primary)}.nt-bg-primary-alpha-300{background-color:var(--nv-color-primary-alpha-300)}.nt-bg-primary-alpha-400{background-color:var(--nv-color-primary-alpha-400)}.nt-bg-secondary{background-color:var(--nv-color-secondary)}.nt-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.nt-bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}.nt-bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.nt-from-foreground-alpha-50{--tw-gradient-from:var(--nv-color-foreground-alpha-50) var(--tw-gradient-from-position);--tw-gradient-to:#fff0 var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.nt-from-primary-foreground-alpha-200{--tw-gradient-from:var(--nv-color-primary-foreground-alpha-200) var(--tw-gradient-from-position);--tw-gradient-to:#fff0 var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.nt-from-transparent{--tw-gradient-from:#0000 var(--tw-gradient-from-position);--tw-gradient-to:#0000 var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.nt-from-20\\%{--tw-gradient-from-position:20%}.nt-to-background{--tw-gradient-to:var(--nv-color-background) var(--tw-gradient-to-position)}.nt-to-transparent{--tw-gradient-to:#0000 var(--tw-gradient-to-position)}.nt-object-cover{object-fit:cover}.nt-p-0{padding:0}.nt-p-0\\.5{padding:.125rem}.nt-p-1{padding:.25rem}.nt-p-2{padding:.5rem}.nt-p-2\\.5{padding:.625rem}.nt-p-3{padding:.75rem}.nt-p-4{padding:1rem}.nt-px-1{padding-left:.25rem;padding-right:.25rem}.nt-px-2{padding-left:.5rem;padding-right:.5rem}.nt-px-3{padding-left:.75rem;padding-right:.75rem}.nt-px-4{padding-left:1rem;padding-right:1rem}.nt-px-8{padding-left:2rem;padding-right:2rem}.nt-px-\\[6px\\]{padding-left:6px;padding-right:6px}.nt-py-1{padding-bottom:.25rem;padding-top:.25rem}.nt-py-2{padding-bottom:.5rem;padding-top:.5rem}.nt-py-3{padding-bottom:.75rem;padding-top:.75rem}.nt-py-3\\.5{padding-bottom:.875rem;padding-top:.875rem}.nt-py-4{padding-bottom:1rem;padding-top:1rem}.nt-py-px{padding-bottom:1px;padding-top:1px}.nt-pb-2{padding-bottom:.5rem}.nt-pb-\\[0\\.625rem\\]{padding-bottom:.625rem}.nt-pr-0{padding-right:0}.nt-pt-2\\.5{padding-top:.625rem}.nt-text-center{text-align:center}.nt-text-start{text-align:start}.nt-font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.nt-text-\\[0\\.8rem\\]{font-size:.8rem}.nt-text-\\[12px\\]{font-size:12px}.nt-text-base{font-size:var(--nv-font-size-base);line-height:var(--nv-line-height-base)}.nt-text-sm{font-size:var(--nv-font-size-sm);line-height:var(--nv-line-height-sm)}.nt-text-xl{font-size:var(--nv-font-size-xl);line-height:var(--nv-line-height-xl)}.nt-text-xs{font-size:var(--nv-font-size-xs);line-height:var(--nv-line-height-xs)}.nt-font-medium{font-weight:500}.nt-font-normal{font-weight:400}.nt-font-semibold{font-weight:600}.nt-leading-none{line-height:1}.nt-text-\\[\\#000000\\]{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}.nt-text-background{color:var(--nv-color-background)}.nt-text-counter-foreground{color:var(--nv-color-counter-foreground)}.nt-text-foreground{color:var(--nv-color-foreground)}.nt-text-foreground-alpha-300{color:var(--nv-color-foreground-alpha-300)}.nt-text-foreground-alpha-400{color:var(--nv-color-foreground-alpha-400)}.nt-text-foreground-alpha-600{color:var(--nv-color-foreground-alpha-600)}.nt-text-foreground-alpha-700{color:var(--nv-color-foreground-alpha-700)}.nt-text-primary-foreground{color:var(--nv-color-primary-foreground)}.nt-text-secondary-foreground{color:var(--nv-color-secondary-foreground)}.nt-text-stripes{color:var(--nv-color-stripes)}.nt-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.nt-underline{text-decoration-line:underline}.nt-opacity-0{opacity:0}.nt-opacity-20{opacity:.2}.nt-opacity-50{opacity:.5}.nt-shadow{--tw-shadow:0 1px 3px 0 #0000001a,0 1px 2px -1px #0000001a;--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-\\[0_0_0_0\\.5px_var\\(--nv-color-primary-600\\)\\]{--tw-shadow:0 0 0 0.5px var(--nv-color-primary-600);--tw-shadow-colored:0 0 0 0.5px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-\\[0_0_0_0\\.5px_var\\(--nv-color-secondary-600\\)\\]{--tw-shadow:0 0 0 0.5px var(--nv-color-secondary-600);--tw-shadow-colored:0 0 0 0.5px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-\\[0px_1px_2px_0px_rgba\\(10\\,13\\,20\\,0\\.03\\)\\]{--tw-shadow:0px 1px 2px 0px #0a0d1408;--tw-shadow-colored:0px 1px 2px 0px var(--tw-shadow-color)}.nt-shadow-\\[0px_1px_2px_0px_rgba\\(10\\,13\\,20\\,0\\.03\\)\\],.nt-shadow-dropdown{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-dropdown{--tw-shadow:0px 12px 16px -4px oklch(from var(--nv-color-shadow) l c h/0.08),0px 4px 6px -2px oklch(from var(--nv-color-shadow) l c h/0.03);--tw-shadow-colored:0px 12px 16px -4px var(--tw-shadow-color),0px 4px 6px -2px var(--tw-shadow-color)}.nt-shadow-lg{--tw-shadow:0 10px 15px -3px #0000001a,0 4px 6px -4px #0000001a;--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color)}.nt-shadow-lg,.nt-shadow-none{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.nt-shadow-popover{--tw-shadow:0px 8px 26px 0px oklch(from var(--nv-color-shadow) l c h/0.08),0px 2px 6px 0px oklch(from var(--nv-color-shadow) l c h/0.12);--tw-shadow-colored:0px 8px 26px 0px var(--tw-shadow-color),0px 2px 6px 0px var(--tw-shadow-color)}.nt-shadow-popover,.nt-shadow-sm{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-shadow-sm{--tw-shadow:0 1px 2px 0 #0000000d;--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)}.nt-shadow-tooltip{--tw-shadow:0 5px 20px 0 oklch(from var(--nv-color-shadow) l c h/0.08);--tw-shadow-colored:0 5px 20px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.nt-outline-none{outline:2px solid #0000;outline-offset:2px}.nt-backdrop-blur-lg{--tw-backdrop-blur:blur(16px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.nt-transition{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1)}.nt-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.nt-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.nt-duration-100{transition-duration:.1s}.nt-duration-200{transition-duration:.2s}.nt-ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0) scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1)) rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0) scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1)) rotate(var(--tw-exit-rotate,0))}}.nt-animate-in{animation-duration:.15s;animation-name:enter;--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial}.nt-fade-in{--tw-enter-opacity:0}.nt-slide-in-from-top-2{--tw-enter-translate-y:-0.5rem}.nt-duration-100{animation-duration:.1s}.nt-duration-200{animation-duration:.2s}.nt-ease-out{animation-timing-function:cubic-bezier(0,0,.2,1)}.\\[interpolate-size\\:allow-keywords\\]{interpolate-size:allow-keywords}.\\[scrollbar-gutter\\:stable\\]{scrollbar-gutter:stable}.\\[word-break\\:break-word\\]{word-break:break-word}.before\\:nt-absolute:before{content:var(--tw-content);position:absolute}.before\\:nt-inset-0:before{content:var(--tw-content);inset:0}.before\\:-nt-right-\\[calc\\(0\\+var\\(--stripes-size\\)\\)\\]:before{content:var(--tw-content);right:calc(var(--stripes-size)*-1)}@keyframes nt-stripes{0%{content:var(--tw-content);transform:translateX(0)}to{content:var(--tw-content);transform:translateX(calc(var(--stripes-size)*-1))}}.before\\:nt-animate-stripes:before{animation:nt-stripes 1s linear infinite paused;content:var(--tw-content)}.before\\:nt-rounded-lg:before{border-radius:var(--nv-radius-lg);content:var(--tw-content)}.before\\:nt-rounded-md:before{border-radius:var(--nv-radius-md);content:var(--tw-content)}.before\\:nt-rounded-xl:before{border-radius:var(--nv-radius-xl);content:var(--tw-content)}.before\\:nt-border:before{border-width:1px;content:var(--tw-content)}.before\\:nt-border-primary-foreground-alpha-100:before{border-color:var(--nv-color-primary-foreground-alpha-100);content:var(--tw-content)}.before\\:nt-border-secondary-foreground-alpha-100:before{border-color:var(--nv-color-secondary-foreground-alpha-100);content:var(--tw-content)}.before\\:nt-bg-dev-stripes-gradient:before{background-image:repeating-linear-gradient(135deg,oklch(from var(--nv-color-stripes) l c h/.1) 25%,oklch(from var(--nv-color-stripes) l c h/.1) 50%,oklch(from var(--nv-color-stripes) l c h/.2) 50%,oklch(from var(--nv-color-stripes) l c h/.2) 75%);content:var(--tw-content)}.before\\:nt-bg-\\[length\\:var\\(--stripes-size\\)_var\\(--stripes-size\\)\\]:before{background-size:var(--stripes-size) var(--stripes-size);content:var(--tw-content)}.before\\:nt-content-\\[\\"\\"\\]:before{--tw-content:"";content:var(--tw-content)}.before\\:\\[mask-image\\:linear-gradient\\(transparent_0\\%\\2c black\\)\\]:before{content:var(--tw-content);-webkit-mask-image:linear-gradient(#0000,#000);mask-image:linear-gradient(#0000,#000)}.after\\:nt-absolute:after{content:var(--tw-content);position:absolute}.after\\:nt-inset-0:after{content:var(--tw-content);inset:0}.after\\:-nt-top-12:after{content:var(--tw-content);top:-3rem}.after\\:nt-bottom-0:after{bottom:0;content:var(--tw-content)}.after\\:nt-left-0:after{content:var(--tw-content);left:0}.after\\:nt-left-0\\.5:after{content:var(--tw-content);left:.125rem}.after\\:nt-top-0\\.5:after{content:var(--tw-content);top:.125rem}.after\\:nt-size-3:after{content:var(--tw-content);height:.75rem;width:.75rem}.after\\:nt-h-\\[2px\\]:after{content:var(--tw-content);height:2px}.after\\:nt-w-full:after{content:var(--tw-content);width:100%}.after\\:nt-translate-x-1\\/2:after{--tw-translate-x:50%}.after\\:nt-translate-x-1\\/2:after,.after\\:nt-translate-x-full:after{content:var(--tw-content);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.after\\:nt-translate-x-full:after{--tw-translate-x:100%}.after\\:nt-rounded-full:after{border-radius:var(--nv-radius-full);content:var(--tw-content)}.after\\:nt-rounded-lg:after{border-radius:var(--nv-radius-lg);content:var(--tw-content)}.after\\:nt-rounded-md:after{border-radius:var(--nv-radius-md);content:var(--tw-content)}.after\\:nt-rounded-xl:after{border-radius:var(--nv-radius-xl);content:var(--tw-content)}.after\\:nt-border-b-2:after{border-bottom-width:2px;content:var(--tw-content)}.after\\:nt-border-background:after{border-color:var(--nv-color-background);content:var(--tw-content)}.after\\:nt-border-b-primary:after{border-bottom-color:var(--nv-color-primary);content:var(--tw-content)}.after\\:nt-border-b-transparent:after{border-bottom-color:#0000;content:var(--tw-content)}.after\\:nt-bg-background:after{background-color:var(--nv-color-background);content:var(--tw-content)}.after\\:nt-bg-\\[linear-gradient\\(180deg\\2c transparent\\2c oklch\\(from_var\\(--nv-color-background\\)_l_c_h_\\/_0\\.9\\)_55\\%\\2c transparent\\)\\]:after{background-image:linear-gradient(180deg,#0000,oklch(from var(--nv-color-background) l c h/.9) 55%,#0000);content:var(--tw-content)}.after\\:nt-bg-\\[linear-gradient\\(180deg\\2c transparent\\2c oklch\\(from_var\\(--nv-color-stripes\\)_l_c_h_\\/_0\\.07\\)_55\\%\\2c transparent\\)\\2c linear-gradient\\(180deg\\2c transparent\\2c oklch\\(from_var\\(--nv-color-background\\)_l_c_h_\\/_0\\.9\\)_55\\%\\2c transparent\\)\\]:after{background-image:linear-gradient(180deg,#0000,oklch(from var(--nv-color-stripes) l c h/.07) 55%,#0000),linear-gradient(180deg,#0000,oklch(from var(--nv-color-background) l c h/.9) 55%,#0000);content:var(--tw-content)}.after\\:nt-bg-gradient-to-b:after{background-image:linear-gradient(to bottom,var(--tw-gradient-stops));content:var(--tw-content)}.after\\:nt-from-primary-foreground-alpha-50:after{content:var(--tw-content);--tw-gradient-from:var(--nv-color-primary-foreground-alpha-50) var(--tw-gradient-from-position);--tw-gradient-to:#fff0 var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.after\\:nt-from-secondary-foreground-alpha-50:after{content:var(--tw-content);--tw-gradient-from:var(--nv-color-secondary-foreground-alpha-50) var(--tw-gradient-from-position);--tw-gradient-to:#fff0 var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.after\\:nt-to-transparent:after{content:var(--tw-content);--tw-gradient-to:#0000 var(--tw-gradient-to-position)}.after\\:nt-opacity-0:after{content:var(--tw-content);opacity:0}.after\\:nt-transition-all:after{content:var(--tw-content);transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.after\\:nt-transition-opacity:after{content:var(--tw-content);transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.after\\:nt-duration-200:after{transition-duration:.2s}.after\\:nt-content-\\[\\"\\"\\]:after,.after\\:nt-content-\\[\\'\\'\\]:after{--tw-content:"";content:var(--tw-content)}.after\\:nt-duration-200:after{animation-duration:.2s;content:var(--tw-content)}.hover\\:nt-bg-neutral-alpha-100:hover{background-color:var(--nv-color-neutral-alpha-100)}.hover\\:nt-bg-neutral-alpha-50:hover{background-color:var(--nv-color-neutral-alpha-50)}.hover\\:nt-bg-primary-600:hover{background-color:var(--nv-color-primary-600)}.hover\\:nt-bg-primary-alpha-25:hover{background-color:var(--nv-color-primary-alpha-25)}.hover\\:nt-bg-primary-alpha-400:hover{background-color:var(--nv-color-primary-alpha-400)}.hover\\:nt-text-foreground-alpha-800:hover{color:var(--nv-color-foreground-alpha-800)}.before\\:hover\\:\\[animation-play-state\\:running\\]:hover:before{animation-play-state:running;content:var(--tw-content)}.hover\\:after\\:nt-opacity-100:hover:after{content:var(--tw-content);opacity:1}.focus\\:nt-outline-none:focus{outline:2px solid #0000;outline-offset:2px}.focus-visible\\:nt-rounded-lg:focus-visible{border-radius:var(--nv-radius-lg)}.focus-visible\\:nt-rounded-md:focus-visible{border-radius:var(--nv-radius-md)}.focus-visible\\:nt-rounded-xl:focus-visible{border-radius:var(--nv-radius-xl)}.focus-visible\\:nt-bg-neutral-alpha-50:focus-visible{background-color:var(--nv-color-neutral-alpha-50)}.focus-visible\\:nt-outline-none:focus-visible{outline:2px solid #0000;outline-offset:2px}.focus-visible\\:nt-ring-2:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus-visible\\:nt-ring-primary:focus-visible{--tw-ring-color:var(--nv-color-primary)}.focus-visible\\:nt-ring-ring:focus-visible{--tw-ring-color:var(--nv-color-ring)}.focus-visible\\:nt-ring-offset-2:focus-visible{--tw-ring-offset-width:2px}.disabled\\:nt-pointer-events-none:disabled{pointer-events:none}.disabled\\:nt-opacity-20:disabled{opacity:.2}.disabled\\:nt-opacity-50:disabled{opacity:.5}.nt-group:focus-within .group-focus-within\\:nt-opacity-100,.nt-group:hover .group-hover\\:nt-opacity-100{opacity:1}.data-\\[open\\=true\\]\\:nt-rotate-180[data-open=true]{--tw-rotate:180deg}.data-\\[open\\=true\\]\\:nt-rotate-180[data-open=true],.data-\\[open\\=true\\]\\:nt-transform[data-open=true]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\\[disabled\\=true\\]\\:nt-text-foreground-alpha-400[data-disabled=true]{color:var(--nv-color-foreground-alpha-400)}.data-\\[disabled\\=true\\]\\:nt-text-foreground-alpha-600[data-disabled=true]{color:var(--nv-color-foreground-alpha-600)}.data-\\[state\\=active\\]\\:nt-text-foreground[data-state=active]{color:var(--nv-color-foreground)}.data-\\[state\\=active\\]\\:after\\:nt-border-b-2[data-state=active]:after{border-bottom-width:2px;content:var(--tw-content)}.data-\\[state\\=active\\]\\:after\\:nt-border-primary[data-state=active]:after{border-color:var(--nv-color-primary);content:var(--tw-content)}.data-\\[state\\=active\\]\\:after\\:nt-opacity-100[data-state=active]:after{content:var(--tw-content);opacity:1}.\\[\\&\\:not\\(\\:first-child\\)\\]\\:nt-border-t:not(:first-child){border-top-width:1px}.\\[\\&_\\.nv-notificationList\\]\\:nt-pb-12 .nv-notificationList{padding-bottom:3rem}.\\[\\&_\\.nv-notificationList\\]\\:nt-pb-8 .nv-notificationList{padding-bottom:2rem}.\\[\\&_\\.nv-preferencesContainer\\]\\:nt-pb-12 .nv-preferencesContainer{padding-bottom:3rem}.\\[\\&_\\.nv-preferencesContainer\\]\\:nt-pb-8 .nv-preferencesContainer{padding-bottom:2rem}.\\[\\&_svg\\]\\:nt-pointer-events-none svg{pointer-events:none}.\\[\\&_svg\\]\\:nt-shrink-0 svg{flex-shrink:0}`;

// src/ui/config/appearanceKeys.ts
var appearanceKeys = [
  // Primitives
  "button",
  "input",
  "icon",
  "badge",
  "popoverContent",
  "popoverTrigger",
  "popoverClose",
  "dropdownContent",
  "dropdownTrigger",
  "dropdownItem",
  "dropdownItemLabel",
  "dropdownItemLabelContainer",
  "dropdownItemLeft__icon",
  "dropdownItemRight__icon",
  "dropdownItem__icon",
  "collapsible",
  "tooltipContent",
  "tooltipTrigger",
  "datePicker",
  "datePickerGrid",
  "datePickerGridRow",
  "datePickerGridCell",
  "datePickerGridCellTrigger",
  "datePickerTrigger",
  "datePickerGridHeader",
  "datePickerControl",
  "datePickerControlPrevTrigger",
  "datePickerControlNextTrigger",
  "datePickerControlPrevTrigger__icon",
  "datePickerControlNextTrigger__icon",
  "datePickerCalendar",
  "datePickerHeaderMonth",
  "datePickerCalendarDay__button",
  "timePicker",
  "timePicker__hourSelect",
  "timePicker__minuteSelect",
  "timePicker__periodSelect",
  "timePicker__separator",
  "timePickerHour__input",
  "timePickerMinute__input",
  "snoozeDatePicker",
  "snoozeDatePicker__actions",
  "snoozeDatePickerCancel__button",
  "snoozeDatePickerApply__button",
  "snoozeDatePicker__timePickerContainer",
  "snoozeDatePicker__timePickerLabel",
  "back__button",
  "skeletonText",
  "skeletonAvatar",
  "skeletonSwitch",
  "skeletonSwitchThumb",
  "tabsRoot",
  "tabsList",
  "tabsContent",
  "tabsTrigger",
  "dots",
  // General
  "root",
  "bellIcon",
  "lockIcon",
  "bellContainer",
  "bellDot",
  "preferences__button",
  "preferencesContainer",
  "inboxHeader",
  "loading",
  // Inbox
  "inboxContent",
  "inbox__popoverTrigger",
  "inbox__popoverContent",
  // Notifications
  "notificationListContainer",
  "notificationList",
  "notificationListEmptyNoticeContainer",
  "notificationListEmptyNoticeOverlay",
  "notificationListEmptyNoticeIcon",
  "notificationListEmptyNotice",
  "notificationList__skeleton",
  "notificationList__skeletonContent",
  "notificationList__skeletonItem",
  "notificationList__skeletonAvatar",
  "notificationList__skeletonText",
  "notificationListNewNotificationsNotice__button",
  "notification",
  "notificationContent",
  "notificationTextContainer",
  "notificationDot",
  "notificationSubject",
  "notificationSubject__strong",
  "notificationBody",
  "notificationBody__strong",
  "notificationBodyContainer",
  "notificationImage",
  "notificationImageLoadingFallback",
  "notificationDate",
  "notificationDateActionsContainer",
  "notificationDefaultActions",
  "notificationCustomActions",
  "notificationPrimaryAction__button",
  "notificationSecondaryAction__button",
  "notificationRead__button",
  "notificationUnread__button",
  "notificationArchive__button",
  "notificationUnarchive__button",
  "notificationSnooze__button",
  "notificationUnsnooze__button",
  "notificationRead__icon",
  "notificationUnread__icon",
  "notificationArchive__icon",
  "notificationUnarchive__icon",
  "notificationSnooze__icon",
  "notificationUnsnooze__icon",
  // Notifications tabs
  "notificationsTabs__tabsRoot",
  "notificationsTabs__tabsList",
  "notificationsTabs__tabsContent",
  "notificationsTabs__tabsTrigger",
  "notificationsTabsTriggerLabel",
  "notificationsTabsTriggerCount",
  // Inbox status
  "inboxStatus__title",
  "inboxStatus__dropdownTrigger",
  "inboxStatus__dropdownContent",
  "inboxStatus__dropdownItem",
  "inboxStatus__dropdownItemLabel",
  "inboxStatus__dropdownItemLabelContainer",
  "inboxStatus__dropdownItemLeft__icon",
  "inboxStatus__dropdownItemRight__icon",
  "inboxStatus__dropdownItem__icon",
  "inboxStatus__dropdownItemCheck__icon",
  // More actions
  "moreActionsContainer",
  "moreActions__dropdownTrigger",
  "moreActions__dropdownContent",
  "moreActions__dropdownItem",
  "moreActions__dropdownItemLabel",
  "moreActions__dropdownItemLeft__icon",
  "moreActions__dots",
  // More tabs
  "moreTabs__button",
  "moreTabs__icon",
  "moreTabs__dropdownTrigger",
  "moreTabs__dropdownContent",
  "moreTabs__dropdownItem",
  "moreTabs__dropdownItemLabel",
  "moreTabs__dropdownItemRight__icon",
  // workflow
  "workflowContainer",
  "workflowLabel",
  "workflowLabelHeader",
  "workflowLabelHeaderContainer",
  "workflowLabelIcon",
  "workflowLabelContainer",
  "workflowContainerDisabledNotice",
  "workflowLabelDisabled__icon",
  "workflowContainerRight__icon",
  "workflowArrow__icon",
  "workflowDescription",
  // preference groups
  "preferencesGroupContainer",
  "preferencesGroupHeader",
  "preferencesGroupLabelContainer",
  "preferencesGroupLabelIcon",
  "preferencesGroupLabel",
  "preferencesGroupActionsContainer",
  "preferencesGroupActionsContainerRight__icon",
  "preferencesGroupBody",
  "preferencesGroupChannels",
  "preferencesGroupInfo",
  "preferencesGroupInfoIcon",
  "preferencesGroupWorkflows",
  // channel
  "channelContainer",
  "channelIconContainer",
  "channel__icon",
  "channelsContainerCollapsible",
  "channelsContainer",
  "channelLabel",
  "channelLabelContainer",
  "channelName",
  "channelSwitchContainer",
  "channelSwitch",
  "channelSwitchThumb",
  // Preferences Header
  "preferencesHeader",
  "preferencesHeader__back__button",
  "preferencesHeader__back__button__icon",
  "preferencesHeader__title",
  "preferencesHeader__icon",
  // Preferences Loading
  "preferencesListEmptyNoticeContainer",
  "preferencesListEmptyNotice",
  "preferencesList__skeleton",
  "preferencesList__skeletonContent",
  "preferencesList__skeletonItem",
  "preferencesList__skeletonIcon",
  "preferencesList__skeletonSwitch",
  "preferencesList__skeletonSwitchThumb",
  "preferencesList__skeletonText",
  // Notification Snooze
  "notificationSnooze__dropdownContent",
  "notificationSnooze__dropdownItem",
  "notificationSnooze__dropdownItem__icon",
  "notificationSnoozeCustomTime_popoverContent",
  // Notification Delivered At
  "notificationDeliveredAt__badge",
  "notificationDeliveredAt__icon",
  "notificationSnoozedUntil__icon",
  // Text formatting
  "strong"
];
var defaultLocalization = {
  locale: "en-US",
  "inbox.filters.dropdownOptions.unread": "Unread only",
  "inbox.filters.dropdownOptions.default": "Unread & read",
  "inbox.filters.dropdownOptions.archived": "Archived",
  "inbox.filters.dropdownOptions.snoozed": "Snoozed",
  "inbox.filters.labels.unread": "Unread",
  "inbox.filters.labels.default": "Inbox",
  "inbox.filters.labels.archived": "Archived",
  "inbox.filters.labels.snoozed": "Snoozed",
  "notifications.emptyNotice": "Quiet for now. Check back later.",
  "notifications.actions.readAll": "Mark all as read",
  "notifications.actions.archiveAll": "Archive all",
  "notifications.actions.archiveRead": "Archive read",
  "notifications.newNotifications": ({ notificationCount }) => `${notificationCount > 99 ? "99+" : notificationCount} new ${notificationCount === 1 ? "notification" : "notifications"}`,
  "notification.actions.read.tooltip": "Mark as read",
  "notification.actions.unread.tooltip": "Mark as unread",
  "notification.actions.archive.tooltip": "Archive",
  "notification.actions.unarchive.tooltip": "Unarchive",
  "notification.actions.snooze.tooltip": "Snooze",
  "notification.actions.unsnooze.tooltip": "Unsnooze",
  "notification.snoozedUntil": "Snoozed until",
  "preferences.title": "Preferences",
  "preferences.emptyNotice": "No notification specific preferences yet.",
  "preferences.global": "Global Preferences",
  "preferences.workflow.disabled.notice": "Contact admin to enable subscription management for this critical notification.",
  "preferences.workflow.disabled.tooltip": "Contact admin to edit",
  "preferences.group.info": "Applies to all notifications under this group.",
  "snooze.datePicker.timePickerLabel": "Time",
  "snooze.datePicker.apply": "Apply",
  "snooze.datePicker.cancel": "Cancel",
  "snooze.options.anHourFromNow": "An hour from now",
  "snooze.datePicker.pastDateTooltip": "Selected time must be at least 3 minutes in the future",
  "snooze.datePicker.noDateSelectedTooltip": "Please select a date",
  "snooze.datePicker.exceedingLimitTooltip": ({ days }) => `Selected time cannot exceed ${days === 1 ? "24 hours" : `${days} days`} from now`,
  "snooze.options.customTime": "Custom time...",
  "snooze.options.inOneDay": "Tomorrow",
  "snooze.options.inOneWeek": "Next week"
};
var [dynamicLocalization, setDynamicLocalization] = solidJs.createSignal({});

// src/ui/config/defaultVariables.ts
var defaultVariables = {
  colorPrimary: "#7D52F4",
  colorPrimaryForeground: "white",
  colorSecondary: "#FFFFFF",
  colorSecondaryForeground: "#646464",
  colorCounter: "#FB3748",
  colorCounterForeground: "white",
  colorBackground: "#FCFCFC",
  colorRing: "#E1E4EA",
  colorForeground: "#1A1523",
  colorNeutral: "#525252",
  colorShadow: "rgb(0,0,0)",
  fontSize: "1rem",
  borderRadius: "0.375rem",
  colorStripes: "#FF9A68"
};

// src/ui/helpers/constants.ts
var DEFAULT_TARGET = "_blank";
var DEFAULT_REFERRER = "noopener noreferrer";
function createInfiniteScroll(fetcher, options) {
  const [data, setData] = solidJs.createSignal([]);
  const [initialLoading, setInitialLoading] = solidJs.createSignal(true);
  const [after, setAfter] = solidJs.createSignal(void 0);
  const [end, setEnd] = solidJs.createSignal(false);
  const [contents, { mutate, refetch }] = solidJs.createResource(
    () => ({ trigger: true, after: after() }),
    (params) => fetcher(params.after)
  );
  let observedElement = null;
  let io = null;
  solidJs.onMount(() => {
    io = new IntersectionObserver(
      (entries) => {
        var _a;
        const entry = entries[0];
        if (entry && entry.isIntersecting && !end() && !contents.loading) {
          const data2 = (_a = contents.latest) == null ? void 0 : _a.data;
          if (data2) {
            setAfter(data2[data2.length - 1][options.paginationField]);
          }
        }
      },
      {
        threshold: 0.1
      }
    );
    if (observedElement && io) {
      io.observe(observedElement);
    }
    solidJs.onCleanup(() => {
      io == null ? void 0 : io.disconnect();
      io = null;
    });
  });
  solidJs.createEffect(() => {
    if (contents.loading) return;
    const content = contents.latest;
    if (!content) return;
    setInitialLoading(false);
    solidJs.batch(() => {
      if (!content.hasMore) setEnd(true);
      setData(content.data);
      requestAnimationFrame(() => {
        checkVisibilityAndLoadMore();
      });
    });
  });
  const checkVisibilityAndLoadMore = () => {
    if (observedElement && !end() && !contents.loading) {
      const observer = new IntersectionObserver(
        (entries) => {
          var _a;
          const entry = entries[0];
          if (entry.isIntersecting) {
            const data2 = (_a = contents.latest) == null ? void 0 : _a.data;
            if (data2) {
              setAfter(data2[data2.length - 1][options.paginationField]);
            }
          }
          observer.disconnect();
        },
        {
          threshold: [0.1]
        }
      );
      observer.observe(observedElement);
      solidJs.onCleanup(() => {
        observer.disconnect();
      });
    }
  };
  const setEl = (el) => {
    if (io && observedElement) {
      io.unobserve(observedElement);
    }
    observedElement = el;
    if (io && el) {
      io.observe(el);
    }
    solidJs.onCleanup(() => {
      if (io && el) io.unobserve(el);
    });
  };
  const reset = () => chunk7B52C2XE_js.__async(this, null, function* () {
    setData([]);
    setInitialLoading(true);
    setEnd(false);
    if (after() !== void 0) {
      setAfter(void 0);
    } else {
      yield refetch();
    }
  });
  return [
    data,
    {
      initialLoading,
      setEl,
      after,
      end,
      mutate,
      reset
    }
  ];
}

// src/ui/helpers/formatToRelativeTime.ts
var DEFAULT_LOCALE = "en-US";
var SECONDS = {
  inMinute: 60,
  inHour: 3600,
  inDay: 86400,
  inWeek: 604800,
  inMonth: 2592e3
};
function formatToRelativeTime({
  fromDate,
  locale = DEFAULT_LOCALE,
  toDate = /* @__PURE__ */ new Date()
}) {
  const elapsed = toDate.getTime() - fromDate.getTime();
  const formatter = new Intl.RelativeTimeFormat(locale, { style: "narrow" });
  const diffInSeconds = Math.floor(elapsed / 1e3);
  if (Math.abs(diffInSeconds) < SECONDS.inMinute) {
    return "Just now";
  } else if (Math.abs(diffInSeconds) < SECONDS.inHour) {
    return formatter.format(Math.floor(-diffInSeconds / SECONDS.inMinute), "minute");
  } else if (Math.abs(diffInSeconds) < SECONDS.inDay) {
    return formatter.format(Math.floor(-diffInSeconds / SECONDS.inHour), "hour");
  } else if (Math.abs(diffInSeconds) < SECONDS.inMonth) {
    return formatter.format(Math.floor(-diffInSeconds / SECONDS.inDay), "day");
  } else {
    return new Intl.DateTimeFormat(locale, { month: "short", day: "numeric" }).format(fromDate);
  }
}
function formatSnoozedUntil({ untilDate, locale = DEFAULT_LOCALE }) {
  const remaining = untilDate.getTime() - (/* @__PURE__ */ new Date()).getTime();
  const diffInSeconds = Math.floor(remaining / 1e3);
  if (diffInSeconds < 0) {
    return "soon";
  }
  if (diffInSeconds < SECONDS.inMinute) {
    return "a moment";
  } else if (diffInSeconds < SECONDS.inHour) {
    const minutes = Math.floor(diffInSeconds / SECONDS.inMinute);
    return `${minutes} ${minutes === 1 ? "minute" : "minutes"}`;
  } else if (diffInSeconds < SECONDS.inDay) {
    const hours = Math.floor(diffInSeconds / SECONDS.inHour);
    return `${hours} ${hours === 1 ? "hour" : "hours"}`;
  } else if (diffInSeconds < SECONDS.inWeek) {
    const days = Math.floor(diffInSeconds / SECONDS.inDay);
    return `${days} ${days === 1 ? "day" : "days"}`;
  } else {
    return new Intl.DateTimeFormat(locale, { month: "short", day: "numeric" }).format(untilDate);
  }
}
var twMerge = tailwindMerge.extendTailwindMerge({
  prefix: "nt-"
});
var publicFacingTwMerge = tailwindMerge.extendTailwindMerge({});
function cn(...inputs) {
  return twMerge(clsx__default.default(inputs));
}
function generateRandomString(length) {
  const characters = "abcdefghijklmnopqrstuvwxyz";
  let result = "";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i += 1) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
function generateUniqueRandomString(set, length) {
  let randomString;
  do {
    randomString = generateRandomString(length);
  } while (set.has(randomString));
  return randomString;
}
function cssObjectToString(styles) {
  return Object.entries(styles).map(([key, value]) => {
    const kebabKey = key.replace(/([A-Z])/g, "-$1").toLowerCase();
    return `${kebabKey}: ${value};`;
  }).join(" ");
}
function createClassAndRuleFromCssString(classNameSet, styles) {
  const className = `novu-css-${generateUniqueRandomString(classNameSet, 8)}`;
  const rule = `.${className} { ${styles} }`;
  classNameSet.add(className);
  return { className, rule };
}
var shades = [25, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900];
function generateDefaultColor(props) {
  const cssVariableDefaultRule = `.${props.id} { --nv-${props.key}: oklch(from ${props.color} l c h); }`;
  return cssVariableDefaultRule;
}
function generateSolidShadeRulesFromColor({ color, key, id }) {
  const rules = [];
  const adjustLightness = (factor) => {
    if (factor >= 0) {
      return `min(1, calc(l + ${factor} * (1 - l)))`;
    } else {
      return `max(0, calc(l * (1 + ${factor})))`;
    }
  };
  const lightnessOffsets = {
    25: adjustLightness(0.475),
    50: adjustLightness(0.45),
    100: adjustLightness(0.4),
    200: adjustLightness(0.3),
    300: adjustLightness(0.2),
    400: adjustLightness(0.1),
    500: "l",
    600: adjustLightness(-0.1),
    700: adjustLightness(-0.2),
    800: adjustLightness(-0.3),
    900: adjustLightness(-0.4)
  };
  shades.forEach((shade) => {
    const newLightness = lightnessOffsets[shade];
    const cssVariableRule = `.${id} { --nv-${key}-${shade}: oklch(from ${color} ${newLightness} c h); }`;
    rules.push(cssVariableRule);
  });
  return rules;
}
function generateAlphaShadeRulesFromColor({ color, key, id }) {
  const rules = [];
  const alphaMap = {
    25: 0.025,
    50: 0.05,
    100: 0.1,
    200: 0.2,
    300: 0.3,
    400: 0.4,
    500: 0.5,
    600: 0.6,
    700: 0.7,
    800: 0.8,
    900: 0.9
  };
  Object.entries(alphaMap).forEach(([shade, alpha]) => {
    const cssVariableAlphaRule = `.${id} { --nv-${key}-${shade}: oklch(from ${color} l c h / ${alpha}); }`;
    rules.push(cssVariableAlphaRule);
  });
  return rules;
}
function generateFontSizeRules(props) {
  const { id, baseFontSize } = props;
  const sizeRatios = {
    xs: 0.65625,
    sm: 0.765625,
    base: 0.875,
    lg: 0.984375,
    xl: 1.09375,
    "2xl": 1.3125,
    "3xl": 1.640625,
    "4xl": 1.96875
  };
  const rules = [];
  Object.entries(sizeRatios).forEach(([key, ratio]) => {
    const size = `calc(${baseFontSize} * ${ratio})`;
    const lineHeight = `calc(${baseFontSize} * ${ratio} * 1.33)`;
    const cssVariableRule = `.${id} { --nv-font-size-${key}: ${size}; --nv-line-height-${key}: ${lineHeight}; }`;
    rules.push(cssVariableRule);
  });
  return rules;
}
function generateBorderRadiusRules(props) {
  const { id, baseRadius } = props;
  const radiusRatios = {
    none: 0,
    xs: 0.333,
    sm: 0.667,
    md: 1,
    lg: 1.333,
    xl: 2,
    "2xl": 2.667,
    "3xl": 4,
    full: 9999
  };
  const rules = [];
  Object.entries(radiusRatios).forEach(([key, ratio]) => {
    const value = key === "none" ? "0px" : key === "full" ? "9999px" : `calc(${baseRadius} * ${ratio})`;
    const cssVariableRule = `.${id} { --nv-radius-${key}: ${value}; }`;
    rules.push(cssVariableRule);
  });
  return rules;
}
var parseVariables = (variables, id) => {
  const rules = [
    generateDefaultColor({ color: variables.colorBackground, key: "color-background", id }),
    generateDefaultColor({ color: variables.colorForeground, key: "color-foreground", id }),
    generateDefaultColor({ color: variables.colorPrimary, key: "color-primary", id }),
    generateDefaultColor({ color: variables.colorPrimaryForeground, key: "color-primary-foreground", id }),
    generateDefaultColor({ color: variables.colorSecondary, key: "color-secondary", id }),
    generateDefaultColor({ color: variables.colorSecondaryForeground, key: "color-secondary-foreground", id }),
    generateDefaultColor({ color: variables.colorCounter, key: "color-counter", id }),
    generateDefaultColor({ color: variables.colorCounterForeground, key: "color-counter-foreground", id }),
    generateDefaultColor({ color: variables.colorShadow, key: "color-shadow", id }),
    generateDefaultColor({ color: variables.colorRing, key: "color-ring", id }),
    generateDefaultColor({ color: variables.colorStripes, key: "color-stripes", id }),
    ...generateAlphaShadeRulesFromColor({ color: variables.colorBackground, key: "color-background-alpha", id }),
    ...generateAlphaShadeRulesFromColor({ color: variables.colorForeground, key: "color-foreground-alpha", id }),
    ...generateSolidShadeRulesFromColor({ color: variables.colorPrimary, key: "color-primary", id }),
    ...generateAlphaShadeRulesFromColor({ color: variables.colorPrimary, key: "color-primary-alpha", id }),
    ...generateAlphaShadeRulesFromColor({
      color: variables.colorPrimaryForeground,
      key: "color-primary-foreground-alpha",
      id
    }),
    ...generateSolidShadeRulesFromColor({ color: variables.colorSecondary, key: "color-secondary", id }),
    ...generateAlphaShadeRulesFromColor({ color: variables.colorSecondary, key: "color-secondary-alpha", id }),
    ...generateAlphaShadeRulesFromColor({
      color: variables.colorSecondaryForeground,
      key: "color-secondary-foreground-alpha",
      id
    }),
    ...generateAlphaShadeRulesFromColor({ color: variables.colorNeutral, key: "color-neutral-alpha", id }),
    ...generateFontSizeRules({ id, baseFontSize: variables.fontSize }),
    ...generateBorderRadiusRules({ id, baseRadius: variables.borderRadius })
  ];
  return rules;
};
var parseElements = (elements) => {
  const elementsStyleData = [];
  const generatedClassNames = /* @__PURE__ */ new Set();
  for (const key in elements) {
    if (elements.hasOwnProperty(key)) {
      const value = elements[key];
      if (typeof value === "object") {
        const cssString = cssObjectToString(value);
        const { className, rule } = createClassAndRuleFromCssString(generatedClassNames, cssString);
        elementsStyleData.push({ key, rule, className });
      }
    }
  }
  const sortedElementsStyleData = elementsStyleData.sort((a, b) => {
    const countA = (a.key.match(/__/g) || []).length;
    const countB = (b.key.match(/__/g) || []).length;
    return countA - countB;
  });
  return sortedElementsStyleData;
};
var getTagsFromTab = (tab) => {
  var _a;
  return ((_a = tab == null ? void 0 : tab.filter) == null ? void 0 : _a.tags) || (tab == null ? void 0 : tab.value) || [];
};
var NOVU_DEFAULT_CSS_ID = "novu-default-css";

// src/ui/helpers/useStyle.ts
var useStyle = () => {
  const appearance = useAppearance();
  const [isServer, setIsServer] = solidJs.createSignal(true);
  solidJs.onMount(() => {
    setIsServer(false);
  });
  const styleFuncMemo = solidJs.createMemo(
    () => (appearanceKey, className, {
      iconKey
    } = {}) => {
      const appearanceKeyParts = appearanceKey.split("__");
      let finalAppearanceKeys = [];
      for (let i = 0; i < appearanceKeyParts.length; i += 1) {
        const accumulated = appearanceKeyParts.slice(i).join("__");
        if (appearanceKeys.includes(accumulated)) {
          finalAppearanceKeys.push(accumulated);
        }
      }
      const classes = (className == null ? void 0 : className.split(/\s+/).map((className2) => className2.replace(/^nv-/, ""))) || [];
      const appearanceKeysInClasses = classes.filter(
        (className2) => appearanceKeys.includes(className2)
      );
      finalAppearanceKeys = Array.from(
        /* @__PURE__ */ new Set([...finalAppearanceKeys, ...appearanceKeysInClasses])
      );
      finalAppearanceKeys.sort((a, b) => {
        const countA = (a.match(/__/g) || []).length;
        const countB = (b.match(/__/g) || []).length;
        return countB - countA;
      });
      const finalClassName = classes.filter((className2) => !finalAppearanceKeys.includes(className2)).join(" ");
      let appearanceClassnames = [];
      const reversedFinalAppearanceKeys = finalAppearanceKeys.reverse();
      for (let i = 0; i < reversedFinalAppearanceKeys.length; i += 1) {
        if (typeof appearance.elements()[reversedFinalAppearanceKeys[i]] === "string") {
          appearanceClassnames.push(appearance.elements()[reversedFinalAppearanceKeys[i]]);
        }
      }
      appearanceClassnames = [publicFacingTwMerge(appearanceClassnames)];
      const cssInJsClasses = !!finalAppearanceKeys.length && !isServer() ? finalAppearanceKeys.map((appKey) => appearance.appearanceKeyToCssInJsClass[appKey]) : [];
      return cn(
        ...finalAppearanceKeys.map((key) => `nv-${key}`),
        "\u{1F514}",
        iconKey ? `nv-${iconKey} \u{1F5BC}\uFE0F` : "",
        finalClassName,
        // default styles
        appearanceClassnames,
        ...cssInJsClasses
      );
    }
  );
  return styleFuncMemo();
};

// src/ui/context/AppearanceContext.tsx
var AppearanceContext = solidJs.createContext(void 0);
var AppearanceProvider = (props) => {
  const [store$1, setStore] = store.createStore({
    appearanceKeyToCssInJsClass: {}
  });
  const [styleElement, setStyleElement] = solidJs.createSignal(null);
  const [elementRules, setElementRules] = solidJs.createSignal([]);
  const [variableRules, setVariableRules] = solidJs.createSignal([]);
  const themes = solidJs.createMemo(() => {
    var _a, _b, _c;
    return Array.isArray((_a = props.appearance) == null ? void 0 : _a.baseTheme) ? ((_b = props.appearance) == null ? void 0 : _b.baseTheme) || [] : [((_c = props.appearance) == null ? void 0 : _c.baseTheme) || {}];
  });
  const id = () => props.id;
  const variables = () => {
    var _a;
    return ((_a = props.appearance) == null ? void 0 : _a.variables) || {};
  };
  const animations = () => {
    var _a, _b;
    return (_b = (_a = props.appearance) == null ? void 0 : _a.animations) != null ? _b : true;
  };
  const icons = () => {
    var _a;
    return ((_a = props.appearance) == null ? void 0 : _a.icons) || {};
  };
  const allElements = solidJs.createMemo(() => {
    var _a;
    const baseElements = themes().reduce((acc, obj) => chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, acc), obj.elements || {}), {});
    return chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, baseElements), ((_a = props.appearance) == null ? void 0 : _a.elements) || {});
  });
  const container = () => props.container;
  solidJs.onMount(() => {
    var _a;
    const root = props.container instanceof ShadowRoot ? props.container : document;
    const el = root.getElementById(props.id);
    if (el) {
      setStyleElement(el);
      return;
    }
    const stylesContainer = (_a = props.container) != null ? _a : document.head;
    const styleEl = document.createElement("style");
    styleEl.id = props.id;
    const defaultCssStyles = root.getElementById(NOVU_DEFAULT_CSS_ID);
    if (defaultCssStyles) {
      stylesContainer.insertBefore(styleEl, defaultCssStyles.nextSibling);
    } else {
      stylesContainer.appendChild(styleEl);
    }
    setStyleElement(styleEl);
    solidJs.onCleanup(() => {
      styleEl.remove();
    });
  });
  solidJs.createEffect(() => {
    var _a;
    const styleEl = styleElement();
    if (!styleEl) {
      return;
    }
    const baseVariables = chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, defaultVariables), themes().reduce((acc, obj) => chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, acc), obj.variables || {}), {}));
    setVariableRules(parseVariables(chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, baseVariables), ((_a = props.appearance) == null ? void 0 : _a.variables) || {}), props.id));
  });
  solidJs.createEffect(() => {
    const styleEl = styleElement();
    if (!styleEl) {
      return;
    }
    const elementsStyleData = parseElements(allElements());
    setStore("appearanceKeyToCssInJsClass", (obj) => chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, obj), elementsStyleData.reduce((acc, item) => {
      acc[item.key] = item.className;
      return acc;
    }, {})));
    setElementRules(elementsStyleData.map((el) => el.rule));
  });
  solidJs.createEffect(() => {
    const styleEl = styleElement();
    if (!styleEl) {
      return;
    }
    styleEl.innerHTML = [...variableRules(), ...elementRules()].join(" ");
  });
  return web.createComponent(AppearanceContext.Provider, {
    get value() {
      return {
        elements: allElements,
        variables,
        animations,
        icons,
        appearanceKeyToCssInJsClass: store$1.appearanceKeyToCssInJsClass,
        // stores are reactive
        id,
        container
      };
    },
    get children() {
      return props.children;
    }
  });
};
function useAppearance() {
  const context = solidJs.useContext(AppearanceContext);
  if (!context) {
    throw new Error("useAppearance must be used within an AppearanceProvider");
  }
  return context;
}
var useNovuEvent = ({
  event,
  eventHandler
}) => {
  const novu = useNovu();
  solidJs.onMount(() => {
    const cleanup = novu.on(event, eventHandler);
    solidJs.onCleanup(() => {
      cleanup();
    });
  });
};

// src/ui/helpers/browser.ts
function requestLock(id, cb) {
  let isFulfilled = false;
  let promiseResolve;
  const promise = new Promise((resolve) => {
    promiseResolve = resolve;
  });
  navigator.locks.request(id, () => {
    if (!isFulfilled) {
      cb(id);
    }
    return promise;
  });
  return () => {
    isFulfilled = true;
    promiseResolve();
  };
}
var useBrowserTabsChannel = ({
  channelName,
  onMessage
}) => {
  const [tabsChannel] = solidJs.createSignal(new BroadcastChannel(channelName));
  const postMessage = (args) => {
    const channel = tabsChannel();
    channel.postMessage(args);
  };
  solidJs.onMount(() => {
    const listener = (event) => {
      onMessage(event.data);
    };
    const channel = tabsChannel();
    channel.addEventListener("message", listener);
    solidJs.onCleanup(() => {
      channel.removeEventListener("message", listener);
    });
  });
  return { postMessage };
};

// src/ui/helpers/useWebSocketEvent.ts
var useWebSocketEvent = ({
  event: webSocketEvent,
  eventHandler: onMessage
}) => {
  const novu = useNovu();
  const channelName = `nv_ws_connection:a=${novu.applicationIdentifier}:s=${novu.subscriberId}:e=${webSocketEvent}`;
  const { postMessage } = useBrowserTabsChannel({ channelName, onMessage });
  const updateReadCount = (data) => {
    onMessage(data);
    postMessage(data);
  };
  solidJs.onMount(() => {
    let cleanup;
    const resolveLock = requestLock(channelName, () => {
      cleanup = novu.on(webSocketEvent, updateReadCount);
    });
    solidJs.onCleanup(() => {
      if (cleanup) {
        cleanup();
      }
      resolveLock();
    });
  });
};
var LocalizationContext = solidJs.createContext(void 0);
var LocalizationProvider = (props) => {
  const localization = solidJs.createMemo(() => {
    const _a = props.localization || {}, {
      dynamic
    } = _a, localizationObject = chunk7B52C2XE_js.__objRest(_a, [
      "dynamic"
    ]);
    return chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, defaultLocalization), dynamicLocalization()), dynamic || {}), localizationObject);
  });
  const t = (key, ...args) => {
    const value = localization()[key];
    if (typeof value === "function") {
      return value(args[0]);
    }
    return value;
  };
  const locale = solidJs.createMemo(() => localization().locale);
  return web.createComponent(LocalizationContext.Provider, {
    value: {
      t,
      locale
    },
    get children() {
      return props.children;
    }
  });
};
function useLocalization() {
  const context = solidJs.useContext(LocalizationContext);
  if (!context) {
    throw new Error("useLocalization must be used within an LocalizationProvider");
  }
  return context;
}

// src/ui/types.ts
var NotificationStatus = /* @__PURE__ */ ((NotificationStatus2) => {
  NotificationStatus2["UNREAD_READ"] = "unreadRead";
  NotificationStatus2["UNREAD"] = "unread";
  NotificationStatus2["ARCHIVED"] = "archived";
  NotificationStatus2["SNOOZED"] = "snoozed";
  return NotificationStatus2;
})(NotificationStatus || {});

// src/ui/context/InboxContext.tsx
var InboxContext = solidJs.createContext(void 0);
var STATUS_TO_FILTER = {
  ["unreadRead" /* UNREAD_READ */]: {
    archived: false,
    snoozed: false
  },
  ["unread" /* UNREAD */]: {
    read: false,
    snoozed: false
  },
  ["archived" /* ARCHIVED */]: {
    archived: true
  },
  ["snoozed" /* SNOOZED */]: {
    snoozed: true
  }
};
var DEFAULT_LIMIT = 10;
var InboxProvider = (props) => {
  var _a, _b;
  const [isOpened, setIsOpened] = solidJs.createSignal(false);
  const [tabs, setTabs] = solidJs.createSignal(props.tabs);
  const [activeTab, setActiveTab] = solidJs.createSignal((_a = props.tabs[0] && props.tabs[0].label) != null ? _a : "");
  const [status, setStatus] = solidJs.createSignal("unreadRead" /* UNREAD_READ */);
  const [limit, setLimit] = solidJs.createSignal(DEFAULT_LIMIT);
  const [filter, setFilter] = solidJs.createSignal(chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, STATUS_TO_FILTER["unreadRead" /* UNREAD_READ */]), {
    tags: props.tabs.length > 0 ? getTagsFromTab(props.tabs[0]) : [],
    data: props.tabs.length > 0 ? (_b = props.tabs[0].filter) == null ? void 0 : _b.data : {}
  }));
  const [hideBranding, setHideBranding] = solidJs.createSignal(false);
  const [isDevelopmentMode, setIsDevelopmentMode] = solidJs.createSignal(false);
  const [maxSnoozeDurationHours, setMaxSnoozeDurationHours] = solidJs.createSignal(0);
  const isSnoozeEnabled = solidJs.createMemo(() => maxSnoozeDurationHours() > 0);
  const [preferencesFilter, setPreferencesFilter] = solidJs.createSignal(props.preferencesFilter);
  const [isKeyless, setIsKeyless] = solidJs.createSignal(false);
  const [applicationIdentifier, setApplicationIdentifier] = solidJs.createSignal(null);
  const [preferenceGroups, setPreferenceGroups] = solidJs.createSignal(props.preferenceGroups);
  const setNewStatus = (newStatus) => {
    setStatus(newStatus);
    setFilter((old) => chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, STATUS_TO_FILTER[newStatus]), {
      tags: old.tags,
      data: old.data
    }));
  };
  const setNewActiveTab = (newActiveTab) => {
    const tab = tabs().find((tab2) => tab2.label === newActiveTab);
    const tags = getTagsFromTab(tab);
    if (!tags) {
      return;
    }
    setActiveTab(newActiveTab);
    setFilter((old) => {
      var _a2;
      return chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, old), {
        tags,
        data: (_a2 = tab == null ? void 0 : tab.filter) == null ? void 0 : _a2.data
      });
    });
  };
  const navigate = (url, target) => {
    if (!url) {
      return;
    }
    const isAbsoluteUrl = !url.startsWith("/");
    if (isAbsoluteUrl) {
      window.open(url, target != null ? target : DEFAULT_TARGET, DEFAULT_REFERRER);
      return;
    }
    if (props.routerPush) {
      props.routerPush(url);
      return;
    }
    const fullUrl = new URL(url, window.location.href);
    const pushState = window.history.pushState.bind(window.history);
    pushState({}, "", fullUrl);
  };
  solidJs.createEffect(() => {
    var _a2;
    setTabs(props.tabs);
    const firstTab = props.tabs[0];
    const tags = getTagsFromTab(firstTab);
    setActiveTab((_a2 = firstTab == null ? void 0 : firstTab.label) != null ? _a2 : "");
    setFilter((old) => {
      var _a3;
      return chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, old), {
        tags,
        data: (_a3 = firstTab == null ? void 0 : firstTab.filter) == null ? void 0 : _a3.data
      });
    });
    setPreferencesFilter(props.preferencesFilter);
    setPreferenceGroups(props.preferenceGroups);
  });
  useNovuEvent({
    event: "session.initialize.resolved",
    eventHandler: ({
      data
    }) => {
      var _a2, _b2;
      if (!data) {
        return;
      }
      const identifier = window.localStorage.getItem("novu_keyless_application_identifier");
      setHideBranding(data.removeNovuBranding);
      setIsDevelopmentMode(data.isDevelopmentMode);
      setMaxSnoozeDurationHours(data.maxSnoozeDurationHours);
      if (data.isDevelopmentMode && !props.applicationIdentifier) {
        setIsKeyless(!data.applicationIdentifier || !!(identifier == null ? void 0 : identifier.startsWith("pk_keyless_")));
        setApplicationIdentifier((_a2 = data.applicationIdentifier) != null ? _a2 : null);
      } else {
        setApplicationIdentifier((_b2 = props.applicationIdentifier) != null ? _b2 : null);
      }
    }
  });
  return web.createComponent(InboxContext.Provider, {
    value: {
      status,
      setStatus: setNewStatus,
      filter,
      tabs,
      activeTab,
      setActiveTab: setNewActiveTab,
      limit,
      setLimit,
      isOpened,
      setIsOpened,
      navigate,
      hideBranding,
      preferencesFilter,
      preferenceGroups,
      isDevelopmentMode,
      maxSnoozeDurationHours,
      isSnoozeEnabled,
      isKeyless,
      applicationIdentifier
    },
    get children() {
      return props.children;
    }
  });
};
var useInboxContext = () => {
  const context = solidJs.useContext(InboxContext);
  if (!context) {
    throw new Error("useInboxContext must be used within a InboxProvider");
  }
  return context;
};
var NovuContext = solidJs.createContext(void 0);
function NovuProvider(props) {
  const novu = solidJs.createMemo(() => props.novu || new chunkRN7LHLHM_js.Novu(props.options));
  return web.createComponent(NovuContext.Provider, {
    get value() {
      return novu();
    },
    get children() {
      return props.children;
    }
  });
}
function useNovu() {
  const context = solidJs.useContext(NovuContext);
  if (!context) {
    throw new Error("useNovu must be used within a NovuProvider");
  }
  return context;
}

// src/ui/context/CountContext.tsx
var MIN_AMOUNT_OF_NOTIFICATIONS = 1;
var CountContext = solidJs.createContext(void 0);
var CountProvider = (props) => {
  const novu = useNovu();
  const {
    isOpened,
    tabs,
    filter,
    limit
  } = useInboxContext();
  const [totalUnreadCount, setTotalUnreadCount] = solidJs.createSignal(0);
  const [unreadCounts, setUnreadCounts] = solidJs.createSignal(/* @__PURE__ */ new Map());
  const [newNotificationCounts, setNewNotificationCounts] = solidJs.createSignal(/* @__PURE__ */ new Map());
  const updateTabCounts = () => chunk7B52C2XE_js.__async(void 0, null, function* () {
    if (tabs().length === 0) {
      return;
    }
    const filters = tabs().map((tab) => {
      var _a;
      return {
        tags: getTagsFromTab(tab),
        read: false,
        archived: false,
        snoozed: false,
        data: (_a = tab.filter) == null ? void 0 : _a.data
      };
    });
    const {
      data
    } = yield novu.notifications.count({
      filters
    });
    if (!data) {
      return;
    }
    const newMap = /* @__PURE__ */ new Map();
    const {
      counts
    } = data;
    for (let i = 0; i < counts.length; i += 1) {
      const tagsKey = createKey(counts[i].filter.tags, counts[i].filter.data);
      newMap.set(tagsKey, data == null ? void 0 : data.counts[i].count);
    }
    setUnreadCounts(newMap);
  });
  solidJs.onMount(() => {
    updateTabCounts();
  });
  useWebSocketEvent({
    event: "notifications.unread_count_changed",
    eventHandler: (data) => {
      setTotalUnreadCount(data.result);
      updateTabCounts();
    }
  });
  useNovuEvent({
    event: "session.initialize.resolved",
    eventHandler: ({
      data
    }) => {
      if (!data) {
        return;
      }
      setTotalUnreadCount(data.totalUnreadCount);
    }
  });
  const updateNewNotificationCountsOrCache = (notification, tags, data) => {
    const notificationsCache = novu.notifications.cache;
    const limitValue = limit();
    const tabSpecificFilterForCache = chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, filter()), {
      tags,
      data,
      after: void 0,
      limit: limitValue
    });
    const hasEmptyCache = !notificationsCache.has(tabSpecificFilterForCache);
    if (!isOpened() && hasEmptyCache) {
      return;
    }
    const cachedData = notificationsCache.getAll(tabSpecificFilterForCache) || {
      hasMore: false,
      filter: tabSpecificFilterForCache,
      notifications: []
    };
    const hasLessThenMinAmount = ((cachedData == null ? void 0 : cachedData.notifications.length) || 0) < MIN_AMOUNT_OF_NOTIFICATIONS;
    if (hasLessThenMinAmount) {
      notificationsCache.update(tabSpecificFilterForCache, chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, cachedData), {
        notifications: [notification, ...cachedData.notifications]
      }));
      return;
    }
    setNewNotificationCounts((oldMap) => {
      const key = createKey(tags, data);
      const newMap = new Map(oldMap);
      newMap.set(key, (oldMap.get(key) || 0) + 1);
      return newMap;
    });
  };
  useWebSocketEvent({
    event: "notifications.notification_received",
    eventHandler: (_0) => chunk7B52C2XE_js.__async(void 0, [_0], function* ({
      result: notification
    }) {
      var _a;
      if (filter().archived || filter().snoozed) {
        return;
      }
      const currentTabs = tabs();
      function checkNotificationDataAgainstTabData(notificationData, tabFilterData) {
        if (!tabFilterData || Object.keys(tabFilterData).length === 0) {
          return true;
        }
        if (!notificationData) {
          return false;
        }
        return Object.entries(tabFilterData).every(([key, filterValue]) => {
          const notifValue = notificationData[key];
          if (notifValue === void 0 && filterValue !== void 0) {
            return false;
          }
          if (Array.isArray(filterValue)) {
            if (Array.isArray(notifValue)) {
              if (filterValue.length !== notifValue.length) return false;
              const sortedFilterValue = [...filterValue].sort();
              const sortedNotifValue = [...notifValue].sort();
              return sortedFilterValue.every((val, index) => val === sortedNotifValue[index]);
            } else {
              return filterValue.includes(notifValue);
            }
          } else {
            return notifValue === filterValue;
          }
        });
      }
      if (currentTabs.length > 0) {
        for (const tab of currentTabs) {
          const tabTags = getTagsFromTab(tab);
          const tabDataFilterCriteria = (_a = tab.filter) == null ? void 0 : _a.data;
          const matchesTagFilter = tabTags.length === 0 || notification.tags && tabTags.some((tag) => notification.tags.includes(tag));
          const matchesDataFilterCriteria = checkNotificationDataAgainstTabData(notification.data, tabDataFilterCriteria);
          if (matchesTagFilter && matchesDataFilterCriteria) {
            updateNewNotificationCountsOrCache(notification, tabTags, tabDataFilterCriteria);
          }
        }
      } else {
        updateNewNotificationCountsOrCache(notification, [], void 0);
      }
    })
  });
  useWebSocketEvent({
    event: "notifications.notification_received",
    eventHandler: updateTabCounts
  });
  const resetNewNotificationCounts = (key) => {
    setNewNotificationCounts((oldMap) => {
      const newMap = new Map(oldMap);
      newMap.set(key, 0);
      return newMap;
    });
  };
  return web.createComponent(CountContext.Provider, {
    value: {
      totalUnreadCount,
      unreadCounts,
      newNotificationCounts,
      resetNewNotificationCounts
    },
    get children() {
      return props.children;
    }
  });
};
var createKey = (tags, data) => {
  return JSON.stringify({
    tags: tags != null ? tags : [],
    data: data != null ? data : {}
  });
};
var useTotalUnreadCount = () => {
  const context = solidJs.useContext(CountContext);
  if (!context) {
    throw new Error("useTotalUnreadCount must be used within a CountProvider");
  }
  return {
    totalUnreadCount: context.totalUnreadCount
  };
};
var useNewMessagesCount = (props) => {
  const context = solidJs.useContext(CountContext);
  if (!context) {
    throw new Error("useNewMessagesCount must be used within a CountProvider");
  }
  const key = solidJs.createMemo(() => createKey(props.filter.tags, props.filter.data));
  const count = solidJs.createMemo(() => context.newNotificationCounts().get(key()) || 0);
  const reset = () => context.resetNewNotificationCounts(key());
  return {
    count,
    reset
  };
};
var useUnreadCount = (props) => {
  const context = solidJs.useContext(CountContext);
  if (!context) {
    throw new Error("useUnreadCount must be used within a CountProvider");
  }
  const count = solidJs.createMemo(() => context.unreadCounts().get(createKey(props.filter.tags, props.filter.data)) || 0);
  return count;
};
var useUnreadCounts = (props) => {
  const context = solidJs.useContext(CountContext);
  if (!context) {
    throw new Error("useUnreadCounts must be used within a CountProvider");
  }
  const counts = solidJs.createMemo(() => props.filters.map((filter) => {
    return context.unreadCounts().get(createKey(filter.tags, filter.data)) || 0;
  }));
  return counts;
};
function createFocusTrap({ element, enabled }) {
  const { container } = useAppearance();
  solidJs.createEffect(() => {
    const trapElement = element();
    if (!trapElement || !enabled()) return;
    const focusableElementsString = "a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex], [contenteditable]";
    const getFocusableElements = () => {
      return Array.from(trapElement.querySelectorAll(focusableElementsString)).filter(
        (el) => el.tabIndex >= 0 && !el.hasAttribute("disabled")
      );
    };
    const handleKeyDown = (event) => {
      if (event.key !== "Tab") return;
      const focusableElements2 = getFocusableElements();
      const firstFocusableElement = focusableElements2[0];
      const lastFocusableElement = focusableElements2[focusableElements2.length - 1];
      const containerElement = container();
      const root = containerElement instanceof ShadowRoot ? containerElement : document;
      if (event.shiftKey) {
        if (root.activeElement === firstFocusableElement) {
          lastFocusableElement.focus();
          event.preventDefault();
        }
      } else {
        if (root.activeElement === lastFocusableElement) {
          firstFocusableElement.focus();
          event.preventDefault();
        }
      }
    };
    trapElement.addEventListener("keydown", handleKeyDown);
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
    solidJs.onCleanup(() => {
      trapElement.removeEventListener("keydown", handleKeyDown);
    });
  });
}
var useFocusTrap_default = createFocusTrap;

// src/ui/context/FocusManagerContext.tsx
var FocusManagerContext = solidJs.createContext(void 0);
var FocusManagerProvider = (props) => {
  const [focusTraps, setFocusTraps] = solidJs.createSignal([]);
  const setActive = (element) => {
    setFocusTraps((traps) => [...traps, element]);
  };
  const removeActive = (element) => {
    setFocusTraps((traps) => traps.filter((item) => item !== element));
  };
  const active = solidJs.createMemo(() => focusTraps().length ? focusTraps()[focusTraps().length - 1] : null);
  useFocusTrap_default({
    element: () => active(),
    enabled: () => true
  });
  return web.createComponent(FocusManagerContext.Provider, {
    value: {
      focusTraps,
      active,
      setActive,
      removeActive
    },
    get children() {
      return props.children;
    }
  });
};
function useFocusManager() {
  const context = solidJs.useContext(FocusManagerContext);
  if (!context) {
    throw new Error("useFocusManager must be used within an FocusManagerProvider");
  }
  return context;
}
var _tmpl$ = /* @__PURE__ */ web.template(`<div>`);
var ExternalElementRenderer = (props) => {
  let ref;
  const [local, rest] = solidJs.splitProps(props, ["render"]);
  solidJs.createEffect(() => {
    const unmount = local.render(ref);
    solidJs.onCleanup(() => {
      unmount();
    });
  });
  return (() => {
    var _el$ = _tmpl$();
    web.use((el) => {
      ref = el;
    }, _el$);
    web.spread(_el$, rest, false, false);
    return _el$;
  })();
};
var _tmpl$2 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 20 20"><path fill=currentColor d="M5.833 8.333L10 12.5l4.166-4.167H5.833z">`);
var ArrowDropDown = (props) => {
  return (() => {
    var _el$ = _tmpl$2();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$3 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 20 20"fill=none xmlns=http://www.w3.org/2000/svg><path d="M9.20425 9.99907L12.9168 13.7116L11.8563 14.7721L7.08325 9.99907L11.8563 5.22607L12.9168 6.28657L9.20425 9.99907Z"fill=currentColor>`);
var ArrowLeft = (props) => {
  return (() => {
    var _el$ = _tmpl$3();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$4 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 20 20"fill=none xmlns=http://www.w3.org/2000/svg><path d="M10.7957 10.0009L7.08325 6.2884L8.14375 5.2279L12.9168 10.0009L8.14375 14.7739L7.08325 13.7134L10.7957 10.0009Z"fill=currentColor>`);
var ArrowRight = (props) => {
  return (() => {
    var _el$ = _tmpl$4();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$5 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 20 20"fill=none xmlns=http://www.w3.org/2000/svg><path d="M10.0001 10.879L13.7126 7.1665L14.7731 8.227L10.0001 13L5.22705 8.227L6.28755 7.1665L10.0001 10.879Z"fill=currentColor>`);
var ArrowDown = (props) => {
  return (() => {
    var _el$ = _tmpl$5();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$6 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 12"fill=none xmlns=http://www.w3.org/2000/svg><path d="M5 12C5.6875 12 6.25 11.4462 6.25 10.7692H3.75C3.75 11.4462 4.3125 12 5 12ZM8.75 8.30769V5.23077C8.75 3.34154 7.73125 1.76 5.9375 1.34154V0.923077C5.9375 0.412308 5.51875 0 5 0C4.48125 0 4.0625 0.412308 4.0625 0.923077V1.34154C2.275 1.76 1.25 3.33538 1.25 5.23077V8.30769L0 9.53846V10.1538H10V9.53846L8.75 8.30769ZM7.5 8.92308H2.5V5.23077C2.5 3.70462 3.44375 2.46154 5 2.46154C6.55625 2.46154 7.5 3.70462 7.5 5.23077V8.92308Z"fill=currentColor>`);
function Bell(props) {
  return (() => {
    var _el$ = _tmpl$6();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
}
var _tmpl$7 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M0.625 9.375L2.93989 8.86059C3.5538 9.18889 4.25516 9.375 5 9.375C7.41622 9.375 9.375 7.41622 9.375 5C9.375 2.58375 7.41622 0.625 5 0.625C2.58375 0.625 0.625 2.58375 0.625 5C0.625 5.74484 0.81113 6.4462 1.13942 7.0601L0.625 9.375ZM6.50881 2.8125L6.43224 3.68761H7.1875V4.56259H6.35568L6.27912 5.43759H7.1875V6.31259H6.2026L6.12604 7.1875H5.24771L5.32423 6.31259H4.44591L4.36934 7.1875H3.49101L3.56755 6.31259H2.8125V5.43759H3.64411L3.72066 4.56259H2.8125V3.68761H3.79721L3.87377 2.8125H4.75211L4.67555 3.68761H5.55392L5.63048 2.8125H6.50881ZM4.59899 4.56259L4.52247 5.43759H5.40079L5.47736 4.56259H4.59899Z"fill=currentColor>`);
var Chat = (props) => {
  return (() => {
    var _el$ = _tmpl$7();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$8 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 8 6"fill=none xmlns=http://www.w3.org/2000/svg><path d="M2.99994 4.58847L7.33298 0L8 0.705765L2.99994 6L0 2.82356L0.666549 2.11779L2.99994 4.58847Z"fill=currentColor>`);
var Check = (props) => {
  return (() => {
    var _el$ = _tmpl$8();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$9 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 12 12"fill=none xmlns=http://www.w3.org/2000/svg><g clip-path=url(#clip0_3188_15050)><path d="M6 3V6L8 7M11 6C11 8.76142 8.76142 11 6 11C3.23858 11 1 8.76142 1 6C1 3.23858 3.23858 1 6 1C8.76142 1 11 3.23858 11 6Z"stroke=currentColor stroke-linecap=round stroke-linejoin=round>`);
var Clock = (props) => {
  return (() => {
    var _el$ = _tmpl$9();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$10 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 20 20"><path fill=currentColor d="M5 8.333c-.917 0-1.667.75-1.667 1.667s.75 1.667 1.667 1.667c.916 0 1.666-.75 1.666-1.667S5.916 8.333 5 8.333zm10 0c-.917 0-1.667.75-1.667 1.667s.75 1.667 1.667 1.667c.916 0 1.666-.75 1.666-1.667S15.916 8.333 15 8.333zm-5 0c-.917 0-1.667.75-1.667 1.667s.75 1.667 1.667 1.667c.916 0 1.666-.75 1.666-1.667S10.916 8.333 10 8.333z">`);
var Dots = (props) => {
  return (() => {
    var _el$ = _tmpl$10();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$11 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M4.20703 1.875H2.8125H2.10547H1.875V2.04688V2.8125V3.60156V5.33984L0.00390625 3.95508C0.0351562 3.60156 0.216797 3.27344 0.505859 3.06055L0.9375 2.74023V1.875C0.9375 1.35742 1.35742 0.9375 1.875 0.9375H3.37109L4.3457 0.216797C4.53516 0.0761719 4.76367 0 5 0C5.23633 0 5.46484 0.0761719 5.6543 0.214844L6.62891 0.9375H8.125C8.64258 0.9375 9.0625 1.35742 9.0625 1.875V2.74023L9.49414 3.06055C9.7832 3.27344 9.96484 3.60156 9.99609 3.95508L8.125 5.33984V3.60156V2.8125V2.04688V1.875H7.89453H7.1875H5.79297H4.20508H4.20703ZM0 8.75V4.72852L4.25 7.87695C4.4668 8.03711 4.73047 8.125 5 8.125C5.26953 8.125 5.5332 8.03906 5.75 7.87695L10 4.72852V8.75C10 9.43945 9.43945 10 8.75 10H1.25C0.560547 10 0 9.43945 0 8.75ZM3.4375 3.125H6.5625C6.73438 3.125 6.875 3.26562 6.875 3.4375C6.875 3.60938 6.73438 3.75 6.5625 3.75H3.4375C3.26562 3.75 3.125 3.60938 3.125 3.4375C3.125 3.26562 3.26562 3.125 3.4375 3.125ZM3.4375 4.375H6.5625C6.73438 4.375 6.875 4.51562 6.875 4.6875C6.875 4.85938 6.73438 5 6.5625 5H3.4375C3.26562 5 3.125 4.85938 3.125 4.6875C3.125 4.51562 3.26562 4.375 3.4375 4.375Z"fill=currentColor>`);
var Email = (props) => {
  return (() => {
    var _el$ = _tmpl$11();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$12 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 12"fill=none xmlns=http://www.w3.org/2000/svg><path d="M4.99962 0.856934C4.64404 0.856934 4.35676 1.14421 4.35676 1.49979V1.88551C2.89024 2.18283 1.78533 3.48059 1.78533 5.03551V5.41318C1.78533 6.35738 1.43779 7.26943 0.810999 7.97658L0.662339 8.14332C0.493589 8.33216 0.45341 8.60336 0.555865 8.83439C0.658321 9.06542 0.889348 9.21408 1.14247 9.21408H8.85676C9.10988 9.21408 9.3389 9.06542 9.44337 8.83439C9.54783 8.60336 9.50564 8.33216 9.33689 8.14332L9.18823 7.97658C8.56145 7.26943 8.2139 6.35939 8.2139 5.41318V5.03551C8.2139 3.48059 7.10899 2.18283 5.64247 1.88551V1.49979C5.64247 1.14421 5.3552 0.856934 4.99962 0.856934ZM5.90966 10.767C6.15073 10.5259 6.28533 10.1985 6.28533 9.85693H4.99962H3.7139C3.7139 10.1985 3.8485 10.5259 4.08957 10.767C4.33064 11.008 4.6581 11.1426 4.99962 11.1426C5.34113 11.1426 5.66859 11.008 5.90966 10.767Z"fill=currentColor>`);
var InApp = (props) => {
  return (() => {
    var _el$ = _tmpl$12();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$13 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M2.29671 10C1.78742 10 1.39807 9.85716 1.12864 9.57149C0.862497 9.28581 0.729426 8.86623 0.729426 8.31274V2.64594H1.69543V8.29668C1.69543 8.52163 1.74964 8.69487 1.85806 8.81624C1.96978 8.93408 2.12914 8.99301 2.33614 8.99301H7.66389C7.86764 8.99301 8.02366 8.93408 8.13209 8.81624C8.24385 8.69487 8.29965 8.52163 8.29965 8.29668V2.64594H9.27059V8.31274C9.27059 8.8627 9.13591 9.28048 8.86648 9.56608C8.59705 9.85536 8.20931 10 7.70333 10H2.29671ZM3.41056 5.34543C3.29556 5.34543 3.20028 5.30438 3.1247 5.22226C3.04913 5.14015 3.01134 5.03304 3.01134 4.90089V4.72949C3.01134 4.59737 3.04749 4.49204 3.11977 4.41348C3.19535 4.33492 3.29227 4.29564 3.41056 4.29564H6.5944C6.71271 4.29564 6.80795 4.33492 6.88026 4.41348C6.95582 4.49204 6.9936 4.59737 6.9936 4.72949V4.90089C6.9936 5.03304 6.95582 5.14015 6.88026 5.22226C6.8047 5.30438 6.70939 5.34543 6.5944 5.34543H3.41056ZM1.05964 3.16014C0.724502 3.16014 0.463285 3.05301 0.276004 2.83877C0.0920037 2.62095 0 2.33172 0 1.97107V1.18907C0 0.824846 0.0952841 0.535614 0.28586 0.321373C0.476428 0.107124 0.734358 0 1.05964 0H8.94536C9.27715 0 9.53511 0.107124 9.71911 0.321373C9.90642 0.535614 10 0.824846 10 1.18907V1.97107C10 2.33172 9.90642 2.62095 9.71911 2.83877C9.53511 3.05301 9.27715 3.16014 8.94536 3.16014H1.05964ZM1.24693 2.19067H8.75805C8.87304 2.19067 8.95516 2.16211 9.00448 2.10497C9.05372 2.04427 9.07838 1.95322 9.07838 1.83181V1.32833C9.07838 1.20335 9.05372 1.1123 9.00448 1.05517C8.95516 0.99803 8.87304 0.969462 8.75805 0.969462H1.24693C1.13193 0.969462 1.04814 0.99803 0.995567 1.05517C0.946281 1.1123 0.921638 1.20335 0.921638 1.32833V1.83181C0.921638 1.95322 0.946281 2.04427 0.995567 2.10497C1.04814 2.16211 1.13193 2.19067 1.24693 2.19067Z"fill=currentColor>`);
var MarkAsArchived = (props) => {
  return (() => {
    var _el$ = _tmpl$13();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$14 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 11 11"fill=none xmlns=http://www.w3.org/2000/svg><path d="M2.17256 10.999C1.69081 10.999 1.3225 10.8562 1.06763 10.5705C0.815875 10.2848 0.689997 9.86525 0.689997 9.31177V3.64497H1.60378V9.2957C1.60378 9.52066 1.65506 9.6939 1.75763 9.81526C1.8633 9.93311 2.01405 9.99203 2.20986 9.99203H7.24963C7.44236 9.99203 7.58995 9.93311 7.69252 9.81526C7.79823 9.6939 7.85102 9.52066 7.85102 9.2957V3.64497H8.76947V9.31177C8.76947 9.86173 8.64208 10.2795 8.38721 10.5651C8.13235 10.8544 7.76556 10.999 7.28693 10.999H2.17256ZM1.00236 4.15916C0.68534 4.15916 0.438242 4.05204 0.261085 3.83779C0.0870305 3.61997 0 3.33074 0 2.97009V2.18809C0 1.82387 0.0901336 1.53464 0.270408 1.3204C0.450675 1.10615 0.694663 0.999023 1.00236 0.999023H8.46182C8.77568 0.999023 9.0197 1.10615 9.19375 1.3204C9.37094 1.53464 9.45946 1.82387 9.45946 2.18809V2.97009C9.45946 3.33074 9.37094 3.61997 9.19375 3.83779C9.0197 4.05204 8.77568 4.15916 8.46182 4.15916H1.00236ZM1.17953 3.1897H8.28464C8.39342 3.1897 8.4711 3.16113 8.51775 3.10399C8.56433 3.04329 8.58765 2.95224 8.58765 2.83083V2.32735C8.58765 2.20238 8.56433 2.11132 8.51775 2.05419C8.4711 1.99705 7.51461 1.96849 7.40583 1.96849H1.17953C1.07074 1.96849 0.991485 1.99705 0.941753 2.05419C0.895131 2.11132 0.87182 2.20238 0.87182 2.32735V2.83083C0.87182 2.95224 0.895131 3.04329 0.941753 3.10399C0.991485 3.16113 1.07074 3.1897 1.17953 3.1897Z"fill=currentColor></path><path d="M9.67298 0.553711C9.84703 0.556646 10.0146 0.614475 10.1535 0.716797L10.2208 0.771484L10.2814 0.833008C10.3958 0.960612 10.4679 1.11928 10.4913 1.28711L10.4992 1.37109L10.4982 1.45605C10.4872 1.64689 10.4124 1.8301 10.2833 1.97559L10.2843 1.97656L7.55482 5.15039L7.55384 5.14941C7.40234 5.3265 7.18382 5.43557 6.94642 5.44336L6.93861 5.44434H6.92005V5.44336C6.69203 5.44397 6.47619 5.35201 6.31947 5.19141L6.31849 5.18945L5.29505 4.13184C5.08531 3.91498 5.00658 3.60427 5.08118 3.31641L5.11634 3.21094C5.2129 2.97124 5.41476 2.78187 5.67396 2.70996L5.78626 2.68652C6.01138 2.65637 6.23763 2.72008 6.41419 2.85938L6.49818 2.93555L6.8849 3.33496L9.0138 0.859375V0.860352C9.15512 0.688807 9.35911 0.576792 9.58509 0.556641L9.67298 0.553711Z"fill=currentColor stroke=white>`);
var MarkAsArchivedRead = (props) => {
  return (() => {
    var _el$ = _tmpl$14();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$15 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><g clip-path=url(#clip0_3445_1172)><path d="M9 9.99902H1C0.867383 9.99902 0.7402 9.94635 0.64645 9.85257C0.552667 9.75882 0.5 9.63164 0.5 9.49902V0.499023C0.5 0.366407 0.552669 0.239223 0.64645 0.145473C0.7402 0.0516901 0.867383 -0.000976562 1 -0.000976562H6.25C6.42865 -0.000976562 6.59368 0.0943401 6.68301 0.249023C6.77233 0.403707 6.77233 0.59434 6.68301 0.749023C6.59368 0.903707 6.42865 0.999023 6.25 0.999023H1.5V8.99902H8.5V4.49902C8.5 4.32037 8.59532 4.15534 8.75 4.06602C8.90468 3.97669 9.09532 3.97669 9.25 4.06602C9.40468 4.15534 9.5 4.32037 9.5 4.49902V9.49902C9.5 9.63164 9.44733 9.75882 9.35355 9.85257C9.2598 9.94636 9.13262 9.99902 9 9.99902Z"fill=currentColor></path><path d="M7.5 8.24902H2.5C2.32135 8.24902 2.15632 8.15371 2.06699 7.99902C1.97767 7.84434 1.97767 7.65371 2.06699 7.49902C2.15632 7.34434 2.32135 7.24902 2.5 7.24902H7.5C7.67865 7.24902 7.84368 7.34434 7.93301 7.49902C8.02233 7.65371 8.02233 7.84434 7.93301 7.99902C7.84368 8.15371 7.67865 8.24902 7.5 8.24902Z"fill=currentColor></path><path d="M4.75 6.49901C4.61709 6.49979 4.48936 6.44761 4.39498 6.35403L2.89498 4.85403C2.76816 4.72717 2.71865 4.54235 2.76507 4.36907C2.81149 4.19583 2.94681 4.06051 3.12005 4.01409C3.29332 3.96767 3.47816 4.01718 3.60501 4.14401L4.73001 5.269L8.37501 1.16901C8.46056 1.06279 8.58578 0.996155 8.72169 0.984497C8.8576 0.972843 8.99233 1.01718 9.09474 1.10728C9.19712 1.19738 9.25825 1.32541 9.26398 1.46167C9.26968 1.59796 9.21948 1.73065 9.12502 1.82902L5.12502 6.32902C5.03371 6.43306 4.90337 6.49461 4.76502 6.49901L4.75 6.49901Z"fill=currentColor></path></g><defs><clipPath id=clip0_3445_1172><rect width=10 height=10 fill=white transform="translate(0 -0.000976562)">`);
var MarkAsRead = (props) => {
  return (() => {
    var _el$ = _tmpl$15();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$16 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M3.15789 2.99902V4.99902L0 2.49902L3.15789 -0.000976562V1.99902H5.78947C6.90618 1.99902 7.97714 2.42045 8.76677 3.1706C9.55639 3.92074 10 4.93816 10 5.99902C10 7.05989 9.55639 8.0773 8.76677 8.82745C7.97714 9.5776 6.90618 9.99902 5.78947 9.99902H1.05263V8.99902H5.78947C6.627 8.99902 7.43022 8.68295 8.02244 8.12034C8.61466 7.55773 8.94737 6.79467 8.94737 5.99902C8.94737 5.20337 8.61466 4.44031 8.02244 3.8777C7.43022 3.31509 6.627 2.99902 5.78947 2.99902H3.15789Z"fill=currentColor>`);
var MarkAsUnarchived = (props) => {
  return (() => {
    var _el$ = _tmpl$16();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$17 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 11 11"fill=none xmlns=http://www.w3.org/2000/svg><path d="M6.8 1.49902H1.5C0.947715 1.49902 0.5 1.94674 0.5 2.49902V9.49902C0.5 10.0513 0.947715 10.499 1.5 10.499H8.5C9.05228 10.499 9.5 10.0513 9.5 9.49902V4.19902"stroke=currentColor stroke-miterlimit=1 stroke-linecap=round></path><circle cx=9.25 cy=1.74902 r=1.25 fill=currentColor>`);
var MarkAsUnread = (props) => {
  return (() => {
    var _el$ = _tmpl$17();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$18 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 13 12"><path fill=currentColor d="M9.787.98A5.972 5.972 0 006.5 0c-.668 0-1.31.11-1.911.31L9.187 4.94c.221.222.6.065.6-.248V.98z"></path><path fill=currentColor d="M2.879 1.216A5.99 5.99 0 00.5 6c0 1.134.315 2.195.862 3.1V7.309c0-1.966 2.379-2.946 3.764-1.552l4.995 5.027A5.99 5.99 0 0012.5 6a5.972 5.972 0 00-.862-3.1v1.791c0 1.966-2.379 2.946-3.764 1.552L2.879 1.216z"></path><path fill=currentColor d="M8.411 11.69L3.813 7.06a.351.351 0 00-.6.248v3.711c.944.62 2.073.98 3.287.98.668 0 1.31-.11 1.911-.31z">`);
var Novu2 = (props) => {
  return (() => {
    var _el$ = _tmpl$18();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$19 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 16"fill=none xmlns=http://www.w3.org/2000/svg><path d="M4.12531 1.8999C3.94958 1.8999 3.80713 2.04235 3.80713 2.21808C3.80713 2.39382 3.94958 2.53627 4.12531 2.53627H6.0344C6.21013 2.53627 6.35258 2.39382 6.35258 2.21808C6.35258 2.04235 6.21013 1.8999 6.0344 1.8999H4.12531Z"fill=currentColor></path><path d="M4.12531 1.8999C3.94958 1.8999 3.80713 2.04235 3.80713 2.21808C3.80713 2.39382 3.94958 2.53627 4.12531 2.53627H6.0344C6.21013 2.53627 6.35258 2.39382 6.35258 2.21808C6.35258 2.04235 6.21013 1.8999 6.0344 1.8999H4.12531Z"stroke=currentColor></path><path d="M2.69329 1.46818H7.30693C7.75127 1.46818 8.11147 1.82839 8.11147 2.27273V13.7273C8.11147 14.1716 7.75127 14.5318 7.30693 14.5318H2.69329C2.24896 14.5318 1.88875 14.1716 1.88875 13.7273V2.27273C1.88875 1.82839 2.24896 1.46818 2.69329 1.46818ZM2.69329 0.85C1.90754 0.85 1.27057 1.48698 1.27057 2.27273V2.95695C1.17568 3.00972 1.11147 3.111 1.11147 3.22727V3.54545C1.11147 3.64155 1.15532 3.7274 1.22411 3.78409C1.15532 3.84078 1.11147 3.92663 1.11147 4.02273V4.65909C1.11147 4.75519 1.15532 4.84104 1.22411 4.89773C1.15532 4.95442 1.11147 5.04027 1.11147 5.13636V6.09091C1.11147 6.20718 1.17568 6.30846 1.27057 6.36123V13.7273C1.27057 14.513 1.90754 15.15 2.69329 15.15H7.30693C8.09268 15.15 8.72966 14.513 8.72966 13.7273V6.36123C8.82454 6.30846 8.88875 6.20718 8.88875 6.09091V4.81818C8.88875 4.70191 8.82454 4.60063 8.72966 4.54786V2.27273C8.72966 1.48698 8.09268 0.85 7.30693 0.85H2.69329Z"fill=currentColor stroke=currentColor stroke-width=0.3>`);
var Push = (props) => {
  return (() => {
    var _el$ = _tmpl$19();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$20 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 20 20"fill=none xmlns=http://www.w3.org/2000/svg><path d="M10 1.75L17.125 5.875V14.125L10 18.25L2.875 14.125V5.875L10 1.75ZM10 3.48325L4.375 6.73975V13.2603L10 16.5167L15.625 13.2603V6.73975L10 3.48325ZM10 13C9.20435 13 8.44129 12.6839 7.87868 12.1213C7.31607 11.5587 7 10.7956 7 10C7 9.20435 7.31607 8.44129 7.87868 7.87868C8.44129 7.31607 9.20435 7 10 7C10.7956 7 11.5587 7.31607 12.1213 7.87868C12.6839 8.44129 13 9.20435 13 10C13 10.7956 12.6839 11.5587 12.1213 12.1213C11.5587 12.6839 10.7956 13 10 13ZM10 11.5C10.3978 11.5 10.7794 11.342 11.0607 11.0607C11.342 10.7794 11.5 10.3978 11.5 10C11.5 9.60218 11.342 9.22064 11.0607 8.93934C10.7794 8.65804 10.3978 8.5 10 8.5C9.60218 8.5 9.22064 8.65804 8.93934 8.93934C8.65804 9.22064 8.5 9.60218 8.5 10C8.5 10.3978 8.65804 10.7794 8.93934 11.0607C9.22064 11.342 9.60218 11.5 10 11.5Z"fill=currentColor>`);
var Cogs = (props) => {
  return (() => {
    var _el$ = _tmpl$20();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$21 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M5.00051 9.28364C7.76195 9.28364 10 7.20598 10 4.64182C10 2.07766 7.76195 0 5.00051 0C2.23907 0 0.00101462 2.07766 0.00101462 4.64182C0.00101462 5.64829 0.346683 6.57889 0.932561 7.33988C0.895455 7.88663 0.709927 8.37313 0.514634 8.74358C0.407223 8.94889 0.297859 9.11404 0.21779 9.22562C0.176778 9.28141 0.145531 9.32381 0.122096 9.35282C0.110379 9.36621 0.102567 9.37737 0.096708 9.38407L0.0908493 9.39076C0.00101462 9.49342 -0.0243734 9.64517 0.0244497 9.77907C0.0732729 9.91297 0.186543 10 0.313483 10C0.873973 10 1.43837 9.80138 1.90707 9.56929C2.35429 9.34613 2.73511 9.08056 2.96751 8.88641C3.58854 9.14305 4.27597 9.28587 5.00051 9.28587V9.28364ZM1.87582 4.03481C1.87582 3.58179 2.19806 3.21357 2.5945 3.21357H2.96946C3.14132 3.21357 3.28193 3.37425 3.28193 3.57063C3.28193 3.76702 3.14132 3.92769 2.96946 3.92769H2.5945C2.54177 3.92769 2.50076 3.97679 2.50076 4.03481C2.50076 4.07052 2.51638 4.10399 2.54373 4.12408L3.11789 4.56148C3.31904 4.71323 3.43817 4.96987 3.43817 5.2466C3.43817 5.69962 3.11593 6.06784 2.71949 6.06784L2.18829 6.07007C2.01644 6.07007 1.87582 5.9094 1.87582 5.71301C1.87582 5.51663 2.01644 5.35595 2.18829 5.35595H2.71949C2.77222 5.35595 2.81323 5.30685 2.81323 5.24883C2.81323 5.21312 2.79761 5.17965 2.77026 5.15956L2.1961 4.72216C1.99691 4.56818 1.87582 4.31154 1.87582 4.03481ZM7.28153 3.21357H7.65649C7.82834 3.21357 7.96896 3.37425 7.96896 3.57063C7.96896 3.76702 7.82834 3.92769 7.65649 3.92769H7.28153C7.2288 3.92769 7.18779 3.97679 7.18779 4.03481C7.18779 4.07052 7.20341 4.10399 7.23075 4.12408L7.80491 4.56148C8.00411 4.71323 8.12519 4.96987 8.12519 5.2466C8.12519 5.69962 7.80296 6.06784 7.40651 6.06784L6.87532 6.07007C6.70346 6.07007 6.56285 5.9094 6.56285 5.71301C6.56285 5.51663 6.70346 5.35595 6.87532 5.35595H7.40651C7.45924 5.35595 7.50025 5.30685 7.50025 5.24883C7.50025 5.21312 7.48463 5.17965 7.45729 5.15956L6.88313 4.72216C6.68393 4.57041 6.56285 4.31377 6.56285 4.03705C6.56285 3.58402 6.88508 3.2158 7.28153 3.2158V3.21357ZM4.31308 3.35639L5.00051 4.40304L5.68794 3.35639C5.76801 3.23365 5.90862 3.18233 6.03751 3.23142C6.1664 3.28052 6.25038 3.41665 6.25038 3.57063V5.71301C6.25038 5.9094 6.10977 6.07007 5.93791 6.07007C5.76605 6.07007 5.62544 5.9094 5.62544 5.71301V4.64182L5.25048 5.21312C5.19189 5.30239 5.09815 5.35595 5.00051 5.35595C4.90286 5.35595 4.80912 5.30239 4.75053 5.21312L4.37557 4.64182V5.71301C4.37557 5.9094 4.23496 6.07007 4.0631 6.07007C3.89124 6.07007 3.75063 5.9094 3.75063 5.71301V3.57063C3.75063 3.41665 3.83656 3.28052 3.9635 3.23142C4.09044 3.18233 4.23105 3.23365 4.31308 3.35639Z"fill=currentColor>`);
var Sms = (props) => {
  return (() => {
    var _el$ = _tmpl$21();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$22 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 8"fill=none xmlns=http://www.w3.org/2000/svg><path d="M1.0119 0.347055C1.06274 0.143703 1.26565 -0.000976562 1.5 -0.000976562H8.5C8.73435 -0.000976562 8.93725 0.143703 8.9881 0.347055L9.9881 4.34707C9.996 4.37871 10 4.41102 10 4.44347V7.55458C10 7.80005 9.77615 7.99902 9.5 7.99902H0.5C0.22386 7.99902 0 7.80005 0 7.55458V4.44347C0 4.41102 0.00399495 4.37871 0.011905 4.34707L1.0119 0.347055ZM1.90108 0.887912L1.12331 3.99902H3.5C3.5 4.73542 4.17155 5.33236 5 5.33236C5.82845 5.33236 6.5 4.73542 6.5 3.99902H8.8767L8.0989 0.887912H1.90108ZM7.292 4.88791C6.9062 5.67276 6.02515 6.22125 5 6.22125C3.97484 6.22125 3.0938 5.67276 2.70802 4.88791H1V7.11013H9V4.88791H7.292Z"fill=currentColor>`);
var Unread = (props) => {
  return (() => {
    var _el$ = _tmpl$22();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$23 = /* @__PURE__ */ web.template(`<svg viewBox="0 0 10 10"fill=none xmlns=http://www.w3.org/2000/svg><path d="M4.99992 2.91634V4.99967M4.79992 5.39616L3.27392 6.46553M1.66659 1.66634L8.33325 8.33301M9.16658 4.99967C9.16658 7.30086 7.30111 9.16634 4.99992 9.16634C2.69873 9.16634 0.833252 7.30086 0.833252 4.99967C0.833252 2.69849 2.69873 0.833008 4.99992 0.833008C7.30111 0.833008 9.16658 2.69849 9.16658 4.99967Z"stroke=currentColor stroke-linecap=round stroke-linejoin=round>`);
var Unsnooze = (props) => {
  return (() => {
    var _el$ = _tmpl$23();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var IconRendererWrapper = (props) => {
  const appearance = useAppearance();
  const customRenderer = () => {
    var _a;
    return (_a = appearance.icons()) == null ? void 0 : _a[props.iconKey];
  };
  return web.createComponent(solidJs.Show, {
    get when() {
      return customRenderer();
    },
    get fallback() {
      return props.fallback;
    },
    get children() {
      return web.createComponent(ExternalElementRenderer, {
        render: (el) => customRenderer()(el, {
          class: props.class
        })
      });
    }
  });
};

// src/ui/components/elements/Bell/DefaultBellContainer.tsx
var _tmpl$24 = /* @__PURE__ */ web.template(`<span>`);
var BellContainer = (props) => {
  const style = useStyle();
  const bellIconStyle = style("bellIcon", "nt-size-4");
  return (() => {
    var _el$ = _tmpl$24();
    web.insert(_el$, web.createComponent(IconRendererWrapper, {
      iconKey: "bell",
      "class": bellIconStyle,
      get fallback() {
        return web.createComponent(Bell, {
          "class": bellIconStyle
        });
      }
    }), null);
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return props.unreadCount > 0;
      },
      get children() {
        var _el$2 = _tmpl$24();
        web.effect(() => web.className(_el$2, style("bellDot", "nt-absolute nt-top-0 nt-right-0 nt-block nt-size-2 nt-transform nt-bg-counter nt-rounded-full nt-border nt-border-background")));
        return _el$2;
      }
    }), null);
    web.effect(() => web.className(_el$, style("bellContainer", `nt-size-4 nt-flex nt-justify-center nt-items-center nt-relative nt-text-foreground nt-cursor-pointer`)));
    return _el$;
  })();
};

// src/ui/components/elements/Bell/Bell.tsx
var Bell2 = (props) => {
  const {
    totalUnreadCount
  } = useTotalUnreadCount();
  return web.createComponent(solidJs.Show, {
    get when() {
      return props.renderBell;
    },
    get fallback() {
      return web.createComponent(BellContainer, {
        get unreadCount() {
          return totalUnreadCount();
        }
      });
    },
    get children() {
      return web.createComponent(ExternalElementRenderer, {
        render: (el) => props.renderBell(el, totalUnreadCount())
      });
    }
  });
};

// src/utils/is-browser.ts
function isBrowser() {
  return typeof window !== "undefined";
}
var _tmpl$25 = /* @__PURE__ */ web.template(`<svg width=6 height=6 viewBox="0 0 6 6"fill=none xmlns=http://www.w3.org/2000/svg><path d="M5.00175 1.70402L0.705765 6L0 5.29424L4.29548 0.998253H0.509608V0H6V5.49039H5.00175V1.70402Z"fill=currentColor>`);
var ArrowUpRight = (props) => {
  return (() => {
    var _el$ = _tmpl$25();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var TooltipContext = solidJs.createContext(void 0);
function TooltipRoot(props) {
  var _a;
  const [reference, setReference] = solidJs.createSignal(null);
  const [floating, setFloating] = solidJs.createSignal(null);
  const {
    animations
  } = useAppearance();
  const defaultAnimationDuration = 0.2;
  const actualAnimationDuration = () => {
    var _a2;
    return (_a2 = props.animationDuration) != null ? _a2 : defaultAnimationDuration;
  };
  const effectiveAnimationDuration = solidJs.createMemo(() => animations() ? actualAnimationDuration() : 0);
  const position = solidFloatingUi.useFloating(reference, floating, {
    placement: props.placement || "top",
    strategy: "fixed",
    whileElementsMounted: dom.autoUpdate,
    middleware: [dom.offset(10), dom.flip({
      fallbackPlacements: props.fallbackPlacements || ["bottom"]
    }), dom.shift()]
  });
  const [uncontrolledOpen, setUncontrolledOpen] = solidJs.createSignal((_a = props.open) != null ? _a : false);
  const openAccessor = solidJs.createMemo(() => {
    return props.open !== void 0 ? !!props.open : uncontrolledOpen();
  });
  const setOpenSetter = (valueOrFn) => {
    if (props.open === void 0) {
      setUncontrolledOpen(valueOrFn);
    }
  };
  const [shouldRenderTooltip, setShouldRenderTooltip] = solidJs.createSignal(openAccessor());
  let renderTimeoutId;
  solidJs.createEffect(() => {
    const isOpen = openAccessor();
    if (renderTimeoutId) {
      clearTimeout(renderTimeoutId);
      renderTimeoutId = void 0;
    }
    if (isOpen) {
      setShouldRenderTooltip(true);
    } else if (effectiveAnimationDuration() > 0) {
      renderTimeoutId = window.setTimeout(() => {
        setShouldRenderTooltip(false);
      }, effectiveAnimationDuration() * 1e3);
    } else {
      setShouldRenderTooltip(false);
    }
  });
  solidJs.createEffect(() => {
    if (openAccessor()) {
      setShouldRenderTooltip(true);
    }
  });
  return web.createComponent(TooltipContext.Provider, {
    value: {
      reference,
      setReference,
      floating,
      setFloating,
      open: openAccessor,
      shouldRender: shouldRenderTooltip,
      setOpen: setOpenSetter,
      floatingStyles: () => {
        var _a2, _b;
        return {
          position: position.strategy,
          top: `${(_a2 = position.y) != null ? _a2 : 0}px`,
          left: `${(_b = position.x) != null ? _b : 0}px`
        };
      },
      effectiveAnimationDuration
    },
    get children() {
      return props.children;
    }
  });
}
function useTooltip() {
  const context = solidJs.useContext(TooltipContext);
  if (!context) {
    throw new Error("useTooltip must be used within Tooltip.Root component");
  }
  return context;
}
var Motion = new Proxy(solidMotionone.Motion, {
  get: (_, tag) => (props) => {
    const {
      animations
    } = useAppearance();
    return web.createComponent(solidMotionone.Motion, web.mergeProps(props, {
      tag,
      get transition() {
        return animations() ? props.transition : {
          duration: 0
        };
      }
    }));
  }
});

// src/ui/components/primitives/Tooltip/TooltipContent.tsx
var tooltipContentVariants = () => "nt-bg-foreground nt-p-2 nt-shadow-tooltip nt-rounded-lg nt-text-background nt-text-xs";
var TooltipContentBody = (props) => {
  const {
    open,
    setFloating,
    floating,
    floatingStyles,
    effectiveAnimationDuration
  } = useTooltip();
  const {
    setActive,
    removeActive
  } = useFocusManager();
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey", "style"]);
  const style = useStyle();
  solidJs.onMount(() => {
    const floatingEl = floating();
    if (floatingEl) setActive(floatingEl);
    solidJs.onCleanup(() => {
      if (floatingEl) removeActive(floatingEl);
    });
  });
  return web.createComponent(Motion.div, web.mergeProps({
    initial: {
      opacity: 0,
      scale: 0.95
    },
    get animate() {
      return open() ? {
        opacity: 1,
        scale: 1
      } : {
        opacity: 0,
        scale: 0.95
      };
    },
    get transition() {
      return {
        duration: effectiveAnimationDuration(),
        easing: "ease-in-out"
      };
    },
    ref: setFloating,
    get ["class"]() {
      return web.memo(() => !!local.class)() ? local.class : style(local.appearanceKey || "tooltipContent", tooltipContentVariants());
    },
    get style() {
      return chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, floatingStyles()), {
        "z-index": 99999
      });
    }
  }, rest, {
    get children() {
      return props.children;
    }
  }));
};
var TooltipContent = (props) => {
  const {
    shouldRender
  } = useTooltip();
  const {
    container
  } = useAppearance();
  const portalContainer = () => {
    var _a;
    return (_a = container()) != null ? _a : document.body;
  };
  return web.createComponent(solidJs.Show, {
    get when() {
      return shouldRender();
    },
    get children() {
      return web.createComponent(web.Portal, {
        get mount() {
          return portalContainer();
        },
        get children() {
          return web.createComponent(Root, {
            get children() {
              return web.createComponent(TooltipContentBody, props);
            }
          });
        }
      });
    }
  });
};

// src/ui/helpers/mergeRefs.ts
function chain(callbacks) {
  return (...args) => {
    for (const callback of callbacks) callback && callback(...args);
  };
}
function mergeRefs(...refs) {
  return chain(refs);
}

// src/ui/components/primitives/Tooltip/TooltipTrigger.tsx
var _tmpl$26 = /* @__PURE__ */ web.template(`<button>`);
var TooltipTrigger = (props) => {
  const {
    setReference,
    setOpen
  } = useTooltip();
  const style = useStyle();
  const [local, rest] = solidJs.splitProps(props, ["appearanceKey", "asChild", "onClick", "onMouseEnter", "onMouseLeave", "ref"]);
  const handleMouseEnter = (e) => {
    if (typeof local.onMouseEnter === "function") {
      local.onMouseEnter(e);
    }
    setOpen(true);
  };
  const ref = solidJs.createMemo(() => local.ref ? mergeRefs(setReference, local.ref) : setReference);
  const handleMouseLeave = (e) => {
    if (typeof local.onMouseLeave === "function") {
      local.onMouseLeave(e);
    }
    setOpen(false);
  };
  if (local.asChild) {
    return web.createComponent(web.Dynamic, web.mergeProps({
      get component() {
        return local.asChild;
      },
      ref(r$) {
        var _ref$ = ref();
        typeof _ref$ === "function" && _ref$(r$);
      },
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave
    }, rest));
  }
  return (() => {
    var _el$ = _tmpl$26();
    _el$.addEventListener("mouseleave", () => {
      setOpen(false);
    });
    _el$.addEventListener("mouseenter", () => {
      setOpen(true);
    });
    var _ref$2 = ref();
    typeof _ref$2 === "function" && web.use(_ref$2, _el$);
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "tooltipTrigger");
      }
    }, rest), false, true);
    web.insert(_el$, () => props.children);
    return _el$;
  })();
};

// src/ui/components/primitives/Tooltip/index.ts
var Tooltip = {
  Root: TooltipRoot,
  /**
   * Tooltip.Trigger renders a `button` and has no default styling.
   */
  Trigger: TooltipTrigger,
  /**
   * Tooltip.Content renders a `div` and has popover specific styling.
   */
  Content: TooltipContent
};

// src/ui/components/primitives/CopyToClipboard.tsx
var _tmpl$27 = /* @__PURE__ */ web.template(`<button type=button>`);
function CopyToClipboard(props) {
  const [isCopied, setIsCopied] = solidJs.createSignal(false);
  const style = useStyle();
  let timeoutId;
  const defaultTooltipText = "Copied!";
  const defaultTooltipDuration = 2e3;
  function handleCopy() {
    return chunk7B52C2XE_js.__async(this, null, function* () {
      var _a;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      try {
        yield navigator.clipboard.writeText(props.textToCopy);
        setIsCopied(true);
        timeoutId = window.setTimeout(() => {
          setIsCopied(false);
          timeoutId = void 0;
        }, (_a = props.tooltipDuration) != null ? _a : defaultTooltipDuration);
      } catch (err) {
        console.error("Failed to copy text: ", err);
      }
    });
  }
  return web.createComponent(Tooltip.Root, {
    get open() {
      return isCopied();
    },
    placement: "top",
    animationDuration: 0.15,
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (triggerProps) => (() => {
          var _el$ = _tmpl$27();
          web.spread(_el$, web.mergeProps(triggerProps, {
            "onClick": handleCopy,
            get ["class"]() {
              return style("button", "nt-cursor-pointer");
            }
          }), false, true);
          web.insert(_el$, () => props.children);
          return _el$;
        })()
      }), web.createComponent(Tooltip.Content, {
        get children() {
          var _a;
          return (_a = props.tooltipText) != null ? _a : defaultTooltipText;
        }
      })];
    }
  });
}

// src/ui/components/elements/Footer.tsx
var _tmpl$28 = /* @__PURE__ */ web.template(`<span class="nt-z-10 nt-text-xs nt-text-stripes">`);
var _tmpl$29 = /* @__PURE__ */ web.template(`<span class="nt-z-10 nt-text-xs">\u2022`);
var _tmpl$32 = /* @__PURE__ */ web.template(`<a target=_blank class="nt-z-10 nt-flex nt-items-center nt-gap-1 nt-justify-center"><span class=nt-text-xs>Inbox by</span><span class=nt-text-xs>Novu`);
var _tmpl$42 = /* @__PURE__ */ web.template(`<span class=nt-underline>Copy cURL`);
var _tmpl$52 = /* @__PURE__ */ web.template(`<div class="nt-z-10 nt-flex nt-items-center nt-gap-1 nt-text-xs nt-text-secondary-foreground"><a href=https://go.novu.co/keyless class="nt-underline nt-flex nt-items-center nt-gap-0.5"target=_blank rel="noopener noreferrer">Get API key</a><span>\u2022</span><span>\u2022</span><button type=button class=nt-underline>Send notification`);
var _tmpl$62 = /* @__PURE__ */ web.template(`<div><div class="nt-flex nt-items-center nt-gap-1">`);
var _tmpl$72 = /* @__PURE__ */ web.template(`<a href="https://go.novu.co/keyless?utm_campaign=keyless-mode"target=_blank rel="noopener noreferrer">Keyless mode`);
var _tmpl$82 = /* @__PURE__ */ web.template(`<br>`);
var stripes = `before:nt-content-[""] before:nt-absolute before:nt-inset-0 before:-nt-right-[calc(0+var(--stripes-size))] before:[mask-image:linear-gradient(transparent_0%,black)] before:nt-bg-dev-stripes-gradient before:nt-bg-[length:var(--stripes-size)_var(--stripes-size)] before:nt-animate-stripes before:hover:[animation-play-state:running]`;
var commonAfter = 'after:nt-content-[""] after:nt-absolute after:nt-inset-0 after:-nt-top-12';
var devModeGradient = `${commonAfter} after:nt-bg-[linear-gradient(180deg,transparent,oklch(from_var(--nv-color-stripes)_l_c_h_/_0.07)_55%,transparent),linear-gradient(180deg,transparent,oklch(from_var(--nv-color-background)_l_c_h_/_0.9)_55%,transparent)]`;
var prodModeGradient = `${commonAfter} after:nt-bg-[linear-gradient(180deg,transparent,oklch(from_var(--nv-color-background)_l_c_h_/_0.9)_55%,transparent)]`;
var Footer = () => {
  const {
    hideBranding,
    isDevelopmentMode,
    isKeyless
  } = useInboxContext();
  const novu = useNovu();
  function handleTriggerHelloWorld() {
    return chunk7B52C2XE_js.__async(this, null, function* () {
      try {
        yield novu.notifications.triggerHelloWorldEvent();
      } catch (error) {
        console.error("Failed to send Hello World from UI via novu.notifications:", error);
      }
    });
  }
  return web.createComponent(solidJs.Show, {
    get when() {
      return !hideBranding() || isDevelopmentMode();
    },
    get children() {
      var _el$ = _tmpl$62(), _el$2 = _el$.firstChild;
      _el$.style.setProperty("--stripes-size", "15px");
      web.insert(_el$2, web.createComponent(solidJs.Show, {
        get when() {
          return isDevelopmentMode();
        },
        get children() {
          var _el$3 = _tmpl$28();
          web.insert(_el$3, (() => {
            var _c$ = web.memo(() => !!isKeyless());
            return () => _c$() ? web.createComponent(Tooltip.Root, {
              get children() {
                return [web.createComponent(Tooltip.Trigger, {
                  "class": "",
                  get children() {
                    return _tmpl$72();
                  }
                }), web.createComponent(Tooltip.Content, {
                  get children() {
                    return ["Temporary <Inbox />. All data will expire in 24 hours.", _tmpl$82(), "Connect API key to persist."];
                  }
                })];
              }
            }) : "Development mode";
          })());
          return _el$3;
        }
      }), null);
      web.insert(_el$2, web.createComponent(solidJs.Show, {
        get when() {
          return web.memo(() => !!isDevelopmentMode())() && !hideBranding();
        },
        get children() {
          return _tmpl$29();
        }
      }), null);
      web.insert(_el$2, web.createComponent(solidJs.Show, {
        get when() {
          return !hideBranding();
        },
        get children() {
          var _el$5 = _tmpl$32(), _el$6 = _el$5.firstChild, _el$7 = _el$6.nextSibling;
          web.insert(_el$5, web.createComponent(Novu2, {
            "class": "nt-size-2.5"
          }), _el$7);
          web.effect(() => web.setAttribute(_el$5, "href", `https://go.novu.co/powered?ref=${getCurrentDomain()}`));
          return _el$5;
        }
      }), null);
      web.insert(_el$, web.createComponent(solidJs.Show, {
        get when() {
          return isKeyless();
        },
        get children() {
          var _el$8 = _tmpl$52(), _el$9 = _el$8.firstChild; _el$9.firstChild; var _el$11 = _el$9.nextSibling, _el$13 = _el$11.nextSibling, _el$14 = _el$13.nextSibling;
          web.insert(_el$9, web.createComponent(ArrowUpRight, {
            "class": "nt-ml-1"
          }), null);
          web.insert(_el$8, web.createComponent(CopyToClipboard, {
            get textToCopy() {
              return getCurlCommand();
            },
            get children() {
              return _tmpl$42();
            }
          }), _el$13);
          _el$14.$$click = (e) => {
            e.preventDefault();
            handleTriggerHelloWorld();
          };
          return _el$8;
        }
      }), null);
      web.effect(() => web.className(_el$, cn(`nt-relative nt-flex nt-shrink-0 nt-flex-col nt-justify-center nt-items-center nt-gap-1 nt-mt-auto nt-py-3 nt-text-foreground-alpha-400`, {
        [stripes]: isDevelopmentMode(),
        [devModeGradient]: isDevelopmentMode(),
        "nt-bg-[oklch(from_var(--nv-color-stripes)_l_c_h_/_0.1)]": isDevelopmentMode(),
        [prodModeGradient]: !isDevelopmentMode()
      })));
      return _el$;
    }
  });
};
function getCurrentDomain() {
  if (isBrowser()) {
    return window.location.hostname;
  }
  return "";
}
function getCurlCommand() {
  const identifier = window.localStorage.getItem("novu_keyless_application_identifier");
  if (!identifier) {
    console.error("Novu application identifier not found for cURL command.");
    return "";
  }
  const DEFAULT_BACKEND_URL = typeof window !== "undefined" && window.NOVU_LOCAL_BACKEND_URL || "https://api.novu.co";
  return `curl -X POST   ${DEFAULT_BACKEND_URL}/${chunkRN7LHLHM_js.DEFAULT_API_VERSION}/events/trigger   -H 'Authorization: Keyless ${identifier}'   -H 'Content-Type: application/json'   -d '{
    "name": "hello-world",
    "to": {
      "subscriberId": "keyless-subscriber-id"
    },
    "payload": {
      "body": "New From Keyless Environment",
      "subject": "Hello World!"
    }
  }'`;
}
web.delegateEvents(["click"]);
var _tmpl$30 = /* @__PURE__ */ web.template(`<button>`);
var buttonVariants = classVarianceAuthority.cva(cn('nt-inline-flex nt-gap-4 nt-items-center nt-justify-center nt-whitespace-nowrap nt-text-sm nt-font-medium nt-transition-colors disabled:nt-pointer-events-none disabled:nt-opacity-50 after:nt-absolute after:nt-content-[""] before:nt-content-[""] before:nt-absolute [&_svg]:nt-pointer-events-none [&_svg]:nt-shrink-0', `focus-visible:nt-outline-none focus-visible:nt-ring-2 focus-visible:nt-rounded-md focus-visible:nt-ring-ring focus-visible:nt-ring-offset-2`), {
  variants: {
    variant: {
      default: "nt-bg-gradient-to-b nt-from-20% nt-from-primary-foreground-alpha-200 nt-to-transparent nt-bg-primary nt-text-primary-foreground nt-shadow-[0_0_0_0.5px_var(--nv-color-primary-600)] nt-relative before:nt-absolute before:nt-inset-0 before:nt-border before:nt-border-primary-foreground-alpha-100 after:nt-absolute after:nt-inset-0 after:nt-opacity-0 hover:after:nt-opacity-100 after:nt-transition-opacity after:nt-bg-gradient-to-b after:nt-from-primary-foreground-alpha-50 after:nt-to-transparent",
      secondary: "nt-bg-secondary nt-text-secondary-foreground nt-shadow-[0_0_0_0.5px_var(--nv-color-secondary-600)] nt-relative before:nt-absolute before:nt-inset-0 before:nt-border before:nt-border-secondary-foreground-alpha-100 after:nt-absolute after:nt-inset-0 after:nt-opacity-0 hover:after:nt-opacity-100 after:nt-transition-opacity after:nt-bg-gradient-to-b after:nt-from-secondary-foreground-alpha-50 after:nt-to-transparent",
      ghost: "hover:nt-bg-neutral-alpha-100 nt-text-foreground-alpha-600 hover:nt-text-foreground-alpha-800",
      unstyled: ""
    },
    size: {
      none: "",
      iconSm: "nt-p-1 nt-rounded-md after:nt-rounded-md before:nt-rounded-md focus-visible:nt-rounded-md",
      icon: "nt-p-2.5 nt-rounded-xl before:nt-rounded-xl after:nt-rounded-xl focus-visible:nt-rounded-xl",
      default: "nt-px-2 nt-py-1 nt-rounded-lg focus-visible:nt-rounded-lg before:nt-rounded-lg after:nt-rounded-lg",
      sm: "nt-px-1 nt-py-px nt-rounded-md nt-text-xs nt-px-1 before:nt-rounded-md focus-visible:nt-rounded-md after:nt-rounded-md",
      lg: "nt-px-8 nt-py-2 nt-text-base before:nt-rounded-lg after:nt-rounded-lg focus-visible:nt-rounded-lg"
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default"
  }
});
var Button = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey"]);
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$30();
    web.spread(_el$, web.mergeProps({
      get ["data-variant"]() {
        return props.variant;
      },
      get ["data-size"]() {
        return props.size;
      },
      get ["class"]() {
        return style(local.appearanceKey || "button", cn(buttonVariants({
          variant: props.variant,
          size: props.size
        }), local.class));
      }
    }, rest), false, false);
    return _el$;
  })();
};
var _tmpl$31 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$210 = /* @__PURE__ */ web.template(`<div><span>`);
var DatePickerContext = solidJs.createContext({
  currentDate: () => /* @__PURE__ */ new Date(),
  setCurrentDate: () => {
  },
  viewMonth: () => /* @__PURE__ */ new Date(),
  setViewMonth: () => {
  },
  selectedDate: () => null,
  setSelectedDate: () => {
  },
  maxDays: () => 0
});
var useDatePicker = () => solidJs.useContext(DatePickerContext);
var DatePicker = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["children", "value", "onDateChange", "class", "maxDays"]);
  const style = useStyle();
  const today = /* @__PURE__ */ new Date();
  today.setHours(0, 0, 0, 0);
  const [currentDate, setCurrentDate] = solidJs.createSignal(today);
  const [viewMonth, setViewMonth] = solidJs.createSignal(today);
  const [selectedDate, setSelectedDate] = solidJs.createSignal(local.value ? new Date(local.value) : null);
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    if (local.onDateChange) {
      local.onDateChange(date);
    }
  };
  return web.createComponent(DatePickerContext.Provider, {
    value: {
      currentDate,
      setCurrentDate,
      viewMonth,
      setViewMonth,
      selectedDate,
      setSelectedDate: handleDateSelect,
      maxDays: () => props.maxDays
    },
    get children() {
      var _el$ = _tmpl$31();
      web.spread(_el$, web.mergeProps({
        get ["class"]() {
          return style("datePicker", cn("nt-p-2", local.class));
        }
      }, rest), false, true);
      web.insert(_el$, () => local.children);
      return _el$;
    }
  });
};
var DatePickerHeader = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey", "children"]);
  const style = useStyle();
  const {
    viewMonth,
    setViewMonth,
    currentDate,
    maxDays
  } = useDatePicker();
  useAppearance();
  const prevIconClass = style("datePickerControlPrevTrigger__icon", "nt-size-4 nt-text-foreground-alpha-700", {
    iconKey: "arrowLeft"
  });
  const nextIconClass = style("datePickerControlNextTrigger__icon", "nt-size-4 nt-text-foreground-alpha-700", {
    iconKey: "arrowRight"
  });
  const handlePrevMonth = () => {
    const date = new Date(viewMonth());
    date.setMonth(date.getMonth() - 1);
    const currentMonth = currentDate();
    if (date.getFullYear() < currentMonth.getFullYear() || date.getFullYear() === currentMonth.getFullYear() && date.getMonth() < currentMonth.getMonth()) {
      return;
    }
    setViewMonth(date);
  };
  const handleNextMonth = () => {
    const date = new Date(viewMonth());
    date.setMonth(date.getMonth() + 1);
    const maxDaysValue = maxDays();
    if (maxDaysValue > 0) {
      const maxDate = new Date(currentDate());
      maxDate.setDate(maxDate.getDate() + maxDaysValue);
      if (date.getFullYear() > maxDate.getFullYear() || date.getFullYear() === maxDate.getFullYear() && date.getMonth() > maxDate.getMonth()) {
        return;
      }
    }
    setViewMonth(date);
  };
  const isPrevDisabled = () => {
    const current = currentDate();
    const view = viewMonth();
    return view.getFullYear() === current.getFullYear() && view.getMonth() === current.getMonth();
  };
  const isNextDisabled = () => {
    const maxDaysValue = maxDays();
    if (maxDaysValue === 0) return false;
    const view = viewMonth();
    const maxDate = new Date(currentDate());
    maxDate.setDate(maxDate.getDate() + maxDaysValue);
    return view.getFullYear() === maxDate.getFullYear() && view.getMonth() === maxDate.getMonth();
  };
  return (() => {
    var _el$2 = _tmpl$210(), _el$3 = _el$2.firstChild;
    web.spread(_el$2, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "datePickerControl", cn("nt-flex nt-items-center nt-justify-between nt-gap-1.5 nt-h-7 nt-p-1 nt-mb-2 nt-rounded-lg nt-bg-background", local.class));
      }
    }, rest), false, true);
    web.insert(_el$2, web.createComponent(Button, {
      appearanceKey: "datePickerControlPrevTrigger",
      variant: "ghost",
      onClick: (e) => {
        e.stopPropagation();
        handlePrevMonth();
      },
      get disabled() {
        return isPrevDisabled();
      },
      "class": "nt-flex nt-justify-center nt-items-center nt-gap-0.5 nt-w-5 nt-h-5 nt-p-0 nt-rounded-md nt-bg-background nt-shadow-[0px_1px_2px_0px_rgba(10,13,20,0.03)]",
      get children() {
        return web.createComponent(IconRendererWrapper, {
          iconKey: "arrowLeft",
          "class": prevIconClass,
          get fallback() {
            return web.createComponent(ArrowLeft, {
              "class": prevIconClass
            });
          }
        });
      }
    }), _el$3);
    web.insert(_el$3, () => viewMonth().toLocaleDateString("en-US", {
      month: "long",
      year: "numeric"
    }));
    web.insert(_el$2, web.createComponent(Button, {
      appearanceKey: "datePickerControlNextTrigger",
      variant: "ghost",
      onClick: (e) => {
        e.stopPropagation();
        handleNextMonth();
      },
      get disabled() {
        return isNextDisabled();
      },
      "class": "nt-flex nt-justify-center nt-items-center nt-gap-0.5 nt-w-5 nt-h-5 nt-p-0 nt-rounded-md nt-bg-background nt-shadow-[0px_1px_2px_0px_rgba(10,13,20,0.03)]",
      get children() {
        return web.createComponent(IconRendererWrapper, {
          iconKey: "arrowRight",
          "class": nextIconClass,
          get fallback() {
            return web.createComponent(ArrowRight, {
              "class": nextIconClass
            });
          }
        });
      }
    }), null);
    web.effect(() => web.className(_el$3, style("datePickerHeaderMonth", "nt-text-sm nt-font-medium nt-text-foreground-alpha-700")));
    return _el$2;
  })();
};
var DatePickerGridCellTrigger = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey", "date"]);
  const {
    selectedDate,
    viewMonth,
    setSelectedDate,
    currentDate,
    maxDays
  } = useDatePicker();
  const {
    t
  } = useLocalization();
  const isCurrentMonth = props.date.getMonth() === viewMonth().getMonth();
  const isPastDate = () => {
    const today = currentDate();
    return props.date < today;
  };
  const isFutureDate = () => {
    const maxDaysValue = maxDays();
    if (maxDaysValue === 0) return false;
    const maxDate = new Date(currentDate());
    maxDate.setDate(maxDate.getDate() + maxDaysValue);
    return props.date > maxDate;
  };
  const isDisabled = !isCurrentMonth || isPastDate() || isFutureDate();
  const isExceedingLimit = () => {
    return isCurrentMonth && isFutureDate();
  };
  const buttonElement = web.createComponent(Button, web.mergeProps({
    appearanceKey: "datePickerCalendarDay__button",
    variant: "ghost",
    disabled: isDisabled,
    onClick: (e) => {
      e.stopPropagation();
      setSelectedDate(local.date);
    },
    get ["class"]() {
      var _a;
      return cn("nt-size-8 nt-w-full nt-rounded-md nt-flex nt-items-center nt-justify-center", {
        "nt-text-muted-foreground disabled:nt-opacity-20": !isCurrentMonth || isPastDate(),
        "nt-text-foreground-alpha-700": isCurrentMonth && !isPastDate() && !isFutureDate()
      }, {
        "nt-bg-primary-alpha-300 hover:nt-bg-primary-alpha-400": ((_a = selectedDate()) == null ? void 0 : _a.toDateString()) === local.date.toDateString()
      });
    }
  }, rest, {
    get children() {
      return local.date.getDate();
    }
  }));
  if (isExceedingLimit()) {
    return web.createComponent(Tooltip.Root, {
      get children() {
        return [web.createComponent(Tooltip.Trigger, {
          children: buttonElement
        }), web.createComponent(Tooltip.Content, {
          get children() {
            return t("snooze.datePicker.exceedingLimitTooltip", {
              days: maxDays()
            });
          }
        })];
      }
    });
  }
  return buttonElement;
};
var DatePickerCalendar = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey"]);
  const style = useStyle();
  const {
    viewMonth
  } = useDatePicker();
  const getDaysInMonth = () => {
    const year = viewMonth().getFullYear();
    const month = viewMonth().getMonth();
    const firstDay = new Date(year, month, 1);
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const startingDay = firstDay.getDay();
    const days = [];
    for (let i = 0; i < startingDay; i += 1) {
      const prevMonthDay = new Date(year, month, -i);
      days.unshift(prevMonthDay);
    }
    for (let i = 1; i <= daysInMonth; i += 1) {
      days.push(new Date(year, month, i));
    }
    const remainingCells = 7 - days.length % 7;
    if (remainingCells < 7) {
      for (let i = 1; i <= remainingCells; i += 1) {
        days.push(new Date(year, month + 1, i));
      }
    }
    return days;
  };
  return (() => {
    var _el$8 = _tmpl$31();
    _el$8.$$click = (e) => e.stopPropagation();
    web.spread(_el$8, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "datePickerCalendar", cn("nt-grid nt-grid-cols-7 nt-gap-1", local.class));
      }
    }, rest), false, true);
    web.insert(_el$8, () => getDaysInMonth().map((date) => {
      return web.createComponent(DatePickerGridCellTrigger, {
        date
      });
    }));
    return _el$8;
  })();
};
web.delegateEvents(["click"]);
var _tmpl$33 = /* @__PURE__ */ web.template(`<button>`);
var PopoverClose = (props) => {
  const {
    onClose
  } = usePopover();
  const style = useStyle();
  const [local, rest] = solidJs.splitProps(props, ["onClick", "asChild", "appearanceKey", "class"]);
  const handleClick = (e) => {
    if (typeof local.onClick === "function") {
      local.onClick(e);
    }
    onClose();
  };
  if (local.asChild) {
    return web.createComponent(web.Dynamic, web.mergeProps({
      get component() {
        return local.asChild;
      },
      onClick: handleClick
    }, rest));
  }
  return (() => {
    var _el$ = _tmpl$33();
    _el$.$$click = handleClick;
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "popoverClose", local.class);
      }
    }, rest), false, false);
    return _el$;
  })();
};
web.delegateEvents(["click"]);
var _tmpl$34 = /* @__PURE__ */ web.template(`<div>`);
var Portal2 = (props) => {
  const appearance = useAppearance();
  let currentElement;
  return [(() => {
    var _el$ = _tmpl$34();
    web.use((el) => {
      currentElement = el;
    }, _el$);
    _el$.style.setProperty("display", "none");
    return _el$;
  })(), web.createComponent(web.Portal, web.mergeProps({
    get mount() {
      return closestNovuRootParent(currentElement, appearance.id());
    }
  }, props))];
};
var closestNovuRootParent = (el, id) => {
  let element = el;
  while (element && element.id !== `novu-root-${id}`) {
    element = element.parentElement;
  }
  if (element && element.id === `novu-root-${id}`) {
    return element;
  }
  return void 0;
};
var PopoverContext = solidJs.createContext(void 0);
function PopoverRoot(props) {
  var _a;
  const [uncontrolledIsOpen, setUncontrolledIsOpen] = solidJs.createSignal((_a = props.open) != null ? _a : false);
  const onOpenChange = () => {
    var _a2;
    return (_a2 = props.onOpenChange) != null ? _a2 : setUncontrolledIsOpen;
  };
  const open = () => {
    var _a2;
    return (_a2 = props.open) != null ? _a2 : uncontrolledIsOpen();
  };
  const [reference, setReference] = solidJs.createSignal(null);
  const [floating, setFloating] = solidJs.createSignal(null);
  const position = solidFloatingUi.useFloating(reference, floating, {
    strategy: "absolute",
    placement: props.placement,
    whileElementsMounted: dom.autoUpdate,
    middleware: [dom.offset(10), dom.flip({
      fallbackPlacements: props.fallbackPlacements
    }), dom.shift()]
  });
  const floatingStyles = solidJs.createMemo(() => {
    var _a2, _b;
    return {
      position: position.strategy,
      top: `${(_a2 = position.y) != null ? _a2 : 0}px`,
      left: `${(_b = position.x) != null ? _b : 0}px`
    };
  });
  const onClose = () => {
    onOpenChange()(false);
  };
  const onToggle = () => {
    onOpenChange()((prev) => !prev);
  };
  return web.createComponent(PopoverContext.Provider, {
    value: {
      onToggle,
      onClose,
      reference,
      setReference,
      floating,
      setFloating,
      open,
      floatingStyles
    },
    get children() {
      return props.children;
    }
  });
}
function usePopover() {
  const context = solidJs.useContext(PopoverContext);
  if (!context) {
    throw new Error("usePopover must be used within Popover.Root component");
  }
  return context;
}

// src/ui/components/primitives/Popover/PopoverContent.tsx
var _tmpl$35 = /* @__PURE__ */ web.template(`<div>`);
var popoverContentVariants = () => cn("nt-w-[400px] nt-h-[600px] nt-rounded-xl nt-bg-background", "nt-shadow-popover nt-animate-in nt-slide-in-from-top-2 nt-fade-in nt-cursor-default nt-flex nt-flex-col nt-overflow-hidden nt-border nt-border-border nt-z-10");
var PopoverContentBody = (props) => {
  const {
    open,
    setFloating,
    floating,
    floatingStyles
  } = usePopover();
  const {
    setActive,
    removeActive
  } = useFocusManager();
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey", "style"]);
  const style = useStyle();
  solidJs.onMount(() => {
    const floatingEl = floating();
    setActive(floatingEl);
    solidJs.onCleanup(() => {
      removeActive(floatingEl);
    });
  });
  return (() => {
    var _el$ = _tmpl$35();
    web.use(setFloating, _el$);
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "popoverContent", cn(popoverContentVariants(), local.class));
      },
      get style() {
        return floatingStyles();
      },
      get ["data-open"]() {
        return open();
      }
    }, rest), false, false);
    return _el$;
  })();
};
var PopoverContent = (props) => {
  const {
    open,
    onClose,
    reference,
    floating
  } = usePopover();
  const {
    active
  } = useFocusManager();
  const {
    container
  } = useAppearance();
  const handleClickOutside = (e) => {
    var _a, _b;
    if ((_a = reference()) == null ? void 0 : _a.contains(e.target)) {
      return;
    }
    const containerElement = container();
    if (active() !== floating() || ((_b = floating()) == null ? void 0 : _b.contains(e.target)) || containerElement && e.target.shadowRoot === containerElement) {
      return;
    }
    onClose();
  };
  const handleEscapeKey = (e) => {
    if (active() !== floating()) {
      return;
    }
    if (e instanceof KeyboardEvent && e.key === "Escape") {
      onClose();
    }
  };
  solidJs.onMount(() => {
    var _a;
    document.body.addEventListener("click", handleClickOutside);
    (_a = container()) == null ? void 0 : _a.addEventListener("click", handleClickOutside);
    document.body.addEventListener("keydown", handleEscapeKey);
  });
  solidJs.onCleanup(() => {
    var _a;
    document.body.removeEventListener("click", handleClickOutside);
    (_a = container()) == null ? void 0 : _a.removeEventListener("click", handleClickOutside);
    document.body.removeEventListener("keydown", handleEscapeKey);
  });
  return web.createComponent(solidJs.Show, {
    get when() {
      return open();
    },
    get children() {
      return web.createComponent(Portal2, {
        get children() {
          return web.createComponent(PopoverContentBody, props);
        }
      });
    }
  });
};
var _tmpl$36 = /* @__PURE__ */ web.template(`<button>`);
var PopoverTrigger = (props) => {
  const {
    setReference,
    onToggle
  } = usePopover();
  const style = useStyle();
  const [local, rest] = solidJs.splitProps(props, ["appearanceKey", "asChild", "onClick", "ref"]);
  const handleClick = (e) => {
    if (typeof local.onClick === "function") {
      local.onClick(e);
    }
    onToggle();
  };
  const ref = solidJs.createMemo(() => local.ref ? mergeRefs(setReference, local.ref) : setReference);
  if (local.asChild) {
    return web.createComponent(web.Dynamic, web.mergeProps({
      get component() {
        return local.asChild;
      },
      ref(r$) {
        var _ref$ = ref();
        typeof _ref$ === "function" && _ref$(r$);
      },
      onClick: handleClick
    }, rest));
  }
  return (() => {
    var _el$ = _tmpl$36();
    _el$.$$click = handleClick;
    var _ref$2 = ref();
    typeof _ref$2 === "function" && web.use(_ref$2, _el$);
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "dropdownTrigger");
      }
    }, rest), false, true);
    web.insert(_el$, () => props.children);
    return _el$;
  })();
};
web.delegateEvents(["click"]);

// src/ui/components/primitives/Popover/index.ts
var Popover = {
  Root: PopoverRoot,
  /**
   * Popover.Trigger renders a `button` and has no default styling.
   */
  Trigger: PopoverTrigger,
  /**
   * Popover.Content renders a `div` and has popover specific styling.
   */
  Content: PopoverContent,
  /**
   * Popover.Close renders a `button` and has no styling.
   * Closes the popover when clicked.
   * `onClick` function is propagated.
   */
  Close: PopoverClose
};
var dropdownContentVariants = () => "nt-p-1 nt-text-sm nt-min-w-52 nt-shadow-dropdown nt-h-fit nt-min-w-52 nt-w-max";
var DropdownContent = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["appearanceKey", "class"]);
  return web.createComponent(Popover.Content, web.mergeProps({
    get appearanceKey() {
      return local.appearanceKey || "dropdownContent";
    },
    get ["class"]() {
      return cn(dropdownContentVariants(), local.class);
    }
  }, rest));
};
var dropdownItemVariants = () => "focus:nt-outline-none nt-flex nt-items-center nt-gap-1.5 nt-text-sm nt-rounded-lg nt-items-center hover:nt-bg-neutral-alpha-50 focus-visible:nt-bg-neutral-alpha-50 nt-py-1 nt-px-2";
var DropdownItem = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["appearanceKey", "onClick", "class", "asChild"]);
  const {
    onClose
  } = usePopover();
  const handleClick = (e) => {
    if (typeof local.onClick === "function") {
      local.onClick(e);
    }
    onClose();
  };
  if (local.asChild) {
    return web.createComponent(web.Dynamic, web.mergeProps({
      get component() {
        return local.asChild;
      },
      onClick: handleClick
    }, rest));
  }
  return web.createComponent(Popover.Close, web.mergeProps({
    get appearanceKey() {
      return local.appearanceKey || "dropdownItem";
    },
    get ["class"]() {
      return cn(dropdownItemVariants(), local.class);
    },
    onClick: (e) => {
      if (typeof local.onClick === "function") {
        local.onClick(e);
      }
      onClose();
    }
  }, rest));
};
var DropdownRoot = (props) => {
  return web.createComponent(Popover.Root, web.mergeProps({
    placement: "bottom",
    fallbackPlacements: ["top"]
  }, props));
};
var dropdownTriggerButtonVariants = () => `nt-relative nt-transition nt-outline-none focus-visible:nt-outline-nonefocus-visible:nt-ring-2 focus-visible:nt-ring-primary focus-visible:nt-ring-offset-2`;
var DropdownTrigger = (props) => {
  const style = useStyle();
  const [local, rest] = solidJs.splitProps(props, ["appearanceKey", "class"]);
  return web.createComponent(Popover.Trigger, web.mergeProps({
    get ["class"]() {
      return style(local.appearanceKey || "dropdownTrigger", cn(dropdownTriggerButtonVariants(), local.class));
    }
  }, rest));
};

// src/ui/components/primitives/Dropdown/index.ts
var Dropdown = {
  Root: DropdownRoot,
  /**
   * Dropdown.Trigger renders a `button` and has no default styling.
   */
  Trigger: DropdownTrigger,
  /**
   * Dropdown.Content renders a `Popover.Content` by default.
   */
  Content: DropdownContent,
  /**
   * Dropdown.Close renders a `Popover.Close` by default.
   */
  Close: Popover.Close,
  /**
   * Dropdown.Item renders a `Popover.Close` with dropdown specific styling.
   * Closes the popover when clicked.
   * `onClick` function is propagated.
   */
  Item: DropdownItem
};
var useKeyboardNavigation = ({
  activeTab,
  setActiveTab,
  tabsContainer
}) => {
  const { container } = useAppearance();
  const [keyboardNavigation, setKeyboardNavigation] = solidJs.createSignal(false);
  const getRoot = () => {
    const containerElement = container();
    return containerElement instanceof ShadowRoot ? containerElement : document;
  };
  solidJs.createEffect(() => {
    const root = getRoot();
    const handleTabKey = (event) => {
      var _a;
      if (!(event instanceof KeyboardEvent) || event.key !== "Tab") {
        return;
      }
      const tabs = (_a = tabsContainer()) == null ? void 0 : _a.querySelectorAll('[role="tab"]');
      if (!tabs || !root.activeElement) {
        return;
      }
      setKeyboardNavigation(Array.from(tabs).includes(root.activeElement));
    };
    root.addEventListener("keyup", handleTabKey);
    return solidJs.onCleanup(() => root.removeEventListener("keyup", handleTabKey));
  });
  solidJs.createEffect(() => {
    const root = getRoot();
    const handleArrowKeys = (event) => {
      var _a, _b;
      if (!keyboardNavigation() || !(event instanceof KeyboardEvent) || event.key !== "ArrowLeft" && event.key !== "ArrowRight") {
        return;
      }
      const tabElements = Array.from((_b = (_a = tabsContainer()) == null ? void 0 : _a.querySelectorAll('[role="tab"]')) != null ? _b : []);
      const tabIds = tabElements.map((tab) => tab.id);
      const currentIndex = tabIds.indexOf(activeTab());
      const { length } = tabIds;
      let activeIndex = currentIndex;
      let newTab = activeTab();
      if (event.key === "ArrowLeft") {
        activeIndex = currentIndex === 0 ? length - 1 : currentIndex - 1;
        newTab = tabIds[activeIndex];
      } else if (event.key === "ArrowRight") {
        activeIndex = currentIndex === length - 1 ? 0 : currentIndex + 1;
        newTab = tabIds[activeIndex];
      }
      tabElements[activeIndex].focus();
      setActiveTab(newTab);
    };
    root.addEventListener("keydown", handleArrowKeys);
    return solidJs.onCleanup(() => root.removeEventListener("keydown", handleArrowKeys));
  });
};

// src/ui/components/primitives/Tabs/TabsRoot.tsx
var _tmpl$37 = /* @__PURE__ */ web.template(`<div>`);
var TabsContext = solidJs.createContext(void 0);
var useTabsContext = () => {
  const context = solidJs.useContext(TabsContext);
  if (!context) {
    throw new Error("useTabsContext must be used within an TabsContext.Provider");
  }
  return context;
};
var tabsRootVariants = () => "nt-flex nt-flex-col";
var TabsRoot = (props) => {
  var _a;
  const [local, rest] = solidJs.splitProps(props, ["defaultValue", "value", "class", "appearanceKey", "onChange", "children"]);
  const [tabsContainer, setTabsContainer] = solidJs.createSignal();
  const [visibleTabs, setVisibleTabs] = solidJs.createSignal([]);
  const [activeTab, setActiveTab] = solidJs.createSignal((_a = local.defaultValue) != null ? _a : "");
  const style = useStyle();
  useKeyboardNavigation({
    tabsContainer,
    activeTab,
    setActiveTab
  });
  solidJs.createEffect(() => {
    if (local.value) {
      setActiveTab(local.value);
    }
  });
  solidJs.createEffect(() => {
    var _a2;
    (_a2 = local.onChange) == null ? void 0 : _a2.call(local, activeTab());
  });
  return web.createComponent(TabsContext.Provider, {
    value: {
      activeTab,
      setActiveTab,
      visibleTabs,
      setVisibleTabs
    },
    get children() {
      var _el$ = _tmpl$37();
      web.use(setTabsContainer, _el$);
      web.spread(_el$, web.mergeProps({
        get ["class"]() {
          return style(local.appearanceKey || "tabsRoot", cn(tabsRootVariants(), local.class));
        }
      }, rest), false, true);
      web.insert(_el$, () => local.children);
      return _el$;
    }
  });
};

// src/ui/components/primitives/Tabs/TabsContent.tsx
var _tmpl$38 = /* @__PURE__ */ web.template(`<div role=tabpanel>`);
var TabsContent = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["value", "class", "appearanceKey", "children"]);
  const style = useStyle();
  const {
    activeTab
  } = useTabsContext();
  return web.createComponent(solidJs.Show, {
    get when() {
      return activeTab() === local.value;
    },
    get children() {
      var _el$ = _tmpl$38();
      web.spread(_el$, web.mergeProps({
        get ["class"]() {
          return web.memo(() => !!local.class)() ? local.class : style(local.appearanceKey || "tabsContent", activeTab() === local.value ? "nt-block" : "nt-hidden");
        },
        get id() {
          return `tabpanel-${local.value}`;
        },
        get ["aria-labelledby"]() {
          return local.value;
        },
        get ["data-state"]() {
          return activeTab() === local.value ? "active" : "inactive";
        }
      }, rest), false, true);
      web.insert(_el$, () => local.children);
      return _el$;
    }
  });
};
var _tmpl$39 = /* @__PURE__ */ web.template(`<div role=tablist>`);
var _tmpl$211 = /* @__PURE__ */ web.template(`<div class="nt-relative nt-z-[-1]">`);
var tabsListVariants = () => "nt-flex nt-gap-6";
var TabsList = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey", "ref", "children"]);
  const style = useStyle();
  return [(() => {
    var _el$ = _tmpl$39();
    var _ref$ = local.ref;
    typeof _ref$ === "function" ? web.use(_ref$, _el$) : local.ref = _el$;
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "tabsList", cn(tabsListVariants(), local.class));
      }
    }, rest), false, true);
    web.insert(_el$, () => local.children);
    return _el$;
  })(), _tmpl$211()];
};
var tabsTriggerVariants = () => cn("nt-relative nt-transition nt-outline-none nt-text-foreground-alpha-600 nt-pb-[0.625rem]", `after:nt-absolute after:nt-content-[''] after:nt-bottom-0 after:nt-left-0 after:nt-w-full after:nt-h-[2px]`, "after:nt-transition-opacity after:nt-duration-200", "data-[state=active]:after:nt-border-b-2 data-[state=active]:after:nt-border-primary data-[state=active]:after:nt-opacity-100", "data-[state=active]:nt-text-foreground after:nt-border-b-transparent after:nt-opacity-0", "focus-visible:nt-outline-none focus-visible:nt-rounded-lg focus-visible:nt-ring-2 focus-visible:nt-ring-ring focus-visible:nt-ring-offset-2");
var TabsTrigger = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["value", "class", "appearanceKey", "ref", "onClick", "children"]);
  const style = useStyle();
  const {
    activeTab,
    setActiveTab
  } = useTabsContext();
  const clickHandler = () => setActiveTab(local.value);
  return web.createComponent(Button, web.mergeProps({
    variant: "unstyled",
    size: "none",
    ref(r$) {
      var _ref$ = local.ref;
      typeof _ref$ === "function" ? _ref$(r$) : local.ref = r$;
    },
    get id() {
      return local.value;
    },
    get appearanceKey() {
      var _a;
      return (_a = local.appearanceKey) != null ? _a : "tabsTrigger";
    },
    get ["class"]() {
      return web.memo(() => !!local.class)() ? local.class : style(local.appearanceKey || "tabsTrigger", tabsTriggerVariants());
    },
    get onClick() {
      var _a;
      return (_a = local.onClick) != null ? _a : clickHandler;
    },
    role: "tab",
    tabIndex: 0,
    get ["aria-selected"]() {
      return activeTab() === local.value;
    },
    get ["aria-controls"]() {
      return `tabpanel-${local.value}`;
    },
    get ["data-state"]() {
      return activeTab() === local.value ? "active" : "inactive";
    }
  }, rest, {
    get children() {
      return local.children;
    }
  }));
};

// src/ui/components/primitives/Tabs/index.ts
var Tabs = {
  Root: TabsRoot,
  List: TabsList,
  Trigger: TabsTrigger,
  Content: TabsContent
};

// src/ui/components/elements/InboxStatus/constants.ts
var notificationStatusOptionsLocalizationKeys = {
  unreadRead: "inbox.filters.dropdownOptions.default",
  unread: "inbox.filters.dropdownOptions.unread",
  archived: "inbox.filters.dropdownOptions.archived",
  snoozed: "inbox.filters.dropdownOptions.snoozed"
};
var inboxFilterLocalizationKeys = {
  unreadRead: "inbox.filters.labels.default",
  unread: "inbox.filters.labels.unread",
  archived: "inbox.filters.labels.archived",
  snoozed: "inbox.filters.labels.snoozed"
};
var _tmpl$40 = /* @__PURE__ */ web.template(`<span><span>`);
var _tmpl$212 = /* @__PURE__ */ web.template(`<span>`);
var cases = [{
  status: "unreadRead" /* UNREAD_READ */,
  iconKey: "unread",
  icon: Unread
}, {
  status: "unread" /* UNREAD */,
  iconKey: "unread",
  icon: MarkAsUnread
}, {
  status: "snoozed" /* SNOOZED */,
  iconKey: "clock",
  icon: Clock
}, {
  status: "archived" /* ARCHIVED */,
  iconKey: "markAsArchived",
  icon: MarkAsArchived
}];
var StatusOptions = (props) => {
  const {
    isSnoozeEnabled
  } = useInboxContext();
  const filteredCases = () => {
    return cases.filter((c) => c.status !== "snoozed" /* SNOOZED */ || isSnoozeEnabled());
  };
  return web.createComponent(solidJs.For, {
    get each() {
      return filteredCases();
    },
    children: (c) => web.createComponent(StatusItem, {
      get localizationKey() {
        return notificationStatusOptionsLocalizationKeys[c.status];
      },
      onClick: () => {
        props.setStatus(c.status);
      },
      get isSelected() {
        return props.status === c.status;
      },
      get icon() {
        return c.icon;
      },
      get iconKey() {
        return c.iconKey;
      }
    })
  });
};
var StatusItem = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const itemIconClass = style("inboxStatus__dropdownItemLeft__icon", "nt-size-3", {
    iconKey: props.iconKey
  });
  const checkIconClass = style("inboxStatus__dropdownItemCheck__icon", "nt-size-3", {
    iconKey: "check"
  });
  return web.createComponent(Dropdown.Item, {
    get ["class"]() {
      return style("inboxStatus__dropdownItem", cn(dropdownItemVariants(), "nt-flex nt-gap-8 nt-justify-between"));
    },
    get onClick() {
      return props.onClick;
    },
    get children() {
      return [(() => {
        var _el$ = _tmpl$40(), _el$2 = _el$.firstChild;
        web.insert(_el$, web.createComponent(IconRendererWrapper, {
          get iconKey() {
            return props.iconKey;
          },
          "class": itemIconClass,
          get fallback() {
            return (() => {
              var _el$3 = _tmpl$212();
              web.className(_el$3, itemIconClass);
              web.insert(_el$3, () => props.icon());
              return _el$3;
            })();
          }
        }), _el$2);
        web.insert(_el$2, () => t(props.localizationKey));
        web.effect((_p$) => {
          var _v$ = style("inboxStatus__dropdownItemLabelContainer", "nt-flex nt-gap-2 nt-items-center"), _v$2 = props.localizationKey, _v$3 = style("inboxStatus__dropdownItemLabel", "nt-leading-none");
          _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
          _v$2 !== _p$.t && web.setAttribute(_el$2, "data-localization", _p$.t = _v$2);
          _v$3 !== _p$.a && web.className(_el$2, _p$.a = _v$3);
          return _p$;
        }, {
          e: void 0,
          t: void 0,
          a: void 0
        });
        return _el$;
      })(), web.createComponent(solidJs.Show, {
        get when() {
          return props.isSelected;
        },
        get children() {
          return web.createComponent(IconRendererWrapper, {
            iconKey: "check",
            "class": checkIconClass,
            get fallback() {
              return web.createComponent(Check, {
                "class": checkIconClass
              });
            }
          });
        }
      })];
    }
  });
};

// src/ui/components/elements/InboxStatus/InboxStatusDropdown.tsx
var _tmpl$41 = /* @__PURE__ */ web.template(`<span>`);
var StatusDropdown = () => {
  const style = useStyle();
  const {
    status,
    setStatus
  } = useInboxContext();
  const {
    t
  } = useLocalization();
  const arrowDropDownIconClass = style("inboxStatus__dropdownItemRight__icon", "nt-text-foreground-alpha-600 nt-size-4", {
    iconKey: "arrowDropDown"
  });
  return web.createComponent(Dropdown.Root, {
    get children() {
      return [web.createComponent(Dropdown.Trigger, {
        get ["class"]() {
          return style("inboxStatus__dropdownTrigger", cn(buttonVariants({
            variant: "unstyled",
            size: "none"
          }), "nt-gap-0.5"));
        },
        asChild: (triggerProps) => web.createComponent(Button, web.mergeProps({
          variant: "unstyled",
          size: "none"
        }, triggerProps, {
          get children() {
            return [(() => {
              var _el$ = _tmpl$41();
              web.insert(_el$, () => t(inboxFilterLocalizationKeys[status()]));
              web.effect((_p$) => {
                var _v$ = inboxFilterLocalizationKeys[status()], _v$2 = style("inboxStatus__title", "nt-text-base");
                _v$ !== _p$.e && web.setAttribute(_el$, "data-localization", _p$.e = _v$);
                _v$2 !== _p$.t && web.className(_el$, _p$.t = _v$2);
                return _p$;
              }, {
                e: void 0,
                t: void 0
              });
              return _el$;
            })(), web.createComponent(IconRendererWrapper, {
              iconKey: "arrowDropDown",
              "class": arrowDropDownIconClass,
              get fallback() {
                return web.createComponent(ArrowDropDown, {
                  "class": arrowDropDownIconClass
                });
              }
            })];
          }
        }))
      }), web.createComponent(Dropdown.Content, {
        appearanceKey: "inboxStatus__dropdownContent",
        get children() {
          return web.createComponent(StatusOptions, {
            setStatus,
            get status() {
              return status();
            }
          });
        }
      })];
    }
  });
};

// src/ui/api/hooks/useArchiveAll.ts
var useArchiveAll = (props) => {
  const novu = useNovu();
  const archiveAll = (..._0) => chunk7B52C2XE_js.__async(void 0, [..._0], function* ({
    tags,
    data
  } = {}) {
    var _a, _b;
    try {
      yield novu.notifications.archiveAll({ tags, data });
      (_a = props == null ? void 0 : props.onSuccess) == null ? void 0 : _a.call(props);
    } catch (error) {
      (_b = void 0 ) == null ? void 0 : _b.call(props, error);
    }
  });
  return { archiveAll };
};

// src/ui/api/hooks/useArchiveAllRead.ts
var useArchiveAllRead = (props) => {
  const novu = useNovu();
  const archiveAllRead = (..._0) => chunk7B52C2XE_js.__async(void 0, [..._0], function* ({
    tags,
    data
  } = {}) {
    var _a, _b;
    try {
      yield novu.notifications.archiveAllRead({ tags, data });
      (_a = props == null ? void 0 : props.onSuccess) == null ? void 0 : _a.call(props);
    } catch (error) {
      (_b = void 0 ) == null ? void 0 : _b.call(props, error);
    }
  });
  return { archiveAllRead };
};
var useNotificationsInfiniteScroll = (props) => {
  const novu = useNovu();
  let filter = chunk7B52C2XE_js.__spreadValues({}, props.options());
  const [data, { initialLoading, setEl, end, mutate, reset }] = createInfiniteScroll(
    (after) => chunk7B52C2XE_js.__async(void 0, null, function* () {
      var _a, _b;
      const { data: data2 } = yield novu.notifications.list(chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, props.options() || {}), { after }));
      return { data: (_a = data2 == null ? void 0 : data2.notifications) != null ? _a : [], hasMore: (_b = data2 == null ? void 0 : data2.hasMore) != null ? _b : false };
    }),
    {
      paginationField: "id"
    }
  );
  solidJs.onMount(() => {
    const listener = ({ data: data2 }) => {
      if (!data2 || !chunkRN7LHLHM_js.isSameFilter(filter, data2.filter)) {
        return;
      }
      mutate({ data: data2.notifications, hasMore: data2.hasMore });
    };
    const cleanup = novu.on("notifications.list.updated", listener);
    solidJs.onCleanup(() => cleanup());
  });
  solidJs.createEffect(() => chunk7B52C2XE_js.__async(void 0, null, function* () {
    const newFilter = chunk7B52C2XE_js.__spreadValues({}, props.options());
    if (chunkRN7LHLHM_js.isSameFilter(filter, newFilter)) {
      return;
    }
    novu.notifications.clearCache();
    yield reset();
    filter = newFilter;
  }));
  const refetch = (_0) => chunk7B52C2XE_js.__async(void 0, [_0], function* ({ filter: filter2 }) {
    novu.notifications.clearCache({ filter: filter2 });
    yield reset();
  });
  return { data, initialLoading, setEl, end, refetch };
};
var usePreferences = (options) => {
  const novu = useNovu();
  const [loading, setLoading] = solidJs.createSignal(true);
  const [preferences, { mutate, refetch }] = solidJs.createResource(options || {}, (_0) => chunk7B52C2XE_js.__async(void 0, [_0], function* ({ tags }) {
    try {
      const response = yield novu.preferences.list({ tags });
      return response.data;
    } catch (error) {
      console.error("Error fetching preferences:", error);
      throw error;
    }
  }));
  solidJs.onMount(() => {
    const listener = ({ data }) => {
      if (!data) {
        return;
      }
      mutate(data);
    };
    const cleanup = novu.on("preferences.list.updated", listener);
    solidJs.onCleanup(() => cleanup());
  });
  solidJs.createEffect(() => {
    setLoading(preferences.loading);
  });
  return { preferences, loading, mutate, refetch };
};

// src/ui/api/hooks/useReadAll.ts
var useReadAll = (props) => {
  const novu = useNovu();
  const readAll = (..._0) => chunk7B52C2XE_js.__async(void 0, [..._0], function* ({
    tags,
    data
  } = {}) {
    var _a, _b;
    try {
      yield novu.notifications.readAll({ tags, data });
      (_a = props == null ? void 0 : props.onSuccess) == null ? void 0 : _a.call(props);
    } catch (error) {
      (_b = void 0 ) == null ? void 0 : _b.call(props, error);
    }
  });
  return { readAll };
};

// src/ui/components/elements/Header/MoreActionsOptions.tsx
var _tmpl$43 = /* @__PURE__ */ web.template(`<span>`);
var iconKeyToComponentMap = {
  markAsRead: MarkAsRead,
  markAsArchived: MarkAsArchived,
  markAsArchivedRead: MarkAsArchivedRead
};
var MoreActionsOptions = () => {
  const {
    filter
  } = useInboxContext();
  const {
    readAll
  } = useReadAll();
  const {
    archiveAll
  } = useArchiveAll();
  const {
    archiveAllRead
  } = useArchiveAllRead();
  return [web.createComponent(ActionsItem, {
    localizationKey: "notifications.actions.readAll",
    onClick: () => readAll({
      tags: filter().tags,
      data: filter().data
    }),
    iconKey: "markAsRead"
  }), web.createComponent(ActionsItem, {
    localizationKey: "notifications.actions.archiveAll",
    onClick: () => archiveAll({
      tags: filter().tags,
      data: filter().data
    }),
    iconKey: "markAsArchived"
  }), web.createComponent(ActionsItem, {
    localizationKey: "notifications.actions.archiveRead",
    onClick: () => archiveAllRead({
      tags: filter().tags,
      data: filter().data
    }),
    iconKey: "markAsArchivedRead"
  })];
};
var ActionsItem = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const DefaultIconComponent = iconKeyToComponentMap[props.iconKey];
  const moreActionsIconClass = style("moreActions__dropdownItemLeft__icon", "nt-size-3", {
    iconKey: props.iconKey
  });
  return web.createComponent(Dropdown.Item, {
    get ["class"]() {
      return style("moreActions__dropdownItem", cn(dropdownItemVariants(), "nt-flex nt-gap-2"));
    },
    get onClick() {
      return props.onClick;
    },
    get children() {
      return [web.createComponent(IconRendererWrapper, {
        get iconKey() {
          return props.iconKey;
        },
        "class": moreActionsIconClass,
        get fallback() {
          return DefaultIconComponent && DefaultIconComponent({
            class: moreActionsIconClass
          });
        }
      }), (() => {
        var _el$ = _tmpl$43();
        web.insert(_el$, () => t(props.localizationKey));
        web.effect((_p$) => {
          var _v$ = props.localizationKey, _v$2 = style("moreActions__dropdownItemLabel", "nt-leading-none");
          _v$ !== _p$.e && web.setAttribute(_el$, "data-localization", _p$.e = _v$);
          _v$2 !== _p$.t && web.className(_el$, _p$.t = _v$2);
          return _p$;
        }, {
          e: void 0,
          t: void 0
        });
        return _el$;
      })()];
    }
  });
};

// src/ui/components/elements/Header/MoreActionsDropdown.tsx
var MoreActionsDropdown = () => {
  const style = useStyle();
  const {
    status
  } = useInboxContext();
  const dotsIconClass = style("moreActions__dots", "nt-size-5", {
    iconKey: "dots"
  });
  return web.createComponent(solidJs.Show, {
    get when() {
      return web.memo(() => status() !== "archived" /* ARCHIVED */)() && status() !== "snoozed" /* SNOOZED */;
    },
    get children() {
      return web.createComponent(Dropdown.Root, {
        get children() {
          return [web.createComponent(Dropdown.Trigger, {
            get ["class"]() {
              return style("moreActions__dropdownTrigger");
            },
            asChild: (triggerProps) => web.createComponent(Button, web.mergeProps({
              variant: "ghost",
              size: "iconSm"
            }, triggerProps, {
              get children() {
                return web.createComponent(IconRendererWrapper, {
                  iconKey: "dots",
                  "class": dotsIconClass,
                  get fallback() {
                    return web.createComponent(Dots, {
                      "class": dotsIconClass
                    });
                  }
                });
              }
            }))
          }), web.createComponent(Dropdown.Content, {
            appearanceKey: "moreActions__dropdownContent",
            get children() {
              return web.createComponent(MoreActionsOptions, {});
            }
          })];
        }
      });
    }
  });
};

// src/ui/components/elements/Header/ActionsContainer.tsx
var _tmpl$44 = /* @__PURE__ */ web.template(`<div>`);
var ActionsContainer = (props) => {
  const style = useStyle();
  const cogsIconClass = style("icon", "nt-size-5", {
    iconKey: "cogs"
  });
  return (() => {
    var _el$ = _tmpl$44();
    web.insert(_el$, web.createComponent(MoreActionsDropdown, {}), null);
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return props.showPreferences;
      },
      children: (showPreferences) => web.createComponent(Button, {
        appearanceKey: "preferences__button",
        variant: "ghost",
        size: "iconSm",
        get onClick() {
          return showPreferences();
        },
        get children() {
          return web.createComponent(IconRendererWrapper, {
            iconKey: "cogs",
            "class": cogsIconClass,
            get fallback() {
              return web.createComponent(Cogs, {
                "class": cogsIconClass
              });
            }
          });
        }
      })
    }), null);
    web.effect(() => web.className(_el$, style("moreActionsContainer", "nt-flex nt-gap-3")));
    return _el$;
  })();
};

// src/ui/components/elements/Header/Header.tsx
var _tmpl$45 = /* @__PURE__ */ web.template(`<div>`);
var Header = (props) => {
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$45();
    web.insert(_el$, web.createComponent(StatusDropdown, {}), null);
    web.insert(_el$, web.createComponent(ActionsContainer, {
      get showPreferences() {
        return props.navigateToPreferences;
      }
    }), null);
    web.effect(() => web.className(_el$, style("inboxHeader", cn("nt-flex nt-bg-neutral-alpha-25 nt-shrink-0 nt-justify-between nt-items-center nt-w-full nt-pb-2 nt-pt-2.5 nt-px-4"))));
    return _el$;
  })();
};
var _tmpl$46 = /* @__PURE__ */ web.template(`<div>`);
var Root = (props) => {
  const [_, rest] = solidJs.splitProps(props, ["class"]);
  const {
    id
  } = useAppearance();
  const style = useStyle();
  const {
    hideBranding
  } = useInboxContext();
  return [web.createComponent(solidJs.Show, {
    get when() {
      return !hideBranding();
    },
    children: new Comment(" Powered by Novu - https://novu.co ")
  }), (() => {
    var _el$ = _tmpl$46();
    web.spread(_el$, web.mergeProps({
      get id() {
        return `novu-root-${id()}`;
      },
      get ["class"]() {
        return style("root"), cn("novu", id(), "nt-text-foreground nt-h-full [interpolate-size:allow-keywords]");
      }
    }, rest), false, false);
    return _el$;
  })()];
};
var _tmpl$47 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 14 14"><path fill=currentColor d="M2.8 8.575V5.162a2.362 2.362 0 1 1 4.725 0v3.675a1.313 1.313 0 1 0 2.625 0V5.335a1.575 1.575 0 1 1 1.05 0v3.502a2.362 2.362 0 1 1-4.725 0V5.162a1.312 1.312 0 1 0-2.625 0v3.413h1.575l-2.1 2.625-2.1-2.625H2.8Z"></path><path fill=url(#a) d="M2.8 8.575V5.162a2.362 2.362 0 1 1 4.725 0v3.675a1.313 1.313 0 1 0 2.625 0V5.335a1.575 1.575 0 1 1 1.05 0v3.502a2.362 2.362 0 1 1-4.725 0V5.162a1.312 1.312 0 1 0-2.625 0v3.413h1.575l-2.1 2.625-2.1-2.625H2.8Z"></path><defs><linearGradient id=a x1=1.225 x2=12.251 y1=6.722 y2=6.779 gradientUnits=userSpaceOnUse><stop stop-color=currentColor></stop><stop offset=1 stop-color=currentColor>`);
var RouteFill = (props) => {
  return (() => {
    var _el$ = _tmpl$47();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$48 = /* @__PURE__ */ web.template(`<div><div>`);
var isInterpolateSizeSupported = () => {
  return CSS.supports("interpolate-size", "allow-keywords");
};
var Collapsible = (props) => {
  const supportsInterpolateSize = isInterpolateSizeSupported();
  const style = useStyle();
  let contentRef;
  const [enableTransition, setEnableTransition] = solidJs.createSignal(false);
  const [scrollHeight, setScrollHeight] = solidJs.createSignal(0);
  const updateScrollHeight = () => {
    setScrollHeight((contentRef == null ? void 0 : contentRef.scrollHeight) || 0);
  };
  solidJs.createEffect(() => {
    requestAnimationFrame(() => setEnableTransition(true));
    const resizeObserver = new ResizeObserver(() => {
      updateScrollHeight();
    });
    if (contentRef && !supportsInterpolateSize) {
      resizeObserver.observe(contentRef);
    }
    updateScrollHeight();
    solidJs.onCleanup(() => {
      resizeObserver.disconnect();
    });
  });
  const height = () => {
    if (supportsInterpolateSize) {
      return props.open ? "max-content" : "0px";
    }
    return props.open ? `${scrollHeight()}px` : "0px";
  };
  return (() => {
    var _el$ = _tmpl$48(), _el$2 = _el$.firstChild;
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style("collapsible", props.class);
      },
      get style() {
        return {
          overflow: "hidden",
          opacity: props.open ? 1 : 0,
          transition: enableTransition() ? "height 250ms ease-in-out, opacity 250ms ease-in-out" : "none",
          height: height()
        };
      }
    }, props), false, true);
    var _ref$ = contentRef;
    typeof _ref$ === "function" ? web.use(_ref$, _el$2) : contentRef = _el$2;
    web.insert(_el$2, () => props.children);
    return _el$;
  })();
};
var _tmpl$49 = /* @__PURE__ */ web.template(`<label><input type=checkbox class=nt-sr-only><div>`);
var Switch = (props) => {
  const style = useStyle();
  const handleChange = () => {
    var _a;
    if (props.disabled) return;
    const nextState = getNextState((_a = props.state) != null ? _a : "disabled");
    props.onChange(nextState);
  };
  const getNextState = (currentState) => {
    switch (currentState) {
      case "enabled":
        return "disabled";
      case "disabled":
        return "enabled";
      case "indeterminate":
        return "enabled";
      default:
        return "disabled";
    }
  };
  const isChecked = () => props.state === "enabled";
  const isIndeterminate = () => props.state === "indeterminate";
  const state = () => props.state;
  const disabled = () => props.disabled;
  return (() => {
    var _el$ = _tmpl$49(), _el$2 = _el$.firstChild, _el$3 = _el$2.nextSibling;
    _el$2.addEventListener("change", handleChange);
    web.effect((_p$) => {
      var _v$ = style("channelSwitch", cn("nt-relative nt-inline-flex nt-cursor-pointer nt-items-center", {
        "nt-opacity-50 nt-cursor-not-allowed": disabled()
      })), _v$2 = disabled(), _v$3 = style("channelSwitchThumb", cn(`nt-h-4 nt-w-7 nt-rounded-full nt-bg-neutral-alpha-300 after:nt-absolute after:nt-top-0.5 after:nt-size-3 after:nt-left-0.5 after:nt-rounded-full after:nt-bg-background after:nt-transition-all after:nt-content-[''] nt-transition-all nt-duration-200 after:nt-duration-200 shadow-sm`, {
        "nt-bg-primary nt-shadow-none nt-border-neutral-alpha-400 after:nt-translate-x-full after:nt-border-background": isChecked(),
        "after:nt-translate-x-1/2": isIndeterminate()
      })), _v$4 = state();
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && (_el$2.disabled = _p$.t = _v$2);
      _v$3 !== _p$.a && web.className(_el$3, _p$.a = _v$3);
      _v$4 !== _p$.o && web.setAttribute(_el$3, "data-state", _p$.o = _v$4);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0,
      o: void 0
    });
    web.effect(() => _el$2.checked = isChecked());
    return _el$;
  })();
};

// src/ui/components/elements/Preferences/ChannelRow.tsx
var _tmpl$50 = /* @__PURE__ */ web.template(`<div><div><div></div><span></span></div><div>`);
var ChannelRow = (props) => {
  const style = useStyle();
  const updatePreference = (enabled) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    props.onChange({
      [props.channel.channel]: enabled
    });
  });
  const onChange = (checked) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    yield updatePreference(checked);
  });
  const state = () => props.channel.state;
  const channel = () => props.channel.channel;
  return (() => {
    var _el$ = _tmpl$50(), _el$2 = _el$.firstChild, _el$3 = _el$2.firstChild, _el$4 = _el$3.nextSibling, _el$5 = _el$2.nextSibling;
    web.insert(_el$3, web.createComponent(ChannelIcon, {
      appearanceKey: "channel__icon",
      get channel() {
        return channel();
      },
      "class": "nt-size-3"
    }));
    web.insert(_el$4, () => getLabel(channel()));
    web.insert(_el$5, web.createComponent(Switch, {
      get state() {
        return state();
      },
      onChange: (newState) => onChange(newState === "enabled")
    }));
    web.effect((_p$) => {
      var _v$ = style("channelContainer", "nt-flex nt-justify-between nt-items-center nt-gap-2 data-[disabled=true]:nt-text-foreground-alpha-600"), _v$2 = style("channelLabelContainer", "nt-flex nt-items-center nt-gap-2 nt-text-foreground"), _v$3 = style("channelIconContainer", "nt-p-1 nt-rounded-md nt-bg-neutral-alpha-25 nt-text-foreground-alpha-300"), _v$4 = style("channelLabel", "nt-text-sm nt-font-semibold"), _v$5 = style("channelSwitchContainer", "nt-flex nt-items-center");
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$2, _p$.t = _v$2);
      _v$3 !== _p$.a && web.className(_el$3, _p$.a = _v$3);
      _v$4 !== _p$.o && web.className(_el$4, _p$.o = _v$4);
      _v$5 !== _p$.i && web.className(_el$5, _p$.i = _v$5);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0,
      o: void 0,
      i: void 0
    });
    return _el$;
  })();
};
var ChannelIcon = (props) => {
  const style = useStyle();
  const iconMap = {
    ["in_app" /* IN_APP */]: {
      key: "inApp",
      component: web.createComponent(InApp, {
        get ["class"]() {
          return style(props.appearanceKey, props.class, {
            iconKey: "inApp"
          });
        }
      })
    },
    ["email" /* EMAIL */]: {
      key: "email",
      component: web.createComponent(Email, {
        get ["class"]() {
          return style(props.appearanceKey, props.class, {
            iconKey: "email"
          });
        }
      })
    },
    ["push" /* PUSH */]: {
      key: "push",
      component: web.createComponent(Push, {
        get ["class"]() {
          return style(props.appearanceKey, props.class, {
            iconKey: "push"
          });
        }
      })
    },
    ["sms" /* SMS */]: {
      key: "sms",
      component: web.createComponent(Sms, {
        get ["class"]() {
          return style(props.appearanceKey, props.class, {
            iconKey: "sms"
          });
        }
      })
    },
    ["chat" /* CHAT */]: {
      key: "chat",
      component: web.createComponent(Chat, {
        get ["class"]() {
          return style(props.appearanceKey, props.class, {
            iconKey: "chat"
          });
        }
      })
    }
  };
  const iconData = iconMap[props.channel];
  if (!iconData) {
    return null;
  }
  return web.createComponent(IconRendererWrapper, {
    get iconKey() {
      return iconData.key;
    },
    get fallback() {
      return iconData.component;
    },
    get ["class"]() {
      return style(props.appearanceKey, props.class, {
        iconKey: iconData.key
      });
    }
  });
};
var getLabel = (channel) => {
  switch (channel) {
    case "in_app" /* IN_APP */:
      return "In-App";
    case "email" /* EMAIL */:
      return "Email";
    case "push" /* PUSH */:
      return "Push";
    case "sms" /* SMS */:
      return "SMS";
    case "chat" /* CHAT */:
      return "Chat";
    default:
      return "";
  }
};

// src/ui/components/elements/Preferences/PreferencesRow.tsx
var _tmpl$51 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$213 = /* @__PURE__ */ web.template(`<div><div><div><div><span></span></div></div><span>`);
var _tmpl$310 = /* @__PURE__ */ web.template(`<span>`);
var iconKeyToComponentMap2 = {
  cogs: Cogs,
  routeFill: RouteFill
};
var PreferencesRow = (props) => {
  const style = useStyle();
  const [isOpenChannels, setIsOpenChannels] = solidJs.createSignal(false);
  const {
    t
  } = useLocalization();
  const channels = solidJs.createMemo(() => {
    var _a, _b;
    return Object.keys((_b = (_a = props.preference) == null ? void 0 : _a.channels) != null ? _b : {}).map((channel) => {
      var _a2;
      return {
        channel,
        state: ((_a2 = props.preference) == null ? void 0 : _a2.channels[channel]) ? "enabled" : "disabled"
      };
    });
  });
  const iconClass = style("workflowLabelIcon", "nt-text-foreground-alpha-600 nt-size-3.5", {
    iconKey: "cogs"
  });
  const arrowDropDownIconClass = style("workflowArrow__icon", "nt-text-foreground-alpha-600 nt-size-4", {
    iconKey: "arrowDropDown"
  });
  const DefaultIconComponent = iconKeyToComponentMap2[props.iconKey];
  return web.createComponent(solidJs.Show, {
    get when() {
      return channels().length > 0;
    },
    get children() {
      var _el$ = _tmpl$213(), _el$2 = _el$.firstChild, _el$3 = _el$2.firstChild, _el$4 = _el$3.firstChild, _el$5 = _el$4.firstChild, _el$6 = _el$3.nextSibling;
      _el$2.$$click = () => {
        setIsOpenChannels((prev) => !prev);
      };
      web.insert(_el$4, web.createComponent(IconRendererWrapper, {
        get iconKey() {
          return props.iconKey;
        },
        "class": iconClass,
        get fallback() {
          return DefaultIconComponent && DefaultIconComponent({
            class: iconClass
          });
        }
      }), _el$5);
      web.insert(_el$5, () => {
        var _a, _b, _c;
        return t((_c = (_b = (_a = props.preference) == null ? void 0 : _a.workflow) == null ? void 0 : _b.identifier) != null ? _c : "preferences.global");
      });
      web.insert(_el$3, web.createComponent(Collapsible, {
        get open() {
          return !isOpenChannels();
        },
        get children() {
          return web.createComponent(WorkflowDescription, {
            get channels() {
              var _a, _b;
              return (_b = (_a = props.preference) == null ? void 0 : _a.channels) != null ? _b : {};
            },
            appearanceKey: "workflowDescription",
            "class": "nt-overflow-hidden"
          });
        }
      }), null);
      web.insert(_el$6, web.createComponent(IconRendererWrapper, {
        iconKey: "arrowDropDown",
        "class": arrowDropDownIconClass,
        get fallback() {
          return web.createComponent(ArrowDropDown, {
            "class": arrowDropDownIconClass
          });
        }
      }));
      web.insert(_el$, web.createComponent(Collapsible, {
        get open() {
          return isOpenChannels();
        },
        get children() {
          var _el$7 = _tmpl$51();
          web.insert(_el$7, web.createComponent(solidJs.Index, {
            get each() {
              return channels();
            },
            children: (channel) => web.createComponent(ChannelRow, {
              get channel() {
                return channel();
              },
              get workflowId() {
                var _a, _b;
                return (_b = (_a = props.preference) == null ? void 0 : _a.workflow) == null ? void 0 : _b.id;
              },
              get onChange() {
                var _a, _b;
                return props.onChange((_b = (_a = props.preference) == null ? void 0 : _a.workflow) == null ? void 0 : _b.identifier);
              }
            })
          }));
          web.effect(() => web.className(_el$7, style("channelsContainer", "nt-flex nt-bg-background nt-border nt-border-neutral-alpha-200 nt-rounded-lg nt-p-2 nt-flex-col nt-gap-1 nt-overflow-hidden")));
          return _el$7;
        }
      }), null);
      web.effect((_p$) => {
        var _a, _b, _c;
        var _v$ = style("workflowContainer", `nt-p-1 nt-bg-neutral-alpha-25 nt-rounded-lg nt-border nt-border-neutral-alpha-50`), _v$2 = isOpenChannels(), _v$3 = style("workflowLabelContainer", "nt-flex nt-justify-between nt-p-1 nt-flex-nowrap nt-self-stretch nt-cursor-pointer nt-items-center nt-overflow-hidden"), _v$4 = style("workflowLabelHeader", "nt-overflow-hidden"), _v$5 = style("workflowLabelHeaderContainer", "nt-flex nt-items-center nt-gap-1"), _v$6 = style("workflowLabel", "nt-text-sm nt-font-semibold nt-truncate nt-text-start"), _v$7 = (_c = (_b = (_a = props.preference) == null ? void 0 : _a.workflow) == null ? void 0 : _b.identifier) != null ? _c : "preferences.global", _v$8 = isOpenChannels(), _v$9 = style("workflowContainerRight__icon", `nt-text-foreground-alpha-600 nt-transition-all nt-duration-200 data-[open=true]:nt-transform data-[open=true]:nt-rotate-180`), _v$10 = isOpenChannels();
        _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
        _v$2 !== _p$.t && web.setAttribute(_el$, "data-open", _p$.t = _v$2);
        _v$3 !== _p$.a && web.className(_el$2, _p$.a = _v$3);
        _v$4 !== _p$.o && web.className(_el$3, _p$.o = _v$4);
        _v$5 !== _p$.i && web.className(_el$4, _p$.i = _v$5);
        _v$6 !== _p$.n && web.className(_el$5, _p$.n = _v$6);
        _v$7 !== _p$.s && web.setAttribute(_el$5, "data-localization", _p$.s = _v$7);
        _v$8 !== _p$.h && web.setAttribute(_el$5, "data-open", _p$.h = _v$8);
        _v$9 !== _p$.r && web.className(_el$6, _p$.r = _v$9);
        _v$10 !== _p$.d && web.setAttribute(_el$6, "data-open", _p$.d = _v$10);
        return _p$;
      }, {
        e: void 0,
        t: void 0,
        a: void 0,
        o: void 0,
        i: void 0,
        n: void 0,
        s: void 0,
        h: void 0,
        r: void 0,
        d: void 0
      });
      return _el$;
    }
  });
};
var WorkflowDescription = (props) => {
  const style = useStyle();
  const channelNames = () => {
    const channels = [];
    for (const key in props.channels) {
      if (props.channels[key] !== void 0) {
        const isDisabled = !props.channels[key];
        const element = (() => {
          var _el$8 = _tmpl$310();
          web.setAttribute(_el$8, "data-disabled", isDisabled);
          web.insert(_el$8, () => getLabel(key));
          web.effect(() => web.className(_el$8, style("channelName", "data-[disabled=true]:nt-text-foreground-alpha-400")));
          return _el$8;
        })();
        channels.push(element);
      }
    }
    return channels.map((c, index) => [c, web.memo(() => index < channels.length - 1 && ", ")]);
  };
  return (() => {
    var _el$9 = _tmpl$51();
    web.insert(_el$9, channelNames);
    web.effect(() => web.className(_el$9, style(props.appearanceKey, cn("nt-text-sm nt-text-foreground-alpha-600 nt-text-start", props.class))));
    return _el$9;
  })();
};
web.delegateEvents(["click"]);
var _tmpl$53 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$214 = /* @__PURE__ */ web.template(`<div><div></div><div>`);
var SkeletonText = (props) => {
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$53();
    web.effect(() => web.className(_el$, style(props.appearanceKey, cn("nt-w-full nt-h-3 nt-rounded nt-bg-gradient-to-r nt-from-foreground-alpha-50 nt-to-transparent", props.class))));
    return _el$;
  })();
};
var SkeletonAvatar = (props) => {
  const style = useStyle();
  return (() => {
    var _el$2 = _tmpl$53();
    web.effect(() => web.className(_el$2, style(props.appearanceKey, cn("nt-size-8 nt-rounded-lg nt-bg-gradient-to-r nt-from-foreground-alpha-50 nt-to-transparent", props.class))));
    return _el$2;
  })();
};
var SkeletonSwitch = (props) => {
  const style = useStyle();
  return (() => {
    var _el$3 = _tmpl$214(), _el$4 = _el$3.firstChild, _el$5 = _el$4.nextSibling;
    web.effect((_p$) => {
      var _v$ = style(props.appearanceKey, cn("nt-relative nt-inline-flex nt-items-center", props.class)), _v$2 = style(props.appearanceKey, "nt-h-4 nt-w-7 nt-rounded-full nt-bg-gradient-to-r nt-from-foreground-alpha-50 nt-to-transparent"), _v$3 = style(props.thumbAppearanceKey, "nt-absolute nt-top-0.5 nt-left-0.5 nt-size-3 nt-rounded-full nt-bg-background nt-shadow");
      _v$ !== _p$.e && web.className(_el$3, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$4, _p$.t = _v$2);
      _v$3 !== _p$.a && web.className(_el$5, _p$.a = _v$3);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0
    });
    return _el$3;
  })();
};

// src/ui/components/elements/Preferences/PreferencesListSkeleton.tsx
var _tmpl$54 = /* @__PURE__ */ web.template(`<div>`);
var channelIcons = [InApp, Email, Sms, Push, Chat];
var PreferencesListSkeleton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  return (() => {
    var _el$ = _tmpl$54();
    web.insert(_el$, web.createComponent(Motion.div, {
      get animate() {
        return {
          scale: props.loading ? 1 : 0.7
        };
      },
      transition: {
        duration: 0.6,
        easing: [0.39, 0.24, 0.3, 1],
        delay: 0.3
      },
      get ["class"]() {
        return style("preferencesList__skeleton", "nt-flex nt-relative nt-mx-auto nt-flex-col nt-w-full nt-mb-4");
      },
      get children() {
        return [web.memo(() => Array.from({
          length: 5
        }).map((_, i) => {
          const Icon = channelIcons[i];
          return web.createComponent(Motion.div, {
            get animate() {
              return {
                marginBottom: props.loading ? 0 : "16px",
                borderWidth: props.loading ? 0 : "1px",
                borderRadius: props.loading ? 0 : "var(--nv-radius-lg)"
              };
            },
            transition: {
              duration: 0.5,
              delay: 0.3,
              easing: "ease-in-out"
            },
            get ["class"]() {
              return style("preferencesList__skeletonContent", "nt-flex nt-border-neutral-alpha-50 nt-items-center nt-gap-3 nt-p-3 nt-bg-neutral-alpha-25");
            },
            get children() {
              return [web.createComponent(Icon, {
                get ["class"]() {
                  return style("preferencesList__skeletonIcon", "nt-size-8 nt-p-2 nt-rounded-lg nt-bg-neutral-alpha-100");
                }
              }), (() => {
                var _el$3 = _tmpl$54();
                web.insert(_el$3, web.createComponent(SkeletonText, {
                  appearanceKey: "notificationList__skeletonText",
                  "class": "nt-h-2 nt-w-1/3 nt-bg-neutral-alpha-50 nt-rounded"
                }), null);
                web.insert(_el$3, web.createComponent(SkeletonText, {
                  appearanceKey: "preferencesList__skeletonText",
                  "class": "nt-h-2 nt-w-2/3 nt-bg-neutral-alpha-50 nt-rounded"
                }), null);
                web.effect(() => web.className(_el$3, style("preferencesList__skeletonItem", "nt-flex nt-flex-col nt-gap-2 nt-flex-1")));
                return _el$3;
              })(), web.createComponent(SkeletonSwitch, {
                appearanceKey: "preferencesList__skeletonSwitch",
                thumbAppearanceKey: "preferencesList__skeletonSwitchThumb"
              })];
            }
          });
        })), (() => {
          var _el$2 = _tmpl$54();
          web.effect(() => web.className(_el$2, style("notificationListEmptyNoticeOverlay", "nt-absolute nt-size-full nt-z-10 nt-inset-0 nt-bg-gradient-to-b nt-from-transparent nt-to-background")));
          return _el$2;
        })()];
      }
    }), null);
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return !props.loading;
      },
      get children() {
        return web.createComponent(Motion.p, {
          initial: {
            opacity: 0,
            y: -4,
            filter: "blur(4px)"
          },
          get animate() {
            return {
              opacity: props.loading ? 0 : 1,
              y: 0,
              filter: "blur(0px)"
            };
          },
          transition: {
            duration: 0.7,
            easing: [0.39, 0.24, 0.3, 1],
            delay: 0.6
          },
          get ["class"]() {
            return style("preferencesListEmptyNotice"), "nt-text-center";
          },
          "data-localization": "preferences.emptyNotice",
          get children() {
            return t("preferences.emptyNotice");
          }
        });
      }
    }), null);
    web.effect(() => web.className(_el$, style("preferencesListEmptyNoticeContainer", "nt-flex nt-flex-col nt-items-center nt-h-fit nt-w-full nt-text-sm nt-text-foreground-alpha-400 nt-text-center")));
    return _el$;
  })();
};

// src/ui/components/elements/Preferences/DefaultPreferences.tsx
var DefaultPreferences = (props) => {
  const workflowPreferences = solidJs.createMemo(() => props.workflowPreferences);
  const updatePreference = (workflowIdentifier) => (channels) => {
    var _a;
    const preference = (_a = workflowPreferences()) == null ? void 0 : _a.find((pref) => {
      var _a2;
      return ((_a2 = pref.workflow) == null ? void 0 : _a2.identifier) === workflowIdentifier;
    });
    if (!preference) return;
    props.updatePreference(preference)(channels);
  };
  return web.createComponent(solidJs.Show, {
    get when() {
      var _a;
      return (_a = workflowPreferences()) == null ? void 0 : _a.length;
    },
    get fallback() {
      return web.createComponent(PreferencesListSkeleton, {
        get loading() {
          return props.loading;
        }
      });
    },
    get children() {
      return web.createComponent(solidJs.Index, {
        get each() {
          return workflowPreferences();
        },
        children: (preference) => {
          return web.createComponent(PreferencesRow, {
            iconKey: "routeFill",
            get preference() {
              return preference();
            },
            onChange: updatePreference
          });
        }
      });
    }
  });
};
var _tmpl$55 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 14 14"><path fill=currentColor d="M5.95 1.75c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H4.9v1.05h2.625v-.525c0-.29.235-.525.525-.525h3.15c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H8.05a.525.525 0 0 1-.525-.525V7H4.9v3.15h2.625v-.525c0-.29.235-.525.525-.525h3.15c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H8.05a.525.525 0 0 1-.525-.525V11.2h-3.15a.525.525 0 0 1-.525-.525V4.9H2.8a.525.525 0 0 1-.525-.525v-2.1c0-.29.235-.525.525-.525h3.15Zm4.725 8.4h-2.1v1.05h2.1v-1.05Zm0-4.2h-2.1V7h2.1V5.95ZM5.425 2.8h-2.1v1.05h2.1V2.8Z"></path><path fill=url(#a) d="M5.95 1.75c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H4.9v1.05h2.625v-.525c0-.29.235-.525.525-.525h3.15c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H8.05a.525.525 0 0 1-.525-.525V7H4.9v3.15h2.625v-.525c0-.29.235-.525.525-.525h3.15c.29 0 .525.235.525.525v2.1c0 .29-.235.525-.525.525H8.05a.525.525 0 0 1-.525-.525V11.2h-3.15a.525.525 0 0 1-.525-.525V4.9H2.8a.525.525 0 0 1-.525-.525v-2.1c0-.29.235-.525.525-.525h3.15Zm4.725 8.4h-2.1v1.05h2.1v-1.05Zm0-4.2h-2.1V7h2.1V5.95ZM5.425 2.8h-2.1v1.05h2.1V2.8Z"></path><defs><linearGradient id=a x1=2.275 x2=11.725 y1=6.982 y2=7.018 gradientUnits=userSpaceOnUse><stop stop-color=currentColor></stop><stop offset=1 stop-color=currentColor>`);
var NodeTree = (props) => {
  return (() => {
    var _el$ = _tmpl$55();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};
var _tmpl$56 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg fill=none viewBox="0 0 16 16"><path fill=currentColor d="M8 13A5 5 0 1 1 8 3a5 5 0 0 1 0 10Zm0-1a4 4 0 1 0 0-8 4 4 0 0 0 0 8Zm.5-4.75V9.5H9v1H7v-1h.5V8.25H7v-1h1.5ZM8.75 6a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z">`);
var Info = (props) => {
  return (() => {
    var _el$ = _tmpl$56();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
};

// src/ui/components/elements/Preferences/GroupedPreferencesRow.tsx
var _tmpl$57 = /* @__PURE__ */ web.template(`<div><div><span data-localization=preferences.group.info></span></div><div>`);
var _tmpl$215 = /* @__PURE__ */ web.template(`<div><div><div><span></span></div><div><span>`);
var GroupedPreferencesRow = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const [isOpened, setIsOpened] = solidJs.createSignal(false);
  const uniqueChannels = solidJs.createMemo(() => {
    return props.group.preferences.reduce((acc, preference) => {
      Object.keys(preference.channels).forEach((el) => {
        const channel = el;
        const currentState = acc[channel];
        const preferenceState = preference.channels[channel] ? "enabled" : "disabled";
        if (!currentState) {
          acc[channel] = preferenceState;
        } else {
          acc[channel] = currentState !== preferenceState ? "indeterminate" : preferenceState;
        }
      });
      return acc;
    }, {});
  });
  const groupState = solidJs.createMemo(() => {
    const someIndeterminate = Object.values(uniqueChannels()).some((state) => state === "indeterminate");
    if (someIndeterminate) {
      return "indeterminate";
    }
    const allEnabled = Object.values(uniqueChannels()).every((state) => state === "enabled");
    const allDisabled = Object.values(uniqueChannels()).every((state) => state === "disabled");
    if (allEnabled) {
      return "enabled";
    }
    if (allDisabled) {
      return "disabled";
    }
    return "indeterminate";
  });
  const updateGroupPreferences = (newState) => {
    const channels = Object.keys(uniqueChannels()).reduce((acc, channel) => {
      acc[channel] = newState === "enabled";
      return acc;
    }, {});
    props.bulkUpdatePreferences(props.group.preferences)(channels);
  };
  const updatePreference = (workflowIdentifier) => (channels) => {
    const preference = props.group.preferences.find((pref) => {
      var _a;
      return ((_a = pref.workflow) == null ? void 0 : _a.identifier) === workflowIdentifier;
    });
    if (!preference) return;
    props.updatePreference(preference)(channels);
  };
  const updatePreferencesForChannel = (channel) => (channels) => {
    const filteredPreferences = props.group.preferences.filter((preference) => Object.keys(preference.channels).some((key) => key === channel));
    props.bulkUpdatePreferences(filteredPreferences)(channels);
  };
  const preferences = solidJs.createMemo(() => props.group.preferences);
  const nodeTreeIconClass = style("preferencesGroupLabelIcon", "nt-text-foreground-alpha-600 nt-size-3.5");
  const infoIconClass = style("preferencesGroupInfoIcon", "nt-size-4");
  const dropdownIconClass = style("moreTabs__icon", "nt-size-4");
  return web.createComponent(solidJs.Show, {
    get when() {
      return Object.keys(uniqueChannels()).length > 0;
    },
    get children() {
      var _el$ = _tmpl$215(), _el$2 = _el$.firstChild, _el$3 = _el$2.firstChild, _el$4 = _el$3.firstChild, _el$5 = _el$3.nextSibling, _el$6 = _el$5.firstChild;
      _el$2.$$click = () => {
        setIsOpened((prev) => !prev);
      };
      web.insert(_el$3, web.createComponent(IconRendererWrapper, {
        iconKey: "nodeTree",
        "class": nodeTreeIconClass,
        get fallback() {
          return web.createComponent(NodeTree, {
            "class": nodeTreeIconClass
          });
        }
      }), _el$4);
      web.insert(_el$4, () => props.group.name);
      web.insert(_el$5, web.createComponent(Switch, {
        get state() {
          return groupState();
        },
        onChange: updateGroupPreferences
      }), _el$6);
      web.insert(_el$6, web.createComponent(IconRendererWrapper, {
        iconKey: "arrowDropDown",
        "class": dropdownIconClass,
        get fallback() {
          return web.createComponent(ArrowDropDown, {
            "class": dropdownIconClass
          });
        }
      }));
      web.insert(_el$, web.createComponent(Collapsible, {
        get open() {
          return isOpened();
        },
        get children() {
          var _el$7 = _tmpl$57(), _el$8 = _el$7.firstChild, _el$9 = _el$8.firstChild, _el$10 = _el$8.nextSibling;
          web.insert(_el$8, web.createComponent(solidJs.Index, {
            get each() {
              return Object.keys(uniqueChannels());
            },
            children: (channel) => {
              return web.createComponent(ChannelRow, {
                get channel() {
                  return {
                    channel: channel(),
                    state: uniqueChannels()[channel()]
                  };
                },
                get onChange() {
                  return updatePreferencesForChannel(channel());
                }
              });
            }
          }), _el$9);
          web.insert(_el$9, web.createComponent(IconRendererWrapper, {
            iconKey: "info",
            "class": infoIconClass,
            get fallback() {
              return web.createComponent(Info, {
                "class": infoIconClass
              });
            }
          }), null);
          web.insert(_el$9, () => t("preferences.group.info"), null);
          web.insert(_el$10, web.createComponent(solidJs.Index, {
            get each() {
              return preferences();
            },
            children: (preference) => web.createComponent(PreferencesRow, {
              iconKey: "routeFill",
              get preference() {
                return preference();
              },
              onChange: updatePreference
            })
          }));
          web.effect((_p$) => {
            var _v$ = style("preferencesGroupBody", "nt-flex nt-flex-col nt-gap-1 nt-overflow-hidden"), _v$2 = style("preferencesGroupChannels", "nt-flex nt-bg-background nt-border-t nt-border-b nt-border-neutral-alpha-50 nt-p-2 nt-flex-col nt-gap-1 nt-overflow-hidden"), _v$3 = style("preferencesGroupInfo", "nt-text-sm nt-text-start nt-text-foreground-alpha-400 nt-mt-1 nt-flex nt-items-center nt-gap-1"), _v$4 = style("preferencesGroupWorkflows", "nt-flex nt-p-2 nt-flex-col nt-gap-1 nt-overflow-hidden");
            _v$ !== _p$.e && web.className(_el$7, _p$.e = _v$);
            _v$2 !== _p$.t && web.className(_el$8, _p$.t = _v$2);
            _v$3 !== _p$.a && web.className(_el$9, _p$.a = _v$3);
            _v$4 !== _p$.o && web.className(_el$10, _p$.o = _v$4);
            return _p$;
          }, {
            e: void 0,
            t: void 0,
            a: void 0,
            o: void 0
          });
          return _el$7;
        }
      }), null);
      web.effect((_p$) => {
        var _v$5 = style("preferencesGroupContainer", `nt-bg-neutral-alpha-25 nt-rounded-lg nt-border nt-border-neutral-alpha-50`), _v$6 = isOpened(), _v$7 = style("preferencesGroupHeader", "nt-flex nt-justify-between nt-p-2 nt-flex-nowrap nt-self-stretch nt-cursor-pointer nt-items-center nt-overflow-hidden"), _v$8 = style("preferencesGroupLabelContainer", "nt-overflow-hidden nt-flex nt-items-center nt-gap-1"), _v$9 = style("preferencesGroupLabel", "nt-text-sm nt-font-semibold nt-truncate nt-text-start"), _v$10 = isOpened(), _v$11 = style("preferencesGroupActionsContainer", "nt-flex nt-items-center nt-gap-1"), _v$12 = style("preferencesGroupActionsContainerRight__icon", `nt-text-foreground-alpha-600 nt-transition-all nt-duration-200 data-[open=true]:nt-transform data-[open=true]:nt-rotate-180`), _v$13 = isOpened();
        _v$5 !== _p$.e && web.className(_el$, _p$.e = _v$5);
        _v$6 !== _p$.t && web.setAttribute(_el$, "data-open", _p$.t = _v$6);
        _v$7 !== _p$.a && web.className(_el$2, _p$.a = _v$7);
        _v$8 !== _p$.o && web.className(_el$3, _p$.o = _v$8);
        _v$9 !== _p$.i && web.className(_el$4, _p$.i = _v$9);
        _v$10 !== _p$.n && web.setAttribute(_el$4, "data-open", _p$.n = _v$10);
        _v$11 !== _p$.s && web.className(_el$5, _p$.s = _v$11);
        _v$12 !== _p$.h && web.className(_el$6, _p$.h = _v$12);
        _v$13 !== _p$.r && web.setAttribute(_el$6, "data-open", _p$.r = _v$13);
        return _p$;
      }, {
        e: void 0,
        t: void 0,
        a: void 0,
        o: void 0,
        i: void 0,
        n: void 0,
        s: void 0,
        h: void 0,
        r: void 0
      });
      return _el$;
    }
  });
};
web.delegateEvents(["click"]);

// src/ui/components/elements/Preferences/GroupedPreferences.tsx
var GroupedPreferences = (props) => {
  const groups = () => props.groups;
  return web.createComponent(solidJs.Show, {
    get when() {
      return props.groups.length && !props.loading;
    },
    get fallback() {
      return web.createComponent(PreferencesListSkeleton, {
        get loading() {
          return props.loading;
        }
      });
    },
    get children() {
      return web.createComponent(solidJs.Index, {
        get each() {
          return groups();
        },
        children: (group) => {
          return web.createComponent(GroupedPreferencesRow, {
            get group() {
              return group();
            },
            get bulkUpdatePreferences() {
              return props.bulkUpdatePreferences;
            },
            get updatePreference() {
              return props.updatePreference;
            }
          });
        }
      });
    }
  });
};

// src/ui/components/elements/Preferences/Preferences.tsx
var _tmpl$58 = /* @__PURE__ */ web.template(`<div>`);
var Preferences = () => {
  var _a;
  const novu = useNovu();
  const style = useStyle();
  const {
    preferencesFilter,
    preferenceGroups
  } = useInboxContext();
  const {
    preferences,
    loading
  } = usePreferences({
    tags: (_a = preferencesFilter()) == null ? void 0 : _a.tags
  });
  const allPreferences = solidJs.createMemo(() => {
    var _a2, _b;
    const globalPreference = (_a2 = preferences()) == null ? void 0 : _a2.find((preference) => preference.level === "global" /* GLOBAL */);
    const workflowPreferences = (_b = preferences()) == null ? void 0 : _b.filter((preference) => preference.level === "template" /* TEMPLATE */);
    return {
      globalPreference,
      workflowPreferences
    };
  });
  solidJs.createEffect(() => {
    setDynamicLocalization((prev) => {
      var _a2;
      return chunk7B52C2XE_js.__spreadValues(chunk7B52C2XE_js.__spreadValues({}, prev), (_a2 = allPreferences().workflowPreferences) == null ? void 0 : _a2.reduce((acc, preference) => {
        acc[preference.workflow.identifier] = preference.workflow.name;
        return acc;
      }, {}));
    });
  });
  const updatePreference = (preference) => (channels) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    yield preference == null ? void 0 : preference.update({
      channels
    });
  });
  const bulkUpdatePreferences = (preferences2) => (channels) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    yield novu.preferences.bulkUpdate(preferences2.map((el) => {
      const oldChannels = Object.keys(el.channels);
      const channelsToUpdate = Object.keys(channels).filter((channel) => oldChannels.includes(channel)).reduce((acc, channel) => {
        acc[channel] = channels[channel];
        return acc;
      }, {});
      return {
        preference: el,
        channels: channelsToUpdate
      };
    }));
  });
  const groupedPreferences = solidJs.createMemo(() => {
    var _a2, _b, _c;
    const workflowPreferences = (_a2 = allPreferences().workflowPreferences) != null ? _a2 : [];
    return (_c = (_b = preferenceGroups()) == null ? void 0 : _b.map((group) => {
      const {
        filter
      } = group;
      if (typeof filter === "function") {
        const preferences2 = filter({
          preferences: workflowPreferences
        });
        return {
          name: group.name,
          preferences: preferences2
        };
      }
      if (typeof filter === "object") {
        return {
          name: group.name,
          preferences: workflowPreferences.filter((preference) => {
            var _a3, _b2, _c2, _d;
            const workflowId = ((_a3 = preference.workflow) == null ? void 0 : _a3.id) || ((_b2 = preference.workflow) == null ? void 0 : _b2.identifier);
            return ((_c2 = filter.workflowIds) == null ? void 0 : _c2.includes(workflowId != null ? workflowId : "")) || ((_d = filter.tags) == null ? void 0 : _d.some((tag) => {
              var _a4, _b3;
              return (_b3 = (_a4 = preference.workflow) == null ? void 0 : _a4.tags) == null ? void 0 : _b3.includes(tag);
            }));
          })
        };
      }
      return {
        name: group.name,
        preferences: []
      };
    })) != null ? _c : [];
  });
  return (() => {
    var _el$ = _tmpl$58();
    web.insert(_el$, web.createComponent(PreferencesRow, {
      iconKey: "cogs",
      get preference() {
        return allPreferences().globalPreference;
      },
      onChange: () => updatePreference(allPreferences().globalPreference)
    }), null);
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return groupedPreferences().length > 0;
      },
      get fallback() {
        return web.createComponent(solidJs.Show, {
          get when() {
            var _a2;
            return (_a2 = allPreferences().workflowPreferences) == null ? void 0 : _a2.length;
          },
          get fallback() {
            return web.createComponent(PreferencesListSkeleton, {
              get loading() {
                return loading();
              }
            });
          },
          get children() {
            return web.createComponent(DefaultPreferences, {
              get workflowPreferences() {
                return allPreferences().workflowPreferences;
              },
              get loading() {
                return loading();
              },
              updatePreference
            });
          }
        });
      },
      get children() {
        return web.createComponent(GroupedPreferences, {
          get groups() {
            return groupedPreferences();
          },
          get loading() {
            return loading();
          },
          updatePreference,
          bulkUpdatePreferences
        });
      }
    }), null);
    web.effect(() => web.className(_el$, style("preferencesContainer", "nt-px-3 nt-py-4 nt-flex nt-flex-col nt-gap-1 nt-overflow-y-auto nt-h-full nt-pr-0 [scrollbar-gutter:stable]")));
    return _el$;
  })();
};
var _tmpl$59 = /* @__PURE__ */ web.template(`<div><div data-localization=preferences.title>`);
var PreferencesHeader = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const arrowLeftIconClass = style("preferencesHeader__back__button__icon", "nt-size-4", {
    iconKey: "arrowLeft"
  });
  return (() => {
    var _el$ = _tmpl$59(), _el$2 = _el$.firstChild;
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return props.navigateToNotifications;
      },
      children: (navigateToNotifications) => web.createComponent(Button, {
        appearanceKey: "preferencesHeader__back__button",
        "class": "nt-text-foreground-alpha-600",
        variant: "unstyled",
        size: "none",
        get onClick() {
          return navigateToNotifications();
        },
        get children() {
          return web.createComponent(IconRendererWrapper, {
            iconKey: "arrowLeft",
            "class": arrowLeftIconClass,
            get fallback() {
              return web.createComponent(ArrowLeft, {
                "class": arrowLeftIconClass
              });
            }
          });
        }
      })
    }), _el$2);
    web.insert(_el$2, () => t("preferences.title"));
    web.effect((_p$) => {
      var _v$ = style("preferencesHeader", "nt-flex nt-bg-neutral-alpha-25 nt-shrink-0 nt-border-b nt-border-border nt-items-center nt-py-3.5 nt-px-4 nt-gap-2"), _v$2 = style("preferencesHeader__title", "nt-text-base nt-font-medium");
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$2, _p$.t = _v$2);
      return _p$;
    }, {
      e: void 0,
      t: void 0
    });
    return _el$;
  })();
};
var useTabsDropdown = ({ tabs }) => {
  const [tabsList, setTabsList] = solidJs.createSignal();
  const [visibleTabs, setVisibleTabs] = solidJs.createSignal([]);
  const [dropdownTabs, setDropdownTabs] = solidJs.createSignal([]);
  solidJs.onMount(() => {
    const tabsListEl = tabsList();
    if (!tabsListEl) return;
    const tabsElements = [...tabsListEl.querySelectorAll('[role="tab"]')];
    const observer = new IntersectionObserver(
      (entries) => {
        let visibleTabIds = entries.filter((entry) => entry.isIntersecting && entry.intersectionRatio === 1).map((entry) => entry.target.id);
        if (tabsElements.length === visibleTabIds.length) {
          setVisibleTabs(tabs.filter((tab) => visibleTabIds.includes(tab.label)));
          observer.disconnect();
          return;
        }
        visibleTabIds = visibleTabIds.slice(0, -1);
        setVisibleTabs(tabs.filter((tab) => visibleTabIds.includes(tab.label)));
        setDropdownTabs(tabs.filter((tab) => !visibleTabIds.includes(tab.label)));
        observer.disconnect();
      },
      { root: tabsListEl }
    );
    for (const tabElement of tabsElements) {
      observer.observe(tabElement);
    }
  });
  return { dropdownTabs, setTabsList, visibleTabs };
};
var _tmpl$60 = /* @__PURE__ */ web.template(`<strong>`);
var _tmpl$216 = /* @__PURE__ */ web.template(`<p>`);
var Bold = (props) => {
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$60();
    web.insert(_el$, () => props.children);
    web.effect(() => web.className(_el$, style(props.appearanceKey || "strong", "nt-font-semibold")));
    return _el$;
  })();
};
var Text = (props) => props.children;
var Markdown = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "children", "appearanceKey", "strongAppearanceKey"]);
  const style = useStyle();
  const tokens = solidJs.createMemo(() => chunkERC62PGI_js.parseMarkdownIntoTokens(local.children));
  return (() => {
    var _el$2 = _tmpl$216();
    web.spread(_el$2, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey, cn(local.class));
      }
    }, rest), false, true);
    web.insert(_el$2, web.createComponent(solidJs.For, {
      get each() {
        return tokens();
      },
      children: (token) => {
        if (token.type === "bold") {
          return web.createComponent(Bold, {
            get appearanceKey() {
              return local.strongAppearanceKey;
            },
            get children() {
              return token.content;
            }
          });
        } else {
          return web.createComponent(Text, {
            get children() {
              return token.content;
            }
          });
        }
      }
    }));
    return _el$2;
  })();
};
var Markdown_default = Markdown;
var _tmpl$61 = /* @__PURE__ */ web.template(`<span>`);
var badgeVariants = classVarianceAuthority.cva(cn("nt-inline-flex nt-flex-row nt-gap-1 nt-items-center"), {
  variants: {
    variant: {
      secondary: "nt-bg-neutral-alpha-50"
    },
    size: {
      default: "nt-px-1 nt-py-px nt-rounded-sm nt-text-xs nt-px-1"
    }
  },
  defaultVariants: {
    variant: "secondary",
    size: "default"
  }
});
var Badge = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey"]);
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$61();
    web.spread(_el$, web.mergeProps({
      get ["data-variant"]() {
        return props.variant;
      },
      get ["data-size"]() {
        return props.size;
      },
      get ["class"]() {
        return style(local.appearanceKey || "badge", cn(badgeVariants({
          variant: props.variant,
          size: props.size
        }), local.class));
      }
    }, rest), false, false);
    return _el$;
  })();
};
var _tmpl$63 = /* @__PURE__ */ web.template(`<input>`);
var inputVariants = classVarianceAuthority.cva(cn(`focus-visible:nt-outline-none focus-visible:nt-ring-2 focus-visible:nt-rounded-md focus-visible:nt-ring-ring focus-visible:nt-ring-offset-2`), {
  variants: {
    variant: {
      default: "nt-border nt-border-neutral-200 nt-rounded-md nt-p-1 nt-bg-background"
    },
    size: {
      default: "nt-h-9",
      sm: "nt-h-8 nt-text-sm"
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default"
  }
});
var Input = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["class", "appearanceKey"]);
  const style = useStyle();
  return (() => {
    var _el$ = _tmpl$63();
    web.spread(_el$, web.mergeProps({
      get ["data-variant"]() {
        return props.variant;
      },
      get ["data-size"]() {
        return props.size;
      },
      get ["class"]() {
        return style(local.appearanceKey || "input", cn(inputVariants({
          variant: props.variant,
          size: props.size
        }), local.class));
      }
    }, rest), false, false);
    return _el$;
  })();
};

// src/ui/components/primitives/TimePicker.tsx
var _tmpl$64 = /* @__PURE__ */ web.template(`<div><span>:</span><select><option value=AM>AM</option><option value=PM>PM`);
var TimePicker = (props) => {
  const [local, rest] = solidJs.splitProps(props, ["value", "onChange", "class", "appearanceKey"]);
  const style = useStyle();
  const initialValue = local.value || {
    hour: 12,
    minute: 0,
    isPM: true
  };
  const [hour, setHour] = solidJs.createSignal(initialValue.hour);
  const [minute, setMinute] = solidJs.createSignal(initialValue.minute);
  const [isPM, setIsPM] = solidJs.createSignal(initialValue.isPM);
  const notifyChange = () => {
    if (local.onChange) {
      local.onChange({
        hour: hour(),
        minute: minute(),
        isPM: isPM()
      });
    }
  };
  const handleHourChange = (newHour) => {
    setHour(newHour);
    notifyChange();
  };
  const handleMinuteChange = (newMinute) => {
    setMinute(newMinute);
    notifyChange();
  };
  const handlePeriodChange = (newIsPM) => {
    setIsPM(newIsPM);
    notifyChange();
  };
  const handleKeyDown = (e) => {
    const allowedKeys = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "Backspace", "Delete", "Tab"];
    if (!allowedKeys.includes(e.key)) {
      e.preventDefault();
    }
  };
  return (() => {
    var _el$ = _tmpl$64(), _el$2 = _el$.firstChild, _el$3 = _el$2.nextSibling;
    _el$.$$click = (e) => e.stopPropagation();
    web.spread(_el$, web.mergeProps({
      get ["class"]() {
        return style(local.appearanceKey || "timePicker", cn("nt-flex nt-items-center nt-gap-1", local.class));
      }
    }, rest), false, true);
    web.insert(_el$, web.createComponent(Input, {
      size: "sm",
      type: "number",
      min: "1",
      max: "12",
      onKeyDown: (e) => {
        e.stopPropagation();
        handleKeyDown(e);
      },
      get value() {
        return hour().toString();
      },
      onInput: (e) => {
        e.stopPropagation();
        enforceMinMax(e.currentTarget);
        handleHourChange(Number(e.currentTarget.value));
      },
      get ["class"]() {
        return style("timePickerHour__input", "nt-flex nt-font-mono nt-justify-center nt-items-center nt-text-center nt-h-7 nt-w-[calc(2ch+2rem)] nt-px-2");
      }
    }), _el$2);
    web.insert(_el$, web.createComponent(Input, {
      size: "sm",
      type: "number",
      min: "0",
      max: "59",
      onKeyDown: (e) => {
        e.stopPropagation();
        handleKeyDown(e);
      },
      get value() {
        return minute().toString().padStart(2, "0");
      },
      onInput: (e) => {
        e.stopPropagation();
        enforceMinMax(e.currentTarget);
        handleMinuteChange(Number(e.currentTarget.value));
      },
      get ["class"]() {
        return style("timePickerHour__input", "nt-flex nt-font-mono nt-justify-center nt-items-center nt-text-center nt-h-7 nt-w-[calc(2ch+2rem)] nt-px-2");
      }
    }), _el$3);
    _el$3.addEventListener("change", (e) => {
      e.stopPropagation();
      handlePeriodChange(e.target.value === "PM");
    });
    _el$3.$$click = (e) => e.stopPropagation();
    web.effect((_p$) => {
      var _v$ = style("timePicker__separator", "nt-text-xl"), _v$2 = style("timePicker__periodSelect", `${inputVariants({
        size: "sm"
      })} nt-h-7 nt-font-mono`);
      _v$ !== _p$.e && web.className(_el$2, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$3, _p$.t = _v$2);
      return _p$;
    }, {
      e: void 0,
      t: void 0
    });
    web.effect(() => _el$3.value = isPM() ? "PM" : "AM");
    return _el$;
  })();
};
var enforceMinMax = (el) => {
  if (el.value !== "") {
    const value = parseInt(el.value, 10);
    const min = parseInt(el.min, 10);
    const max = parseInt(el.max, 10);
    if (value < min || value > max) {
      el.value = el.value.slice(0, -1);
      const newValue = parseInt(el.value, 10);
      if (Number.isNaN(newValue) || newValue < min) {
        el.value = el.min;
      } else if (newValue > max) {
        el.value = el.max;
      }
    }
  }
};
web.delegateEvents(["click"]);

// src/ui/components/Notification/SnoozeDateTimePicker.tsx
var _tmpl$65 = /* @__PURE__ */ web.template(`<div><div><p></p></div><div>`);
var fiveMinutesFromNow = () => {
  const futureTime = new Date(Date.now() + 5 * 60 * 1e3);
  const hours = futureTime.getHours();
  const isPM = hours >= 12;
  let hour;
  if (hours === 0) {
    hour = 12;
  } else if (hours === 12) {
    hour = 12;
  } else {
    hour = hours % 12;
  }
  return {
    hour,
    minute: futureTime.getMinutes(),
    isPM
  };
};
var convertTo24Hour = (time) => {
  if (time.isPM) {
    return time.hour === 12 ? 12 : time.hour + 12;
  } else {
    return time.hour === 12 ? 0 : time.hour;
  }
};
var REFRESH_INTERVAL = 5e3;
var SnoozeDateTimePicker = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const [selectedDate, setSelectedDate] = solidJs.createSignal(null);
  const [timeValue, setTimeValue] = solidJs.createSignal(fiveMinutesFromNow());
  const [currentTime, setCurrentTime] = solidJs.createSignal(/* @__PURE__ */ new Date());
  solidJs.createEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(/* @__PURE__ */ new Date());
    }, REFRESH_INTERVAL);
    solidJs.onCleanup(() => clearInterval(interval));
  });
  const onDateTimeSelect = () => {
    var _a;
    if (selectedDate() && timeValue()) {
      const date = new Date(selectedDate());
      const hours = convertTo24Hour(timeValue());
      date.setHours(hours, timeValue().minute, 0, 0);
      (_a = props.onSelect) == null ? void 0 : _a.call(props, date);
    }
  };
  const maxDays = () => {
    if (!props.maxDurationHours) return 0;
    return Math.ceil(props.maxDurationHours / 24);
  };
  const getSelectedDateTime = () => {
    if (!selectedDate() || !timeValue()) return null;
    const date = new Date(selectedDate());
    const hours = convertTo24Hour(timeValue());
    date.setHours(hours, timeValue().minute, 0, 0);
    return date;
  };
  const isTimeInPast = solidJs.createMemo(() => {
    const dateTime = getSelectedDateTime();
    if (!dateTime) return false;
    const minAllowedDateTime = new Date(currentTime().getTime() + 3 * 60 * 1e3);
    return dateTime < minAllowedDateTime;
  });
  const isTimeExceedingMaxDuration = solidJs.createMemo(() => {
    const dateTime = getSelectedDateTime();
    if (!dateTime || !props.maxDurationHours) return false;
    const maxAllowedDateTime = new Date(currentTime().getTime() + props.maxDurationHours * 60 * 60 * 1e3);
    return dateTime > maxAllowedDateTime;
  });
  const applyButtonEnabled = solidJs.createMemo(() => {
    if (!selectedDate() || !timeValue()) {
      return false;
    }
    if (isTimeInPast()) {
      return false;
    }
    if (props.maxDurationHours && isTimeExceedingMaxDuration()) {
      return false;
    }
    return true;
  });
  const getTooltipMessage = solidJs.createMemo(() => {
    if (isTimeInPast()) {
      return t("snooze.datePicker.pastDateTooltip");
    }
    if (isTimeExceedingMaxDuration()) {
      return t("snooze.datePicker.exceedingLimitTooltip", {
        days: maxDays()
      });
    }
    return t("snooze.datePicker.noDateSelectedTooltip");
  });
  return (() => {
    var _el$ = _tmpl$65(), _el$2 = _el$.firstChild, _el$3 = _el$2.firstChild, _el$4 = _el$2.nextSibling;
    _el$.$$click = (e) => e.stopPropagation();
    web.insert(_el$, web.createComponent(DatePicker, {
      onDateChange: (date) => setSelectedDate(date),
      get maxDays() {
        return maxDays();
      },
      get children() {
        return [web.createComponent(DatePickerHeader, {}), web.createComponent(DatePickerCalendar, {})];
      }
    }), _el$2);
    web.insert(_el$3, () => t("snooze.datePicker.timePickerLabel"));
    web.insert(_el$2, web.createComponent(TimePicker, {
      get value() {
        return timeValue();
      },
      onChange: setTimeValue
    }), null);
    web.insert(_el$4, web.createComponent(Button, {
      appearanceKey: "snoozeDatePickerCancel__button",
      variant: "secondary",
      "class": "nt-h-7 nt-w-[60px] nt-px-2",
      get onClick() {
        return props.onCancel;
      },
      get children() {
        return t("snooze.datePicker.cancel");
      }
    }), null);
    web.insert(_el$4, web.createComponent(solidJs.Show, {
      get when() {
        return applyButtonEnabled();
      },
      get fallback() {
        return web.createComponent(Tooltip.Root, {
          get children() {
            return [web.createComponent(Tooltip.Trigger, {
              asChild: (props2) => web.createComponent(Button, web.mergeProps({
                appearanceKey: "snoozeDatePickerApply__button",
                "class": "nt-h-7 nt-w-[60px] nt-px-2 !nt-pointer-events-auto",
                onClick: onDateTimeSelect,
                disabled: true
              }, props2, {
                get children() {
                  return t("snooze.datePicker.apply");
                }
              }))
            }), web.createComponent(Tooltip.Content, {
              get children() {
                return getTooltipMessage();
              }
            })];
          }
        });
      },
      get children() {
        return web.createComponent(Button, {
          appearanceKey: "snoozeDatePickerApply__button",
          "class": "nt-h-7 nt-w-[60px] nt-px-2",
          onClick: onDateTimeSelect,
          get children() {
            return t("snooze.datePicker.apply");
          }
        });
      }
    }), null);
    web.effect((_p$) => {
      var _v$ = style("snoozeDatePicker", "nt-bg-background nt-rounded-md nt-shadow-lg nt-w-[260px]"), _v$2 = style("snoozeDatePicker__timePickerContainer", "nt-flex nt-flex-row nt-justify-between nt-p-2 nt-items-center nt-border-t nt-border-neutral-200 nt-border-b"), _v$3 = style("snoozeDatePicker__timePickerLabel", "nt-text-sm nt-font-medium nt-text-foreground-alpha-700 nt-p-2"), _v$4 = style("snoozeDatePicker__actions", "nt-flex nt-flex-row nt-justify-end nt-gap-2 nt-p-2");
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$2, _p$.t = _v$2);
      _v$3 !== _p$.a && web.className(_el$3, _p$.a = _v$3);
      _v$4 !== _p$.o && web.className(_el$4, _p$.o = _v$4);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0,
      o: void 0
    });
    return _el$;
  })();
};
web.delegateEvents(["click"]);

// src/ui/components/Notification/NotificationActions.tsx
var _tmpl$66 = /* @__PURE__ */ web.template(`<div><span>`);
var _tmpl$217 = /* @__PURE__ */ web.template(`<span>`);
var SNOOZE_PRESETS = [{
  key: "snooze.options.anHourFromNow",
  hours: 1,
  getDate: () => new Date(Date.now() + 1 * 60 * 60 * 1e3)
}, {
  key: "snooze.options.inOneDay",
  hours: 24,
  getDate: () => {
    const date = new Date(Date.now() + 1 * 24 * 60 * 60 * 1e3);
    date.setHours(9, 0, 0, 0);
    return date;
  }
}, {
  key: "snooze.options.inOneWeek",
  hours: 168,
  getDate: () => {
    const date = new Date(Date.now() + 7 * 24 * 60 * 60 * 1e3);
    date.setHours(9, 0, 0, 0);
    return date;
  }
}];
var formatSnoozeOption = (preset, t, locale) => {
  const date = preset.getDate();
  const dayName = new Intl.DateTimeFormat(locale, {
    weekday: "short"
  }).format(date);
  const dateMonth = new Intl.DateTimeFormat(locale, {
    day: "numeric",
    month: "short"
  }).format(date);
  const timeString = new Intl.DateTimeFormat(locale, {
    hour: "numeric",
    minute: "numeric"
  }).format(date);
  return {
    label: t(preset.key),
    time: `${dayName}, ${dateMonth}, ${timeString}`
  };
};
var SnoozeDropdownItem = (props) => {
  const style = useStyle();
  const snoozeItemIconClass = style("notificationSnooze__dropdownItem__icon", "nt-size-3 nt-text-foreground-alpha-400 nt-mr-2", {
    iconKey: "clock"
  });
  const content = [(() => {
    var _el$ = _tmpl$66(), _el$2 = _el$.firstChild;
    web.insert(_el$, web.createComponent(IconRendererWrapper, {
      iconKey: "clock",
      "class": snoozeItemIconClass,
      get fallback() {
        return web.createComponent(Clock, {
          "class": snoozeItemIconClass
        });
      }
    }), _el$2);
    web.insert(_el$2, () => props.label);
    web.effect((_p$) => {
      var _v$ = style("dropdownItem", "nt-flex nt-items-center nt-flex-1"), _v$2 = style("dropdownItemLabel");
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$2, _p$.t = _v$2);
      return _p$;
    }, {
      e: void 0,
      t: void 0
    });
    return _el$;
  })(), (() => {
    var _el$3 = _tmpl$217();
    web.insert(_el$3, () => props.time);
    web.effect(() => web.className(_el$3, style("dropdownItemRight__icon", "nt-text-foreground-alpha-300 nt-ml-2 nt-text-xs")));
    return _el$3;
  })()];
  if (props.asChild) {
    return props.asChild({
      class: style("notificationSnooze__dropdownItem", dropdownItemVariants()),
      onClick: props.onClick,
      children: content
    });
  }
  return web.createComponent(Dropdown.Item, {
    appearanceKey: "notificationSnooze__dropdownItem",
    get onClick() {
      return props.onClick;
    },
    get ["class"]() {
      return style("dropdownItem", "nt-justify-between");
    },
    children: content
  });
};
var ReadButton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const readIconClass = style("notificationRead__icon", "nt-size-3", {
    iconKey: "markAsRead"
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (childProps) => web.createComponent(Button, web.mergeProps({
          appearanceKey: "notificationRead__button",
          size: "iconSm",
          variant: "ghost"
        }, childProps, {
          onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
            e.stopPropagation();
            yield props.notification.read();
          }),
          get children() {
            return web.createComponent(IconRendererWrapper, {
              iconKey: "markAsRead",
              "class": readIconClass,
              get fallback() {
                return web.createComponent(MarkAsRead, {
                  "class": readIconClass
                });
              }
            });
          }
        }))
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.read.tooltip",
        get children() {
          return t("notification.actions.read.tooltip");
        }
      })];
    }
  });
};
var UnreadButton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const unreadIconClass = style("notificationUnread__icon", "nt-size-3", {
    iconKey: "markAsUnread"
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (childProps) => web.createComponent(Button, web.mergeProps({
          appearanceKey: "notificationUnread__button",
          size: "iconSm",
          variant: "ghost"
        }, childProps, {
          onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
            e.stopPropagation();
            yield props.notification.unread();
          }),
          get children() {
            return web.createComponent(IconRendererWrapper, {
              iconKey: "markAsUnread",
              "class": unreadIconClass,
              get fallback() {
                return web.createComponent(MarkAsUnread, {
                  "class": unreadIconClass
                });
              }
            });
          }
        }))
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.unread.tooltip",
        get children() {
          return t("notification.actions.unread.tooltip");
        }
      })];
    }
  });
};
var ArchiveButton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const archiveIconClass = style("notificationArchive__icon", "nt-size-3", {
    iconKey: "markAsArchived"
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (childProps) => web.createComponent(Button, web.mergeProps({
          appearanceKey: "notificationArchive__button",
          size: "iconSm",
          variant: "ghost"
        }, childProps, {
          onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
            e.stopPropagation();
            yield props.notification.archive();
          }),
          get children() {
            return web.createComponent(IconRendererWrapper, {
              iconKey: "markAsArchived",
              "class": archiveIconClass,
              get fallback() {
                return web.createComponent(MarkAsArchived, {
                  "class": archiveIconClass
                });
              }
            });
          }
        }))
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.archive.tooltip",
        get children() {
          return t("notification.actions.archive.tooltip");
        }
      })];
    }
  });
};
var UnarchiveButton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const unarchiveIconClass = style("notificationArchive__icon", "nt-size-3", {
    iconKey: "markAsUnarchived"
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (childProps) => web.createComponent(Button, web.mergeProps({
          appearanceKey: "notificationUnarchive__button",
          size: "iconSm",
          variant: "ghost"
        }, childProps, {
          onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
            e.stopPropagation();
            yield props.notification.unarchive();
          }),
          get children() {
            return web.createComponent(IconRendererWrapper, {
              iconKey: "markAsUnarchived",
              "class": unarchiveIconClass,
              get fallback() {
                return web.createComponent(MarkAsUnarchived, {
                  "class": unarchiveIconClass
                });
              }
            });
          }
        }))
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.unarchive.tooltip",
        get children() {
          return t("notification.actions.unarchive.tooltip");
        }
      })];
    }
  });
};
var UnsnoozeButton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const unsnoozeIconClass = style("notificationUnsnooze__icon", "nt-size-3", {
    iconKey: "unsnooze"
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (childProps) => web.createComponent(Button, web.mergeProps({
          appearanceKey: "notificationUnsnooze__button",
          size: "iconSm",
          variant: "ghost"
        }, childProps, {
          onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
            e.stopPropagation();
            yield props.notification.unsnooze();
          }),
          get children() {
            return web.createComponent(IconRendererWrapper, {
              iconKey: "unsnooze",
              "class": unsnoozeIconClass,
              get fallback() {
                return web.createComponent(Unsnooze, {
                  "class": unsnoozeIconClass
                });
              }
            });
          }
        }))
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.unsnooze.tooltip",
        get children() {
          return t("notification.actions.unsnooze.tooltip");
        }
      })];
    }
  });
};
var SnoozeButton = (props) => {
  const style = useStyle();
  const {
    t,
    locale
  } = useLocalization();
  const {
    maxSnoozeDurationHours
  } = useInboxContext();
  const [isSnoozeDateTimePickerOpen, setIsSnoozeDateTimePickerOpen] = solidJs.createSignal(false);
  const snoozeButtonIconClass = style("notificationSnooze__icon", "nt-size-3", {
    iconKey: "clock"
  });
  const availableSnoozePresets = solidJs.createMemo(() => {
    if (!maxSnoozeDurationHours()) return SNOOZE_PRESETS;
    return SNOOZE_PRESETS.filter((preset) => preset.hours <= maxSnoozeDurationHours());
  });
  return web.createComponent(Tooltip.Root, {
    get children() {
      return [web.createComponent(Tooltip.Trigger, {
        asChild: (tooltipProps) => web.createComponent(Dropdown.Root, {
          get children() {
            return [web.createComponent(Dropdown.Trigger, web.mergeProps(tooltipProps, {
              asChild: (popoverProps) => web.createComponent(Button, web.mergeProps({
                appearanceKey: "notificationSnooze__button",
                size: "iconSm",
                variant: "ghost"
              }, popoverProps, {
                onClick: (e) => {
                  var _a;
                  e.stopPropagation();
                  (_a = popoverProps.onClick) == null ? void 0 : _a.call(popoverProps, e);
                },
                get children() {
                  return web.createComponent(IconRendererWrapper, {
                    iconKey: "clock",
                    "class": snoozeButtonIconClass,
                    get fallback() {
                      return web.createComponent(Clock, {
                        "class": snoozeButtonIconClass
                      });
                    }
                  });
                }
              }))
            })), web.createComponent(Dropdown.Content, {
              portal: true,
              appearanceKey: "notificationSnooze__dropdownContent",
              get children() {
                return [web.createComponent(solidJs.For, {
                  get each() {
                    return availableSnoozePresets();
                  },
                  children: (preset) => {
                    const option = formatSnoozeOption(preset, t, locale());
                    return web.createComponent(SnoozeDropdownItem, {
                      get label() {
                        return option.label;
                      },
                      get time() {
                        return option.time;
                      },
                      onClick: (e) => chunk7B52C2XE_js.__async(this, null, function* () {
                        e.stopPropagation();
                        yield props.notification.snooze(preset.getDate().toISOString());
                      })
                    });
                  }
                }), web.createComponent(Popover.Root, {
                  get open() {
                    return isSnoozeDateTimePickerOpen();
                  },
                  onOpenChange: setIsSnoozeDateTimePickerOpen,
                  placement: "bottom-start",
                  get children() {
                    return [web.createComponent(SnoozeDropdownItem, {
                      get label() {
                        return t("snooze.options.customTime");
                      },
                      time: "",
                      asChild: (childProps) => web.createComponent(Popover.Trigger, web.mergeProps(childProps, {
                        onClick: (e) => {
                          var _a;
                          e.stopPropagation();
                          (_a = childProps.onClick) == null ? void 0 : _a.call(childProps, e);
                        }
                      }))
                    }), web.createComponent(Popover.Content, {
                      portal: true,
                      get ["class"]() {
                        return style("notificationSnoozeCustomTime_popoverContent", "nt-size-fit nt-w-[260px]");
                      },
                      get children() {
                        return web.createComponent(SnoozeDateTimePicker, {
                          get maxDurationHours() {
                            return maxSnoozeDurationHours();
                          },
                          onSelect: (date) => chunk7B52C2XE_js.__async(this, null, function* () {
                            yield props.notification.snooze(date.toISOString());
                          }),
                          onCancel: () => {
                            setIsSnoozeDateTimePickerOpen(false);
                          }
                        });
                      }
                    })];
                  }
                })];
              }
            })];
          }
        })
      }), web.createComponent(Tooltip.Content, {
        "data-localization": "notification.actions.snooze.tooltip",
        get children() {
          return t("notification.actions.snooze.tooltip");
        }
      })];
    }
  });
};
var renderNotificationActions = (notification, status) => {
  const {
    isSnoozeEnabled
  } = useInboxContext();
  if (notification.isSnoozed) {
    return web.createComponent(UnsnoozeButton, {
      notification
    });
  }
  if (notification.isArchived) {
    return web.createComponent(UnarchiveButton, {
      notification
    });
  }
  return [web.memo(() => web.memo(() => status() !== "archived" /* ARCHIVED */)() && (notification.isRead ? web.createComponent(UnreadButton, {
    notification
  }) : web.createComponent(ReadButton, {
    notification
  }))), web.memo(() => web.memo(() => !!isSnoozeEnabled())() && web.createComponent(SnoozeButton, {
    notification
  })), web.createComponent(ArchiveButton, {
    notification
  })];
};

// src/ui/components/Notification/DefaultNotification.tsx
var _tmpl$67 = /* @__PURE__ */ web.template(`<img>`);
var _tmpl$218 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$311 = /* @__PURE__ */ web.template(`<span>`);
var _tmpl$410 = /* @__PURE__ */ web.template(`<a><div><div></div><div></div><div>`);
var DefaultNotification = (props) => {
  const style = useStyle();
  const {
    t,
    locale
  } = useLocalization();
  const {
    navigate,
    status
  } = useInboxContext();
  const [minutesPassed, setMinutesPassed] = solidJs.createSignal(0);
  const deliveredAtIconClass = style("notificationDeliveredAt__icon", "nt-size-3", {
    iconKey: "clock"
  });
  const snoozedUntilIconClass = style("notificationSnoozedUntil__icon", "nt-size-3", {
    iconKey: "clock"
  });
  const createdAt = solidJs.createMemo(() => {
    minutesPassed();
    return formatToRelativeTime({
      fromDate: new Date(props.notification.createdAt),
      locale: locale()
    });
  });
  const snoozedUntil = solidJs.createMemo(() => {
    minutesPassed();
    if (!props.notification.snoozedUntil) {
      return null;
    }
    return formatSnoozedUntil({
      untilDate: new Date(props.notification.snoozedUntil),
      locale: locale()
    });
  });
  const deliveredAt = solidJs.createMemo(() => {
    minutesPassed();
    if (!props.notification.deliveredAt || !Array.isArray(props.notification.deliveredAt)) {
      return null;
    }
    return props.notification.deliveredAt.map((date) => formatToRelativeTime({
      fromDate: new Date(date),
      locale: locale()
    }));
  });
  solidJs.createEffect(() => {
    const interval = setInterval(() => {
      setMinutesPassed((prev) => prev + 1);
    }, 1e3 * 60);
    return () => clearInterval(interval);
  });
  const handleNotificationClick = (e) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    var _a, _b, _c;
    e.stopPropagation();
    e.preventDefault();
    if (!props.notification.isRead) {
      yield props.notification.read();
    }
    (_a = props.onNotificationClick) == null ? void 0 : _a.call(props, props.notification);
    navigate((_b = props.notification.redirect) == null ? void 0 : _b.url, (_c = props.notification.redirect) == null ? void 0 : _c.target);
  });
  const handleActionButtonClick = (action, e) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
    e.stopPropagation();
    if (action === "primary" /* PRIMARY */) {
      yield props.notification.completePrimary();
      (_a = props.onPrimaryActionClick) == null ? void 0 : _a.call(props, props.notification);
      navigate((_c = (_b = props.notification.primaryAction) == null ? void 0 : _b.redirect) == null ? void 0 : _c.url, (_e = (_d = props.notification.primaryAction) == null ? void 0 : _d.redirect) == null ? void 0 : _e.target);
    } else {
      yield props.notification.completeSecondary();
      (_f = props.onSecondaryActionClick) == null ? void 0 : _f.call(props, props.notification);
      navigate((_h = (_g = props.notification.secondaryAction) == null ? void 0 : _g.redirect) == null ? void 0 : _h.url, (_j = (_i = props.notification.secondaryAction) == null ? void 0 : _i.redirect) == null ? void 0 : _j.target);
    }
  });
  return (() => {
    var _el$ = _tmpl$410(), _el$3 = _el$.firstChild, _el$4 = _el$3.firstChild, _el$5 = _el$4.nextSibling, _el$7 = _el$5.nextSibling;
    _el$.$$click = handleNotificationClick;
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return props.notification.avatar;
      },
      get fallback() {
        return (() => {
          var _el$9 = _tmpl$218();
          web.effect(() => web.className(_el$9, style("notificationImageLoadingFallback", "nt-size-8 nt-rounded-lg nt-shrink-0 nt-aspect-square")));
          return _el$9;
        })();
      },
      get children() {
        var _el$2 = _tmpl$67();
        web.effect((_p$) => {
          var _v$ = style("notificationImage", "nt-size-8 nt-rounded-lg nt-object-cover nt-aspect-square"), _v$2 = props.notification.avatar;
          _v$ !== _p$.e && web.className(_el$2, _p$.e = _v$);
          _v$2 !== _p$.t && web.setAttribute(_el$2, "src", _p$.t = _v$2);
          return _p$;
        }, {
          e: void 0,
          t: void 0
        });
        return _el$2;
      }
    }), _el$3);
    web.insert(_el$4, web.createComponent(solidJs.Show, {
      get when() {
        return props.renderSubject;
      },
      get fallback() {
        return web.createComponent(solidJs.Show, {
          get when() {
            return props.notification.subject;
          },
          children: (subject) => web.createComponent(Markdown_default, {
            appearanceKey: "notificationSubject",
            "class": "nt-text-start nt-font-medium nt-whitespace-pre-wrap [word-break:break-word]",
            strongAppearanceKey: "notificationSubject__strong",
            get children() {
              return subject();
            }
          })
        });
      },
      children: (renderSubject) => web.createComponent(ExternalElementRenderer, {
        render: (el) => renderSubject()(el, props.notification)
      })
    }), null);
    web.insert(_el$4, web.createComponent(solidJs.Show, {
      get when() {
        return props.renderBody;
      },
      get fallback() {
        return web.createComponent(Markdown_default, {
          appearanceKey: "notificationBody",
          strongAppearanceKey: "notificationBody__strong",
          "class": "nt-text-start nt-whitespace-pre-wrap nt-text-foreground-alpha-600 [word-break:break-word]",
          get children() {
            return props.notification.body;
          }
        });
      },
      children: (renderBody) => web.createComponent(ExternalElementRenderer, {
        render: (el) => renderBody()(el, props.notification)
      })
    }), null);
    web.insert(_el$5, () => renderNotificationActions(props.notification, status));
    web.insert(_el$3, web.createComponent(solidJs.Show, {
      get when() {
        return props.notification.primaryAction || props.notification.secondaryAction;
      },
      get children() {
        var _el$6 = _tmpl$218();
        web.insert(_el$6, web.createComponent(solidJs.Show, {
          get when() {
            return props.notification.primaryAction;
          },
          keyed: true,
          children: (primaryAction) => web.createComponent(Button, {
            appearanceKey: "notificationPrimaryAction__button",
            variant: "default",
            onClick: (e) => handleActionButtonClick("primary" /* PRIMARY */, e),
            get children() {
              return primaryAction.label;
            }
          })
        }), null);
        web.insert(_el$6, web.createComponent(solidJs.Show, {
          get when() {
            return props.notification.secondaryAction;
          },
          keyed: true,
          children: (secondaryAction) => web.createComponent(Button, {
            appearanceKey: "notificationSecondaryAction__button",
            variant: "secondary",
            onClick: (e) => handleActionButtonClick("secondary" /* SECONDARY */, e),
            get children() {
              return secondaryAction.label;
            }
          })
        }), null);
        web.effect(() => web.className(_el$6, style("notificationCustomActions", "nt-flex nt-flex-wrap nt-gap-2")));
        return _el$6;
      }
    }), _el$7);
    web.insert(_el$7, web.createComponent(solidJs.Show, {
      get when() {
        return snoozedUntil();
      },
      get fallback() {
        return web.createComponent(solidJs.Show, {
          get when() {
            return deliveredAt();
          },
          get fallback() {
            return web.memo(createdAt);
          },
          children: (deliveredAt2) => web.createComponent(solidJs.Show, {
            get when() {
              return deliveredAt2().length >= 2;
            },
            get children() {
              return [" ", web.createComponent(solidJs.For, {
                get each() {
                  return deliveredAt2().slice(-2);
                },
                children: (date, index) => [web.createComponent(solidJs.Show, {
                  get when() {
                    return index() === 0;
                  },
                  get children() {
                    return [date, " \xB7"];
                  }
                }), web.createComponent(solidJs.Show, {
                  get when() {
                    return index() === 1;
                  },
                  get children() {
                    return web.createComponent(Badge, {
                      appearanceKey: "notificationDeliveredAt__badge",
                      get children() {
                        return [web.createComponent(IconRendererWrapper, {
                          iconKey: "clock",
                          "class": deliveredAtIconClass,
                          get fallback() {
                            return web.createComponent(Clock, {
                              "class": deliveredAtIconClass
                            });
                          }
                        }), date];
                      }
                    });
                  }
                })]
              })];
            }
          })
        });
      },
      children: (snoozedUntil2) => [web.createComponent(IconRendererWrapper, {
        iconKey: "clock",
        "class": snoozedUntilIconClass,
        get fallback() {
          return web.createComponent(Clock, {
            "class": snoozedUntilIconClass
          });
        }
      }), web.memo(() => t("notification.snoozedUntil")), " \xB7 ", web.memo(snoozedUntil2)]
    }));
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return !props.notification.isRead;
      },
      get children() {
        var _el$8 = _tmpl$311();
        web.effect(() => web.className(_el$8, style("notificationDot", "nt-size-1.5 nt-bg-primary nt-rounded-full nt-shrink-0")));
        return _el$8;
      }
    }), null);
    web.effect((_p$) => {
      var _a;
      var _v$3 = style("notification", cn("nt-w-full nt-text-sm hover:nt-bg-primary-alpha-25 nt-group nt-relative nt-flex nt-items-start nt-p-4 nt-gap-2", "[&:not(:first-child)]:nt-border-t nt-border-neutral-alpha-100", {
        "nt-cursor-pointer": !props.notification.isRead || !!((_a = props.notification.redirect) == null ? void 0 : _a.url)
      })), _v$4 = style("notificationContent", "nt-flex nt-flex-col nt-gap-2 nt-w-full"), _v$5 = style("notificationTextContainer"), _v$6 = style("notificationDefaultActions", `nt-absolute nt-transition nt-duration-100 nt-ease-out nt-gap-0.5 nt-flex nt-shrink-0 nt-opacity-0 group-hover:nt-opacity-100 group-focus-within:nt-opacity-100 nt-justify-center nt-items-center nt-bg-background/90 nt-right-3 nt-top-3 nt-border nt-border-neutral-alpha-100 nt-rounded-lg nt-backdrop-blur-lg nt-p-0.5`), _v$7 = style("notificationDate", "nt-text-foreground-alpha-400 nt-flex nt-items-center nt-gap-1");
      _v$3 !== _p$.e && web.className(_el$, _p$.e = _v$3);
      _v$4 !== _p$.t && web.className(_el$3, _p$.t = _v$4);
      _v$5 !== _p$.a && web.className(_el$4, _p$.a = _v$5);
      _v$6 !== _p$.o && web.className(_el$5, _p$.o = _v$6);
      _v$7 !== _p$.i && web.className(_el$7, _p$.i = _v$7);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0,
      o: void 0,
      i: void 0
    });
    return _el$;
  })();
};
web.delegateEvents(["click"]);

// src/ui/components/Notification/Notification.tsx
var Notification = (props) => {
  return web.createComponent(solidJs.Show, {
    get when() {
      return props.renderNotification;
    },
    get fallback() {
      return web.createComponent(DefaultNotification, {
        get notification() {
          return props.notification;
        },
        get renderSubject() {
          return props.renderSubject;
        },
        get renderBody() {
          return props.renderBody;
        },
        get onNotificationClick() {
          return props.onNotificationClick;
        },
        get onPrimaryActionClick() {
          return props.onPrimaryActionClick;
        },
        get onSecondaryActionClick() {
          return props.onSecondaryActionClick;
        }
      });
    },
    get children() {
      return web.createComponent(ExternalElementRenderer, {
        render: (el) => props.renderNotification(el, props.notification)
      });
    }
  });
};
var NewMessagesCta = (props) => {
  const shouldRender = solidJs.createMemo(() => !!props.count);
  const {
    t
  } = useLocalization();
  return web.createComponent(solidJs.Show, {
    get when() {
      return shouldRender();
    },
    get children() {
      return web.createComponent(Button, {
        appearanceKey: "notificationListNewNotificationsNotice__button",
        "class": "nt-absolute nt-w-fit nt-h-fit nt-top-0 nt-mx-auto nt-inset-2 nt-z-10 nt-rounded-full hover:nt-bg-primary-600 nt-animate-in nt-slide-in-from-top-2 nt-fade-in",
        get onClick() {
          return props.onClick;
        },
        "data-localization": "notifications.newNotifications",
        get children() {
          return t("notifications.newNotifications", {
            notificationCount: props.count
          });
        }
      });
    }
  });
};
var _tmpl$68 = /* @__PURE__ */ web.template(`<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 16 16"fill=none><path fill=currentColor d="M12.1675 2.04492L11.5308 2.68164L11.1069 3.10645L12.9614 4.96094L12.7495 5.17383L10.894 3.31836L10.4692 3.74219L9.40967 4.80273L8.98486 5.22754L9.40967 5.65137L10.5747 6.81738L10.3628 7.03027L9.19775 5.86328L8.77295 5.43945L6.35889 7.85352L6.62744 8.26172C7.00257 8.83177 7.18147 9.50559 7.14111 10.1816L7.10986 10.4707C7.00656 11.1451 6.68818 11.7654 6.20557 12.2402L5.98877 12.4346C5.46027 12.8661 4.80786 13.1133 4.13135 13.1426L3.84033 13.1416C3.0614 13.1032 2.3236 12.7769 1.771 12.2266H1.77002C1.28602 11.744 0.974717 11.1186 0.877441 10.4473L0.849121 10.1572C0.814077 9.47419 1.00158 8.80051 1.38037 8.2373L1.55518 8.00293C2.04954 7.39769 2.75121 6.99767 3.52393 6.88086C4.29677 6.76406 5.0856 6.93884 5.73682 7.37109L6.146 7.64258L6.49268 7.29492L11.9546 1.83203L12.1675 2.04492ZM4.00537 7.10645C3.71967 7.11042 3.4363 7.15732 3.16553 7.24512L2.89893 7.34668C2.63748 7.46146 2.39532 7.61469 2.18018 7.80078L1.97803 7.99316C1.52375 8.46356 1.2476 9.0739 1.18994 9.71973L1.17822 9.99805C1.18392 10.6519 1.41417 11.2812 1.82568 11.7822L2.01318 11.9883C2.47551 12.4506 3.0805 12.7377 3.7251 12.8066L4.00342 12.8232C4.75062 12.8297 5.4708 12.5425 6.0083 12.0234L6.44775 11.5986L6.40186 11.5527C6.44537 11.4885 6.48869 11.4241 6.52686 11.3564L6.65479 11.1016C6.76956 10.84 6.84411 10.563 6.87646 10.2803L6.89404 9.99609C6.89801 9.71049 6.85899 9.42635 6.77881 9.15332L6.68506 8.88379C6.5776 8.61923 6.4315 8.3726 6.25146 8.15234L6.06006 7.94141C5.85804 7.73939 5.62719 7.56844 5.37549 7.43555L5.1167 7.31543C4.76396 7.17222 4.38604 7.10121 4.00537 7.10645Z"stroke=#525866 stroke-width=1.2>`);
function Key(props) {
  return (() => {
    var _el$ = _tmpl$68();
    web.spread(_el$, props, true, true);
    return _el$;
  })();
}

// src/ui/components/Notification/NotificationListSkeleton.tsx
var _tmpl$69 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$219 = /* @__PURE__ */ web.template(`<p data-localization=notifications.emptyNotice>`);
var _tmpl$312 = /* @__PURE__ */ web.template(`<div><p>Trigger your notification. No setup needed.</p><p>Temporary &lt;Inbox />, data will expire in 24h. Connect API key to persists messages, enable
                preferences, and connect email.</p><div><div>`);
var NotificationListSkeleton = (props) => {
  const style = useStyle();
  const {
    t
  } = useLocalization();
  const {
    isKeyless
  } = useInboxContext();
  return (() => {
    var _el$ = _tmpl$69();
    web.insert(_el$, web.createComponent(Motion.div, {
      get animate() {
        return {
          scale: props.loading ? 1 : 0.7
        };
      },
      transition: {
        duration: 0.6,
        easing: [0.39, 0.24, 0.3, 1],
        delay: 0.3
      },
      get ["class"]() {
        return style("notificationList__skeleton", "nt-flex nt-relative nt-mx-auto nt-flex-col nt-w-full nt-mb-4");
      },
      get children() {
        return [web.memo(() => Array.from({
          length: 5
        }).map((_, i) => web.createComponent(Motion.div, {
          get animate() {
            return {
              marginBottom: props.loading ? 0 : "16px",
              borderWidth: props.loading ? 0 : "1px",
              borderRadius: props.loading ? 0 : "var(--nv-radius-lg)"
            };
          },
          transition: {
            duration: 0.5,
            delay: 0.3,
            easing: "ease-in-out"
          },
          get ["class"]() {
            return style("notificationList__skeletonContent", "nt-flex nt-border-neutral-alpha-50 nt-items-center nt-gap-3 nt-p-3 nt-bg-neutral-alpha-25");
          },
          get children() {
            return [web.createComponent(SkeletonAvatar, {
              appearanceKey: "notificationList__skeletonAvatar",
              "class": "nt-w-8 nt-h-8 nt-rounded-full nt-bg-neutral-alpha-100"
            }), (() => {
              var _el$3 = _tmpl$69();
              web.insert(_el$3, web.createComponent(SkeletonText, {
                appearanceKey: "notificationList__skeletonText",
                "class": "nt-h-2 nt-w-1/3 nt-bg-neutral-alpha-50 nt-rounded"
              }), null);
              web.insert(_el$3, web.createComponent(SkeletonText, {
                appearanceKey: "notificationList__skeletonText",
                "class": "nt-h-2 nt-w-2/3 nt-bg-neutral-alpha-50 nt-rounded"
              }), null);
              web.effect(() => web.className(_el$3, style("notificationList__skeletonItem", "nt-flex nt-flex-col nt-gap-2 nt-flex-1")));
              return _el$3;
            })()];
          }
        }))), (() => {
          var _el$2 = _tmpl$69();
          web.effect(() => web.className(_el$2, style("notificationListEmptyNoticeOverlay", "nt-absolute nt-size-full nt-z-10 nt-inset-0 nt-bg-gradient-to-b nt-from-transparent nt-to-background")));
          return _el$2;
        })()];
      }
    }), null);
    web.insert(_el$, web.createComponent(solidJs.Show, {
      get when() {
        return !props.loading;
      },
      get children() {
        return web.createComponent(Motion.p, {
          initial: {
            opacity: 0,
            y: -4,
            filter: "blur(4px)"
          },
          get animate() {
            return {
              opacity: props.loading ? 0 : 1,
              y: 0,
              filter: "blur(0px)"
            };
          },
          transition: {
            duration: 0.7,
            easing: [0.39, 0.24, 0.3, 1],
            delay: 0.6
          },
          get ["class"]() {
            return style("notificationListEmptyNotice", "nt-text-center");
          },
          get children() {
            return web.memo(() => !!isKeyless())() ? web.createComponent(KeylessEmptyState, {}) : (() => {
              var _el$4 = _tmpl$219();
              web.insert(_el$4, () => t("notifications.emptyNotice"));
              return _el$4;
            })();
          }
        });
      }
    }), null);
    web.effect(() => web.className(_el$, style("notificationListEmptyNoticeContainer", "nt-flex nt-flex-col nt-items-center nt-h-fit nt-w-full nt-text-sm nt-text-foreground-alpha-400 nt-text-center")));
    return _el$;
  })();
};
function KeylessEmptyState() {
  const style = useStyle();
  const novu = useNovu();
  return (() => {
    var _el$5 = _tmpl$312(), _el$6 = _el$5.firstChild, _el$7 = _el$6.nextSibling, _el$8 = _el$7.nextSibling, _el$9 = _el$8.firstChild;
    web.insert(_el$8, web.createComponent(Button, {
      variant: "secondary",
      size: "sm",
      get ["class"]() {
        return style(
          "notificationListEmptyNotice",
          // eslint-disable-next-line max-len
          "nt-h-8 nt-px-4 nt-flex nt-items-center nt-justify-center nt-gap-2 nt-bg-white nt-border nt-border-neutral-alpha-100 nt-shadow-sm nt-text-[12px] nt-font-medium"
        );
      },
      onClick: () => window.open("https://go.novu.co/keyless", "_blank", "noopener noreferrer"),
      get children() {
        return [web.createComponent(Key, {
          get ["class"]() {
            return style("lockIcon", "nt-size-4 nt-mr-2");
          }
        }), "Get API key"];
      }
    }), _el$9);
    web.insert(_el$9, web.createComponent(Button, {
      variant: "default",
      size: "sm",
      get ["class"]() {
        return style(
          "notificationListEmptyNotice",
          // eslint-disable-next-line max-len
          "nt-h-8 nt-px-4 nt-flex nt-items-center nt-justify-center nt-gap-2 nt-bg-neutral-900 nt-text-white nt-shadow-sm nt-text-[12px] nt-font-medium"
        );
      },
      onClick: () => novu.notifications.triggerHelloWorldEvent(),
      get children() {
        return [web.createComponent(Bell, {
          get ["class"]() {
            return style("bellIcon", "nt-size-4 nt-mr-2");
          }
        }), "Send 'Hello World!'"];
      }
    }));
    web.effect((_p$) => {
      var _v$ = style("notificationListEmptyNotice", "nt--mt-[50px]"), _v$2 = style("strong", "nt-text-[#000000] nt-mb-1"), _v$3 = style("notificationListEmptyNotice", "nt-mb-4"), _v$4 = style("notificationListEmptyNotice", "nt-flex nt-gap-4 nt-justify-center");
      _v$ !== _p$.e && web.className(_el$5, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$6, _p$.t = _v$2);
      _v$3 !== _p$.a && web.className(_el$7, _p$.a = _v$3);
      _v$4 !== _p$.o && web.className(_el$8, _p$.o = _v$4);
      return _p$;
    }, {
      e: void 0,
      t: void 0,
      a: void 0,
      o: void 0
    });
    return _el$5;
  })();
}

// src/ui/components/Notification/NotificationList.tsx
var _tmpl$70 = /* @__PURE__ */ web.template(`<div>`);
var _tmpl$220 = /* @__PURE__ */ web.template(`<div><div>`);
var NotificationList = (props) => {
  var _a, _b, _c, _d;
  const options = solidJs.createMemo(() => chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, props.filter), {
    limit: props.limit
  }));
  const style = useStyle();
  const {
    data,
    setEl,
    end,
    refetch,
    initialLoading
  } = useNotificationsInfiniteScroll({
    options
  });
  const {
    count,
    reset: resetNewMessagesCount
  } = useNewMessagesCount({
    filter: {
      tags: (_b = (_a = props.filter) == null ? void 0 : _a.tags) != null ? _b : [],
      data: (_d = (_c = props.filter) == null ? void 0 : _c.data) != null ? _d : {}
    }
  });
  const {
    setLimit
  } = useInboxContext();
  const ids = solidJs.createMemo(() => data().map((n) => n.id));
  let notificationListElement;
  solidJs.createEffect(() => {
    setLimit(props.limit || DEFAULT_LIMIT);
  });
  const handleOnNewMessagesClick = (e) => chunk7B52C2XE_js.__async(void 0, null, function* () {
    e.stopPropagation();
    resetNewMessagesCount();
    refetch({
      filter: props.filter
    });
    notificationListElement.scrollTo({
      top: 0
    });
  });
  return (() => {
    var _el$ = _tmpl$220(), _el$2 = _el$.firstChild;
    web.insert(_el$, web.createComponent(NewMessagesCta, {
      get count() {
        return count();
      },
      onClick: handleOnNewMessagesClick
    }), _el$2);
    web.use((el) => {
      notificationListElement = el;
    }, _el$2);
    web.insert(_el$2, web.createComponent(solidJs.Show, {
      get when() {
        return data().length > 0;
      },
      get fallback() {
        return web.createComponent(NotificationListSkeleton, {
          get loading() {
            return initialLoading();
          }
        });
      },
      get children() {
        return [web.createComponent(solidJs.For, {
          get each() {
            return ids();
          },
          children: (_, index) => {
            const notification = () => data()[index()];
            return web.createComponent(Notification, {
              get notification() {
                return notification();
              },
              get renderNotification() {
                return props.renderNotification;
              },
              get renderSubject() {
                return props.renderSubject;
              },
              get renderBody() {
                return props.renderBody;
              },
              get onNotificationClick() {
                return props.onNotificationClick;
              },
              get onPrimaryActionClick() {
                return props.onPrimaryActionClick;
              },
              get onSecondaryActionClick() {
                return props.onSecondaryActionClick;
              }
            });
          }
        }), web.createComponent(solidJs.Show, {
          get when() {
            return !end();
          },
          get children() {
            var _el$3 = _tmpl$70();
            web.use(setEl, _el$3);
            web.insert(_el$3, web.createComponent(NotificationListSkeleton, {
              loading: true
            }));
            return _el$3;
          }
        })];
      }
    }));
    web.effect((_p$) => {
      var _v$ = style("notificationListContainer", "nt-relative nt-border-t nt-border-t-neutral-alpha-200 nt-h-full nt-overflow-hidden"), _v$2 = style("notificationList", "nt-relative nt-h-full nt-flex nt-flex-col nt-overflow-y-auto");
      _v$ !== _p$.e && web.className(_el$, _p$.e = _v$);
      _v$2 !== _p$.t && web.className(_el$2, _p$.t = _v$2);
      return _p$;
    }, {
      e: void 0,
      t: void 0
    });
    return _el$;
  })();
};
var _tmpl$71 = /* @__PURE__ */ web.template(`<span>`);
var getDisplayCount = (count) => count >= 100 ? "99+" : count;
var InboxTabUnreadNotificationsCount = (props) => {
  const style = useStyle();
  const displayCount = solidJs.createMemo(() => getDisplayCount(props.count));
  return (() => {
    var _el$ = _tmpl$71();
    web.insert(_el$, displayCount);
    web.effect(() => web.className(_el$, style("notificationsTabsTriggerCount", "nt-rounded-full nt-bg-counter nt-px-[6px] nt-text-counter-foreground nt-text-sm")));
    return _el$;
  })();
};
var InboxTab = (props) => {
  var _a;
  const {
    status
  } = useInboxContext();
  const style = useStyle();
  const unreadCount = useUnreadCount({
    filter: {
      tags: getTagsFromTab(props),
      data: (_a = props.filter) == null ? void 0 : _a.data
    }
  });
  return web.createComponent(Tabs.Trigger, {
    get value() {
      return props.label;
    },
    get ["class"]() {
      return style("notificationsTabs__tabsTrigger", cn(tabsTriggerVariants(), "nt-flex nt-gap-2", props.class));
    },
    get children() {
      return [(() => {
        var _el$2 = _tmpl$71();
        web.insert(_el$2, () => props.label);
        web.effect(() => web.className(_el$2, style("notificationsTabsTriggerLabel", "nt-text-sm nt-font-medium")));
        return _el$2;
      })(), web.createComponent(solidJs.Show, {
        get when() {
          return web.memo(() => status() !== "archived" /* ARCHIVED */)() && unreadCount();
        },
        get children() {
          return web.createComponent(InboxTabUnreadNotificationsCount, {
            get count() {
              return unreadCount();
            }
          });
        }
      })];
    }
  });
};
var InboxDropdownTab = (props) => {
  var _a;
  const {
    status
  } = useInboxContext();
  const style = useStyle();
  const unreadCount = useUnreadCount({
    filter: {
      tags: getTagsFromTab(props),
      data: (_a = props.filter) == null ? void 0 : _a.data
    }
  });
  return web.createComponent(Dropdown.Item, {
    get ["class"]() {
      return style("moreTabs__dropdownItem", cn(dropdownItemVariants(), "nt-flex nt-justify-between nt-gap-2"));
    },
    get onClick() {
      return props.onClick;
    },
    get children() {
      return [(() => {
        var _el$3 = _tmpl$71();
        web.insert(_el$3, () => props.label);
        web.effect(() => web.className(_el$3, style("moreTabs__dropdownItemLabel", "nt-mr-auto")));
        return _el$3;
      })(), web.memo(() => props.rightIcon), web.createComponent(solidJs.Show, {
        get when() {
          return web.memo(() => status() !== "archived" /* ARCHIVED */)() && unreadCount();
        },
        get children() {
          return web.createComponent(InboxTabUnreadNotificationsCount, {
            get count() {
              return unreadCount();
            }
          });
        }
      })];
    }
  });
};

// src/ui/components/InboxTabs/InboxTabs.tsx
var tabsDropdownTriggerVariants = () => `nt-relative after:nt-absolute after:nt-content-[''] after:nt-bottom-0 after:nt-left-0 after:nt-w-full after:nt-h-[2px] after:nt-border-b-2 nt-mb-[0.625rem]`;
var InboxTabs = (props) => {
  const style = useStyle();
  const {
    activeTab,
    status,
    setActiveTab,
    filter
  } = useInboxContext();
  const {
    dropdownTabs,
    setTabsList,
    visibleTabs
  } = useTabsDropdown({
    tabs: props.tabs
  });
  const dropdownTabsUnreadCounts = useUnreadCounts({
    filters: dropdownTabs().map((tab) => {
      var _a;
      return {
        tags: getTagsFromTab(tab),
        data: (_a = tab.filter) == null ? void 0 : _a.data
      };
    })
  });
  const checkIconClass = style("moreTabs__dropdownItemRight__icon", "nt-size-3", {
    iconKey: "check"
  });
  const options = solidJs.createMemo(() => dropdownTabs().map((tab) => chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, tab), {
    rightIcon: tab.label === activeTab() ? web.createComponent(IconRendererWrapper, {
      iconKey: "check",
      "class": checkIconClass,
      get fallback() {
        return web.createComponent(Check, {
          "class": checkIconClass
        });
      }
    }) : void 0
  })));
  const dropdownTabsUnreadSum = solidJs.createMemo(() => dropdownTabsUnreadCounts().reduce((accumulator, currentValue) => accumulator + currentValue, 0));
  const isTabsDropdownActive = solidJs.createMemo(() => dropdownTabs().map((tab) => tab.label).includes(activeTab()));
  const moreTabsIconClass = style("moreTabs__icon", "nt-size-5", {
    iconKey: "arrowDown"
  });
  return web.createComponent(Tabs.Root, {
    appearanceKey: "notificationsTabs__tabsRoot",
    "class": "nt-flex nt-flex-col nt-flex-1 nt-min-h-0",
    get value() {
      return activeTab();
    },
    onChange: setActiveTab,
    get children() {
      return [web.createComponent(solidJs.Show, {
        get when() {
          return visibleTabs().length > 0;
        },
        get fallback() {
          return web.createComponent(Tabs.List, {
            ref: setTabsList,
            appearanceKey: "notificationsTabs__tabsList",
            "class": "nt-bg-neutral-alpha-25 nt-px-4",
            get children() {
              return props.tabs.map((tab) => web.createComponent(InboxTab, web.mergeProps(tab, {
                "class": "nt-invisible"
              })));
            }
          });
        },
        get children() {
          return web.createComponent(Tabs.List, {
            appearanceKey: "notificationsTabs__tabsList",
            "class": "nt-bg-neutral-alpha-25 nt-px-4",
            get children() {
              return [web.createComponent(solidJs.For, {
                get each() {
                  return visibleTabs();
                },
                children: (tab) => web.createComponent(InboxTab, tab)
              }), web.createComponent(solidJs.Show, {
                get when() {
                  return dropdownTabs().length > 0;
                },
                get children() {
                  return web.createComponent(Dropdown.Root, {
                    get children() {
                      return [web.createComponent(Dropdown.Trigger, {
                        appearanceKey: "moreTabs__dropdownTrigger",
                        asChild: (triggerProps) => web.createComponent(Button, web.mergeProps({
                          variant: "unstyled",
                          size: "iconSm",
                          appearanceKey: "moreTabs__button"
                        }, triggerProps, {
                          get ["class"]() {
                            return cn(tabsDropdownTriggerVariants(), "nt-ml-auto", isTabsDropdownActive() ? "after:nt-border-b-primary" : "after:nt-border-b-transparent nt-text-foreground-alpha-700");
                          },
                          get children() {
                            return [web.createComponent(IconRendererWrapper, {
                              iconKey: "arrowDown",
                              "class": moreTabsIconClass,
                              get fallback() {
                                return web.createComponent(ArrowDown, {
                                  "class": moreTabsIconClass
                                });
                              }
                            }), web.createComponent(solidJs.Show, {
                              get when() {
                                return web.memo(() => status() !== "archived" /* ARCHIVED */)() && dropdownTabsUnreadSum();
                              },
                              get children() {
                                return web.createComponent(InboxTabUnreadNotificationsCount, {
                                  get count() {
                                    return dropdownTabsUnreadSum();
                                  }
                                });
                              }
                            })];
                          }
                        }))
                      }), web.createComponent(Dropdown.Content, {
                        appearanceKey: "moreTabs__dropdownContent",
                        get children() {
                          return web.createComponent(solidJs.For, {
                            get each() {
                              return options();
                            },
                            children: (option) => web.createComponent(InboxDropdownTab, web.mergeProps({
                              onClick: () => setActiveTab(option.label)
                            }, option))
                          });
                        }
                      })];
                    }
                  });
                }
              })];
            }
          });
        }
      }), web.memo(() => props.tabs.map((tab) => web.createComponent(Tabs.Content, {
        get value() {
          return tab.label;
        },
        get ["class"]() {
          return style("notificationsTabs__tabsContent", cn(activeTab() === tab.label ? "nt-block" : "nt-hidden", "nt-overflow-auto nt-flex-1 nt-flex nt-flex-col nt-min-h-0"));
        },
        get children() {
          return web.createComponent(NotificationList, {
            get renderNotification() {
              return props.renderNotification;
            },
            get renderSubject() {
              return props.renderSubject;
            },
            get renderBody() {
              return props.renderBody;
            },
            get onNotificationClick() {
              return props.onNotificationClick;
            },
            get onPrimaryActionClick() {
              return props.onPrimaryActionClick;
            },
            get onSecondaryActionClick() {
              return props.onSecondaryActionClick;
            },
            get filter() {
              var _a;
              return chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, filter()), {
                tags: getTagsFromTab(tab),
                data: (_a = tab.filter) == null ? void 0 : _a.data
              });
            }
          });
        }
      })))];
    }
  });
};

// src/ui/components/Inbox.tsx
var _tmpl$73 = /* @__PURE__ */ web.template(`<div>`);
var InboxPage = /* @__PURE__ */ function(InboxPage2) {
  InboxPage2["Notifications"] = "notifications";
  InboxPage2["Preferences"] = "preferences";
  return InboxPage2;
}({});
var InboxContent = (props) => {
  const {
    isDevelopmentMode
  } = useInboxContext();
  const [currentPage, setCurrentPage] = solidJs.createSignal(props.initialPage || InboxPage.Notifications);
  const {
    tabs,
    filter
  } = useInboxContext();
  const style = useStyle();
  const navigateToPage = solidJs.createMemo(() => (page) => {
    if (props.hideNav) {
      return void 0;
    }
    return () => {
      setCurrentPage(page);
    };
  });
  return (() => {
    var _el$ = _tmpl$73();
    web.insert(_el$, web.createComponent(solidJs.Switch, {
      get children() {
        return [web.createComponent(solidJs.Match, {
          get when() {
            return currentPage() === InboxPage.Notifications;
          },
          get children() {
            return [web.createComponent(Header, {
              get navigateToPreferences() {
                return navigateToPage()(InboxPage.Preferences);
              }
            }), web.createComponent(solidJs.Show, {
              keyed: true,
              get when() {
                return web.memo(() => !!tabs())() && tabs().length > 0;
              },
              get fallback() {
                return web.createComponent(NotificationList, {
                  get renderNotification() {
                    return props.renderNotification;
                  },
                  get renderSubject() {
                    return props.renderSubject;
                  },
                  get renderBody() {
                    return props.renderBody;
                  },
                  get onNotificationClick() {
                    return props.onNotificationClick;
                  },
                  get onPrimaryActionClick() {
                    return props.onPrimaryActionClick;
                  },
                  get onSecondaryActionClick() {
                    return props.onSecondaryActionClick;
                  },
                  get filter() {
                    return filter();
                  }
                });
              },
              get children() {
                return web.createComponent(InboxTabs, {
                  get renderNotification() {
                    return props.renderNotification;
                  },
                  get renderSubject() {
                    return props.renderSubject;
                  },
                  get renderBody() {
                    return props.renderBody;
                  },
                  get onNotificationClick() {
                    return props.onNotificationClick;
                  },
                  get onPrimaryActionClick() {
                    return props.onPrimaryActionClick;
                  },
                  get onSecondaryActionClick() {
                    return props.onSecondaryActionClick;
                  },
                  get tabs() {
                    return tabs();
                  }
                });
              }
            })];
          }
        }), web.createComponent(solidJs.Match, {
          get when() {
            return currentPage() === InboxPage.Preferences;
          },
          get children() {
            return [web.createComponent(PreferencesHeader, {
              get navigateToNotifications() {
                return navigateToPage()(InboxPage.Notifications);
              }
            }), web.createComponent(Preferences, {})];
          }
        })];
      }
    }), null);
    web.insert(_el$, web.createComponent(Footer, {}), null);
    web.effect(() => web.className(_el$, style("inboxContent", cn("nt-h-full nt-flex nt-flex-col [&_.nv-preferencesContainer]:nt-pb-8 [&_.nv-notificationList]:nt-pb-8", {
      "[&_.nv-preferencesContainer]:nt-pb-12 [&_.nv-notificationList]:nt-pb-12": isDevelopmentMode(),
      "[&_.nv-preferencesContainer]:nt-pb-8 [&_.nv-notificationList]:nt-pb-8": !isDevelopmentMode()
    }))));
    return _el$;
  })();
};
var Inbox = (props) => {
  const style = useStyle();
  const {
    isOpened,
    setIsOpened
  } = useInboxContext();
  const isOpen = () => {
    var _a;
    return (_a = props == null ? void 0 : props.open) != null ? _a : isOpened();
  };
  return web.createComponent(Popover.Root, {
    get open() {
      return isOpen();
    },
    onOpenChange: setIsOpened,
    get placement() {
      return props.placement;
    },
    get offset() {
      return props.placementOffset;
    },
    get children() {
      return [web.createComponent(Popover.Trigger, {
        asChild: (triggerProps) => web.createComponent(Button, web.mergeProps({
          get ["class"]() {
            return style("inbox__popoverTrigger");
          },
          variant: "ghost",
          size: "icon"
        }, triggerProps, {
          get children() {
            return web.createComponent(Bell2, {
              get renderBell() {
                return props.renderBell;
              }
            });
          }
        }))
      }), web.createComponent(Popover.Content, {
        appearanceKey: "inbox__popoverContent",
        portal: true,
        get children() {
          return web.createComponent(solidJs.Show, {
            get when() {
              return props.renderNotification;
            },
            get fallback() {
              return web.createComponent(InboxContent, {
                get renderSubject() {
                  return props.renderSubject;
                },
                get renderBody() {
                  return props.renderBody;
                },
                get onNotificationClick() {
                  return props.onNotificationClick;
                },
                get onPrimaryActionClick() {
                  return props.onPrimaryActionClick;
                },
                get onSecondaryActionClick() {
                  return props.onSecondaryActionClick;
                }
              });
            },
            get children() {
              return web.createComponent(InboxContent, {
                get renderNotification() {
                  return props.renderNotification;
                },
                get onNotificationClick() {
                  return props.onNotificationClick;
                },
                get onPrimaryActionClick() {
                  return props.onPrimaryActionClick;
                },
                get onSecondaryActionClick() {
                  return props.onSecondaryActionClick;
                }
              });
            }
          });
        }
      })];
    }
  });
};

// src/ui/components/Renderer.tsx
var novuComponents = {
  Inbox,
  InboxContent,
  Bell: Bell2,
  Notifications: (props) => {
    if (props.renderNotification) {
      const _a = props, propsWithoutBodyAndSubject = chunk7B52C2XE_js.__objRest(_a, [
        "renderBody",
        "renderSubject"
      ]);
      return web.createComponent(InboxContent, web.mergeProps(propsWithoutBodyAndSubject, {
        hideNav: true,
        get initialPage() {
          return InboxPage.Notifications;
        }
      }));
    }
    const _b = props, propsWithoutRenderNotification = chunk7B52C2XE_js.__objRest(_b, [
      "renderNotification"
    ]);
    return web.createComponent(InboxContent, web.mergeProps(propsWithoutRenderNotification, {
      hideNav: true,
      get initialPage() {
        return InboxPage.Notifications;
      }
    }));
  },
  Preferences: (props) => {
    if (props.renderNotification) {
      const _a = props, propsWithoutBodyAndSubject = chunk7B52C2XE_js.__objRest(_a, [
        "renderBody",
        "renderSubject"
      ]);
      return web.createComponent(InboxContent, web.mergeProps(propsWithoutBodyAndSubject, {
        hideNav: true,
        get initialPage() {
          return InboxPage.Preferences;
        }
      }));
    }
    const _b = props, propsWithoutRenderNotification = chunk7B52C2XE_js.__objRest(_b, [
      "renderNotification"
    ]);
    return web.createComponent(InboxContent, web.mergeProps(propsWithoutRenderNotification, {
      hideNav: true,
      get initialPage() {
        return InboxPage.Preferences;
      }
    }));
  }
};
var Renderer = (props) => {
  const nodes = () => [...props.nodes.keys()];
  solidJs.onMount(() => {
    var _a;
    const id = NOVU_DEFAULT_CSS_ID;
    const root = props.container instanceof ShadowRoot ? props.container : document;
    const el = root.getElementById(id);
    if (el) {
      return;
    }
    const styleEl = document.createElement("style");
    styleEl.id = id;
    styleEl.innerHTML = ui_default;
    const stylesContainer = (_a = props.container) != null ? _a : document.head;
    stylesContainer.insertBefore(styleEl, stylesContainer.firstChild);
    solidJs.onCleanup(() => {
      styleEl.remove();
    });
  });
  return web.createComponent(NovuProvider, {
    get options() {
      return props.options;
    },
    get novu() {
      return props.novu;
    },
    get children() {
      return web.createComponent(LocalizationProvider, {
        get localization() {
          return props.localization;
        },
        get children() {
          return web.createComponent(AppearanceProvider, {
            get id() {
              return props.novuUI.id;
            },
            get appearance() {
              return props.appearance;
            },
            get container() {
              return props.container;
            },
            get children() {
              return web.createComponent(FocusManagerProvider, {
                get children() {
                  return web.createComponent(InboxProvider, {
                    get applicationIdentifier() {
                      var _a;
                      return (_a = props.options) == null ? void 0 : _a.applicationIdentifier;
                    },
                    get tabs() {
                      return props.tabs;
                    },
                    get preferencesFilter() {
                      return props.preferencesFilter;
                    },
                    get preferenceGroups() {
                      return props.preferenceGroups;
                    },
                    get routerPush() {
                      return props.routerPush;
                    },
                    get children() {
                      return web.createComponent(CountProvider, {
                        get children() {
                          return web.createComponent(solidJs.For, {
                            get each() {
                              return nodes();
                            },
                            children: (node) => {
                              const novuComponent = () => props.nodes.get(node);
                              let portalDivElement;
                              const Component = novuComponents[novuComponent().name];
                              solidJs.onMount(() => {
                                if (!["Notifications", "Preferences", "InboxContent"].includes(novuComponent().name)) return;
                                if (node instanceof HTMLElement) {
                                  node.style.height = "100%";
                                }
                                if (portalDivElement) {
                                  portalDivElement.style.height = "100%";
                                }
                              });
                              return web.createComponent(web.Portal, {
                                mount: node,
                                ref: (el) => {
                                  portalDivElement = el;
                                },
                                get children() {
                                  return web.createComponent(Root, {
                                    get children() {
                                      return web.createComponent(Component, web.mergeProps(() => novuComponent().props));
                                    }
                                  });
                                }
                              });
                            }
                          });
                        }
                      });
                    }
                  });
                }
              });
            }
          });
        }
      });
    }
  });
};

// src/ui/novuUI.tsx
var _dispose, _container, _setContainer, _rootElement, _mountedElements, _setMountedElements, _appearance, _setAppearance, _localization, _setLocalization, _options, _setOptions, _tabs, _setTabs, _routerPush, _setRouterPush, _preferencesFilter, _setPreferencesFilter, _preferenceGroups, _setPreferenceGroups, _predefinedNovu, _NovuUI_instances, getContainerElement_fn, mountComponentRenderer_fn, updateComponentProps_fn;
var NovuUI = class {
  constructor(props) {
    chunk7B52C2XE_js.__privateAdd(this, _NovuUI_instances);
    chunk7B52C2XE_js.__privateAdd(this, _dispose, null);
    chunk7B52C2XE_js.__privateAdd(this, _container);
    chunk7B52C2XE_js.__privateAdd(this, _setContainer);
    chunk7B52C2XE_js.__privateAdd(this, _rootElement);
    chunk7B52C2XE_js.__privateAdd(this, _mountedElements);
    chunk7B52C2XE_js.__privateAdd(this, _setMountedElements);
    chunk7B52C2XE_js.__privateAdd(this, _appearance);
    chunk7B52C2XE_js.__privateAdd(this, _setAppearance);
    chunk7B52C2XE_js.__privateAdd(this, _localization);
    chunk7B52C2XE_js.__privateAdd(this, _setLocalization);
    chunk7B52C2XE_js.__privateAdd(this, _options);
    chunk7B52C2XE_js.__privateAdd(this, _setOptions);
    chunk7B52C2XE_js.__privateAdd(this, _tabs);
    chunk7B52C2XE_js.__privateAdd(this, _setTabs);
    chunk7B52C2XE_js.__privateAdd(this, _routerPush);
    chunk7B52C2XE_js.__privateAdd(this, _setRouterPush);
    chunk7B52C2XE_js.__privateAdd(this, _preferencesFilter);
    chunk7B52C2XE_js.__privateAdd(this, _setPreferencesFilter);
    chunk7B52C2XE_js.__privateAdd(this, _preferenceGroups);
    chunk7B52C2XE_js.__privateAdd(this, _setPreferenceGroups);
    chunk7B52C2XE_js.__privateAdd(this, _predefinedNovu);
    var _a;
    this.id = generateRandomString(16);
    const [appearance, setAppearance] = solidJs.createSignal(props.appearance);
    const [localization, setLocalization] = solidJs.createSignal(props.localization);
    const [options, setOptions] = solidJs.createSignal(props.options);
    const [mountedElements, setMountedElements] = solidJs.createSignal(/* @__PURE__ */ new Map());
    const [tabs, setTabs] = solidJs.createSignal((_a = props.tabs) != null ? _a : []);
    const [preferencesFilter, setPreferencesFilter] = solidJs.createSignal(props.preferencesFilter);
    const [preferenceGroups, setPreferenceGroups] = solidJs.createSignal(props.preferenceGroups);
    const [routerPush, setRouterPush] = solidJs.createSignal(props.routerPush);
    const [container, setContainer] = solidJs.createSignal(chunk7B52C2XE_js.__privateMethod(this, _NovuUI_instances, getContainerElement_fn).call(this, props.container));
    chunk7B52C2XE_js.__privateSet(this, _mountedElements, mountedElements);
    chunk7B52C2XE_js.__privateSet(this, _setMountedElements, setMountedElements);
    chunk7B52C2XE_js.__privateSet(this, _appearance, appearance);
    chunk7B52C2XE_js.__privateSet(this, _setAppearance, setAppearance);
    chunk7B52C2XE_js.__privateSet(this, _localization, localization);
    chunk7B52C2XE_js.__privateSet(this, _setLocalization, setLocalization);
    chunk7B52C2XE_js.__privateSet(this, _options, options);
    chunk7B52C2XE_js.__privateSet(this, _setOptions, setOptions);
    chunk7B52C2XE_js.__privateSet(this, _tabs, tabs);
    chunk7B52C2XE_js.__privateSet(this, _setTabs, setTabs);
    chunk7B52C2XE_js.__privateSet(this, _routerPush, routerPush);
    chunk7B52C2XE_js.__privateSet(this, _setRouterPush, setRouterPush);
    chunk7B52C2XE_js.__privateSet(this, _predefinedNovu, props.novu);
    chunk7B52C2XE_js.__privateSet(this, _preferencesFilter, preferencesFilter);
    chunk7B52C2XE_js.__privateSet(this, _setPreferencesFilter, setPreferencesFilter);
    chunk7B52C2XE_js.__privateSet(this, _preferenceGroups, preferenceGroups);
    chunk7B52C2XE_js.__privateSet(this, _setPreferenceGroups, setPreferenceGroups);
    chunk7B52C2XE_js.__privateSet(this, _container, container);
    chunk7B52C2XE_js.__privateSet(this, _setContainer, setContainer);
    chunk7B52C2XE_js.__privateMethod(this, _NovuUI_instances, mountComponentRenderer_fn).call(this);
  }
  mountComponent({
    name,
    element,
    props: componentProps
  }) {
    if (chunk7B52C2XE_js.__privateGet(this, _mountedElements).call(this).has(element)) {
      return chunk7B52C2XE_js.__privateMethod(this, _NovuUI_instances, updateComponentProps_fn).call(this, element, componentProps);
    }
    chunk7B52C2XE_js.__privateGet(this, _setMountedElements).call(this, (oldNodes) => {
      const newNodes = new Map(oldNodes);
      newNodes.set(element, {
        name,
        props: componentProps
      });
      return newNodes;
    });
  }
  unmountComponent(element) {
    chunk7B52C2XE_js.__privateGet(this, _setMountedElements).call(this, (oldMountedElements) => {
      const newMountedElements = new Map(oldMountedElements);
      newMountedElements.delete(element);
      return newMountedElements;
    });
  }
  updateAppearance(appearance) {
    chunk7B52C2XE_js.__privateGet(this, _setAppearance).call(this, appearance);
  }
  updateLocalization(localization) {
    chunk7B52C2XE_js.__privateGet(this, _setLocalization).call(this, localization);
  }
  updateOptions(options) {
    chunk7B52C2XE_js.__privateGet(this, _setOptions).call(this, options);
  }
  updateTabs(tabs) {
    chunk7B52C2XE_js.__privateGet(this, _setTabs).call(this, tabs != null ? tabs : []);
  }
  updatePreferencesFilter(preferencesFilter) {
    chunk7B52C2XE_js.__privateGet(this, _setPreferencesFilter).call(this, preferencesFilter);
  }
  updatePreferenceGroups(preferenceGroups) {
    chunk7B52C2XE_js.__privateGet(this, _setPreferenceGroups).call(this, preferenceGroups);
  }
  updateRouterPush(routerPush) {
    chunk7B52C2XE_js.__privateGet(this, _setRouterPush).call(this, () => routerPush);
  }
  updateContainer(container) {
    chunk7B52C2XE_js.__privateGet(this, _setContainer).call(this, chunk7B52C2XE_js.__privateMethod(this, _NovuUI_instances, getContainerElement_fn).call(this, container));
  }
  unmount() {
    var _a, _b;
    (_a = chunk7B52C2XE_js.__privateGet(this, _dispose)) == null ? void 0 : _a.call(this);
    chunk7B52C2XE_js.__privateSet(this, _dispose, null);
    (_b = chunk7B52C2XE_js.__privateGet(this, _rootElement)) == null ? void 0 : _b.remove();
  }
};
_dispose = new WeakMap();
_container = new WeakMap();
_setContainer = new WeakMap();
_rootElement = new WeakMap();
_mountedElements = new WeakMap();
_setMountedElements = new WeakMap();
_appearance = new WeakMap();
_setAppearance = new WeakMap();
_localization = new WeakMap();
_setLocalization = new WeakMap();
_options = new WeakMap();
_setOptions = new WeakMap();
_tabs = new WeakMap();
_setTabs = new WeakMap();
_routerPush = new WeakMap();
_setRouterPush = new WeakMap();
_preferencesFilter = new WeakMap();
_setPreferencesFilter = new WeakMap();
_preferenceGroups = new WeakMap();
_setPreferenceGroups = new WeakMap();
_predefinedNovu = new WeakMap();
_NovuUI_instances = new WeakSet();
getContainerElement_fn = function(container) {
  var _a;
  if (container === null || container === void 0) {
    return container;
  }
  if (typeof container === "string") {
    return (_a = document.querySelector(container)) != null ? _a : document.getElementById(container);
  }
  return container;
};
mountComponentRenderer_fn = function() {
  if (chunk7B52C2XE_js.__privateGet(this, _dispose) !== null) {
    return;
  }
  chunk7B52C2XE_js.__privateSet(this, _rootElement, document.createElement("div"));
  chunk7B52C2XE_js.__privateGet(this, _rootElement).setAttribute("id", `novu-ui-${this.id}`);
  const container = chunk7B52C2XE_js.__privateGet(this, _container).call(this);
  (container != null ? container : document.body).appendChild(chunk7B52C2XE_js.__privateGet(this, _rootElement));
  const dispose = web.render(() => {
    const _self$ = this;
    return web.createComponent(Renderer, {
      novuUI: _self$,
      get nodes() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _mountedElements).call(_a);
      },
      get options() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _options).call(_a);
      },
      get appearance() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _appearance).call(_a);
      },
      get localization() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _localization).call(_a);
      },
      get tabs() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _tabs).call(_a);
      },
      get preferencesFilter() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _preferencesFilter).call(_a);
      },
      get preferenceGroups() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _preferenceGroups).call(_a);
      },
      get routerPush() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _routerPush).call(_a);
      },
      get novu() {
        return chunk7B52C2XE_js.__privateGet(_self$, _predefinedNovu);
      },
      get container() {
        var _a;
        return chunk7B52C2XE_js.__privateGet(_a = _self$, _container).call(_a);
      }
    });
  }, chunk7B52C2XE_js.__privateGet(this, _rootElement));
  chunk7B52C2XE_js.__privateSet(this, _dispose, dispose);
};
updateComponentProps_fn = function(element, props) {
  chunk7B52C2XE_js.__privateGet(this, _setMountedElements).call(this, (oldMountedElements) => {
    const newMountedElements = new Map(oldMountedElements);
    const mountedElement = newMountedElements.get(element);
    if (mountedElement) {
      newMountedElements.set(element, chunk7B52C2XE_js.__spreadProps(chunk7B52C2XE_js.__spreadValues({}, mountedElement), {
        props
      }));
    }
    return newMountedElements;
  });
};

exports.NotificationStatus = NotificationStatus;
exports.NovuUI = NovuUI;
