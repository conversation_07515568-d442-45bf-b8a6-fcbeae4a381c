import { g as NovuOptions } from '../novu-CnZkqUKP.js';
export { d as Notification } from '../novu-CnZkqUKP.js';
import { B as BellRenderer, N as NotificationClickHandler, a as NotificationActionClickHandler, b as NotificationRenderer, S as SubjectRenderer, c as BodyRenderer, d as NovuProviderProps, e as BaseNovuProviderProps, A as Appearance, L as Localization, T as Tab, P as PreferencesFilter, f as PreferenceGroups, R as RouterPush } from '../types-BSZsDqkz.js';
export { g as AppearanceKey, h as ElementStyles, E as Elements, I as IconKey, i as IconOverrides, j as IconRenderer, k as LocalizationKey, l as NotificationStatus, m as Theme, V as Variables } from '../types-BSZsDqkz.js';
import { Placement, OffsetOptions } from '@floating-ui/dom';
import * as solid_js from 'solid-js';
import { ComponentProps } from 'solid-js';
import { MountableElement } from 'solid-js/web';

type NotificationRendererProps = {
    renderNotification: NotificationRenderer;
    renderSubject?: never;
    renderBody?: never;
};
type SubjectBodyRendererProps = {
    renderNotification?: never;
    renderSubject?: SubjectRenderer;
    renderBody?: BodyRenderer;
};
type NoRendererProps = {
    renderNotification?: undefined;
    renderSubject?: undefined;
    renderBody?: undefined;
};
type InboxProps = {
    open?: boolean;
    renderBell?: BellRenderer;
    onNotificationClick?: NotificationClickHandler;
    onPrimaryActionClick?: NotificationActionClickHandler;
    onSecondaryActionClick?: NotificationActionClickHandler;
    placement?: Placement;
    placementOffset?: OffsetOptions;
} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);
declare enum InboxPage {
    Notifications = "notifications",
    Preferences = "preferences"
}
type InboxContentProps = {
    onNotificationClick?: NotificationClickHandler;
    onPrimaryActionClick?: NotificationActionClickHandler;
    onSecondaryActionClick?: NotificationActionClickHandler;
    initialPage?: InboxPage;
    hideNav?: boolean;
} & (NotificationRendererProps | SubjectBodyRendererProps | NoRendererProps);

declare const novuComponents: {
    Inbox: (props: InboxProps) => solid_js.JSX.Element;
    InboxContent: (props: InboxContentProps) => solid_js.JSX.Element;
    Bell: solid_js.Component<{
        renderBell?: BellRenderer;
    }>;
    Notifications: (props: Omit<InboxContentProps, "hideNav" | "initialPage">) => solid_js.JSX.Element;
    Preferences: (props: Omit<InboxContentProps, "hideNav" | "initialPage">) => solid_js.JSX.Element;
};
type NovuComponentName = keyof typeof novuComponents;

type NovuUIOptions = NovuProviderProps;
type BaseNovuUIOptions = BaseNovuProviderProps;
declare class NovuUI {
    #private;
    id: string;
    constructor(props: NovuProviderProps);
    mountComponent<T extends NovuComponentName>({ name, element, props: componentProps, }: {
        name: T;
        element: MountableElement;
        props?: ComponentProps<(typeof novuComponents)[T]>;
    }): void;
    unmountComponent(element: MountableElement): void;
    updateAppearance(appearance?: Appearance): void;
    updateLocalization(localization?: Localization): void;
    updateOptions(options: NovuOptions): void;
    updateTabs(tabs?: Array<Tab>): void;
    updatePreferencesFilter(preferencesFilter?: PreferencesFilter): void;
    updatePreferenceGroups(preferenceGroups?: PreferenceGroups): void;
    updateRouterPush(routerPush?: RouterPush): void;
    updateContainer(container?: Node | string | null): void;
    unmount(): void;
}

export { Appearance, type BaseNovuUIOptions, BellRenderer, BodyRenderer, InboxPage, type InboxProps, Localization, NotificationActionClickHandler, NotificationClickHandler, NotificationRenderer, NovuProviderProps, NovuUI, type NovuUIOptions, PreferenceGroups, PreferencesFilter, RouterPush, SubjectRenderer, Tab };
