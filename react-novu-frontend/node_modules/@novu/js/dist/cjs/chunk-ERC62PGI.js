'use strict';

// src/ui/internal/parseMarkdown.tsx
var parseMarkdownIntoTokens = (text) => {
  const tokens = [];
  let buffer = "";
  let inBold = false;
  for (let i = 0; i < text.length; i += 1) {
    if (text[i] === "\\" && text[i + 1] === "*") {
      buffer += "*";
      i += 1;
    } else if (text[i] === "*" && text[i + 1] === "*") {
      if (buffer) {
        tokens.push({
          type: inBold ? "bold" : "text",
          content: buffer
        });
        buffer = "";
      }
      inBold = !inBold;
      i += 1;
    } else {
      buffer += text[i];
    }
  }
  if (buffer) {
    tokens.push({
      type: inBold ? "bold" : "text",
      content: buffer
    });
  }
  return tokens;
};

exports.parseMarkdownIntoTokens = parseMarkdownIntoTokens;
