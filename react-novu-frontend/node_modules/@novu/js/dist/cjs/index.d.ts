import { N as NotificationFilter } from './novu-CnZkqUKP.js';
export { C as ChannelPreference, c as ChannelType, E as EventHandler, a as Events, F as FiltersCountResponse, I as InboxNotification, L as ListNotificationsResponse, d as Notification, e as NotificationStatus, b as Novu, f as NovuError, g as NovuOptions, P as Preference, i as PreferenceLevel, j as PreferencesResponse, S as SocketEventNames, h as StandardNovuOptions, k as Subscriber, W as WebSocketEvent } from './novu-CnZkqUKP.js';

declare const areTagsEqual: (tags1?: string[], tags2?: string[]) => boolean;
declare const isSameFilter: (filter1: NotificationFilter, filter2: NotificationFilter) => boolean;

export { NotificationFilter, areTagsEqual, isSameFilter };
