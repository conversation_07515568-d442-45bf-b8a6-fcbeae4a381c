"use strict";(()=>{var Wo=Object.create;var ai=Object.defineProperty,$o=Object.defineProperties,zo=Object.getOwnPropertyDescriptor,Vo=Object.getOwnPropertyDescriptors,Ho=Object.getOwnPropertyNames,oi=Object.getOwnPropertySymbols,jo=Object.getPrototypeOf,or=Object.prototype.hasOwnProperty,fn=Object.prototype.propertyIsEnumerable;var un=i=>{throw TypeError(i)},dn=Math.pow,sr=(i,e,t)=>e in i?ai(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,E=(i,e)=>{for(var t in e||(e={}))or.call(e,t)&&sr(i,t,e[t]);if(oi)for(var t of oi(e))fn.call(e,t)&&sr(i,t,e[t]);return i},R=(i,e)=>$o(i,Vo(e));var N=(i=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(i,{get:(e,t)=>(typeof require!="undefined"?require:e)[t]}):i)(function(i){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+i+'" is not supported')});var ar=(i,e)=>{var t={};for(var r in i)or.call(i,r)&&e.indexOf(r)<0&&(t[r]=i[r]);if(i!=null&&oi)for(var r of oi(i))e.indexOf(r)<0&&fn.call(i,r)&&(t[r]=i[r]);return t};var P=(i,e)=>()=>(e||i((e={exports:{}}).exports,e),e.exports),Go=(i,e)=>{for(var t in e)ai(i,t,{get:e[t],enumerable:!0})},Ko=(i,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Ho(e))!or.call(i,n)&&n!==t&&ai(i,n,{get:()=>e[n],enumerable:!(r=zo(e,n))||r.enumerable});return i};var K=(i,e,t)=>(t=i!=null?Wo(jo(i)):{},Ko(e||!i||!i.__esModule?ai(t,"default",{value:i,enumerable:!0}):t,i));var O=(i,e,t)=>sr(i,typeof e!="symbol"?e+"":e,t),cr=(i,e,t)=>e.has(i)||un("Cannot "+t);var l=(i,e,t)=>(cr(i,e,"read from private field"),t?t.call(i):e.get(i)),v=(i,e,t)=>e.has(i)?un("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(i):e.set(i,t),S=(i,e,t,r)=>(cr(i,e,"write to private field"),r?r.call(i,t):e.set(i,t),t),le=(i,e,t)=>(cr(i,e,"access private method"),t);var m=(i,e,t)=>new Promise((r,n)=>{var s=h=>{try{c(t.next(h))}catch(f){n(f)}},o=h=>{try{c(t.throw(h))}catch(f){n(f)}},c=h=>h.done?r(h.value):Promise.resolve(h.value).then(s,o);c((t=t.apply(i,e)).next())});var yr=P((ef,Tn)=>{"use strict";var ut=1e3,dt=ut*60,pt=dt*60,Ke=pt*24,la=Ke*7,ha=Ke*365.25;Tn.exports=function(i,e){e=e||{};var t=typeof i;if(t==="string"&&i.length>0)return fa(i);if(t==="number"&&isFinite(i))return e.long?da(i):ua(i);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(i))};function fa(i){if(i=String(i),!(i.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(e){var t=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return t*ha;case"weeks":case"week":case"w":return t*la;case"days":case"day":case"d":return t*Ke;case"hours":case"hour":case"hrs":case"hr":case"h":return t*pt;case"minutes":case"minute":case"mins":case"min":case"m":return t*dt;case"seconds":case"second":case"secs":case"sec":case"s":return t*ut;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function ua(i){var e=Math.abs(i);return e>=Ke?Math.round(i/Ke)+"d":e>=pt?Math.round(i/pt)+"h":e>=dt?Math.round(i/dt)+"m":e>=ut?Math.round(i/ut)+"s":i+"ms"}function da(i){var e=Math.abs(i);return e>=Ke?Ci(i,e,Ke,"day"):e>=pt?Ci(i,e,pt,"hour"):e>=dt?Ci(i,e,dt,"minute"):e>=ut?Ci(i,e,ut,"second"):i+" ms"}function Ci(i,e,t,r){var n=e>=t*1.5;return Math.round(i/t)+" "+r+(n?"s":"")}});var vr=P((tf,On)=>{"use strict";function pa(i){t.debug=t,t.default=t,t.coerce=h,t.disable=s,t.enable=n,t.enabled=o,t.humanize=yr(),t.destroy=f,Object.keys(i).forEach(a=>{t[a]=i[a]}),t.names=[],t.skips=[],t.formatters={};function e(a){let u=0;for(let d=0;d<a.length;d++)u=(u<<5)-u+a.charCodeAt(d),u|=0;return t.colors[Math.abs(u)%t.colors.length]}t.selectColor=e;function t(a){let u,d=null,_,b;function g(...w){if(!g.enabled)return;let T=g,x=Number(new Date),p=x-(u||x);T.diff=p,T.prev=u,T.curr=x,u=x,w[0]=t.coerce(w[0]),typeof w[0]!="string"&&w.unshift("%O");let y=0;w[0]=w[0].replace(/%([a-zA-Z%])/g,(A,M)=>{if(A==="%%")return"%";y++;let q=t.formatters[M];if(typeof q=="function"){let ee=w[y];A=q.call(T,ee),w.splice(y,1),y--}return A}),t.formatArgs.call(T,w),(T.log||t.log).apply(T,w)}return g.namespace=a,g.useColors=t.useColors(),g.color=t.selectColor(a),g.extend=r,g.destroy=t.destroy,Object.defineProperty(g,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(_!==t.namespaces&&(_=t.namespaces,b=t.enabled(a)),b),set:w=>{d=w}}),typeof t.init=="function"&&t.init(g),g}function r(a,u){let d=t(this.namespace+(typeof u=="undefined"?":":u)+a);return d.log=this.log,d}function n(a){t.save(a),t.namespaces=a,t.names=[],t.skips=[];let u,d=(typeof a=="string"?a:"").split(/[\s,]+/),_=d.length;for(u=0;u<_;u++)d[u]&&(a=d[u].replace(/\*/g,".*?"),a[0]==="-"?t.skips.push(new RegExp("^"+a.slice(1)+"$")):t.names.push(new RegExp("^"+a+"$")))}function s(){let a=[...t.names.map(c),...t.skips.map(c).map(u=>"-"+u)].join(",");return t.enable(""),a}function o(a){if(a[a.length-1]==="*")return!0;let u,d;for(u=0,d=t.skips.length;u<d;u++)if(t.skips[u].test(a))return!1;for(u=0,d=t.names.length;u<d;u++)if(t.names[u].test(a))return!0;return!1}function c(a){return a.toString().substring(2,a.toString().length-2).replace(/\.\*\?$/,"*")}function h(a){return a instanceof Error?a.stack||a.message:a}function f(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}On.exports=pa});var In=P((re,Ni)=>{"use strict";re.formatArgs=ga;re.save=ya;re.load=va;re.useColors=ma;re.storage=_a();re.destroy=(()=>{let i=!1;return()=>{i||(i=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();re.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function ma(){if(typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let i;return typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&(i=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(i[1],10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function ga(i){if(i[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+i[0]+(this.useColors?"%c ":" ")+"+"+Ni.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;i.splice(1,0,e,"color: inherit");let t=0,r=0;i[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(t++,n==="%c"&&(r=t))}),i.splice(r,0,e)}re.log=console.debug||console.log||(()=>{});function ya(i){try{i?re.storage.setItem("debug",i):re.storage.removeItem("debug")}catch(e){}}function va(){let i;try{i=re.storage.getItem("debug")}catch(e){}return!i&&typeof process!="undefined"&&"env"in process&&(i=process.env.DEBUG),i}function _a(){try{return localStorage}catch(i){}}Ni.exports=vr()(re);var{formatters:Ea}=Ni.exports;Ea.j=function(i){try{return JSON.stringify(i)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var Ln=P((rf,Pn)=>{"use strict";Pn.exports=(i,e=process.argv)=>{let t=i.startsWith("-")?"":i.length===1?"-":"--",r=e.indexOf(t+i),n=e.indexOf("--");return r!==-1&&(n===-1||r<n)}});var Sr=P((nf,Un)=>{"use strict";var Sa=N("os"),Fn=N("tty"),he=Ln(),{env:V}=process,Be;he("no-color")||he("no-colors")||he("color=false")||he("color=never")?Be=0:(he("color")||he("colors")||he("color=true")||he("color=always"))&&(Be=1);"FORCE_COLOR"in V&&(V.FORCE_COLOR==="true"?Be=1:V.FORCE_COLOR==="false"?Be=0:Be=V.FORCE_COLOR.length===0?1:Math.min(parseInt(V.FORCE_COLOR,10),3));function _r(i){return i===0?!1:{level:i,hasBasic:!0,has256:i>=2,has16m:i>=3}}function Er(i,e){if(Be===0)return 0;if(he("color=16m")||he("color=full")||he("color=truecolor"))return 3;if(he("color=256"))return 2;if(i&&!e&&Be===void 0)return 0;let t=Be||0;if(V.TERM==="dumb")return t;if(process.platform==="win32"){let r=Sa.release().split(".");return Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in V)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(r=>r in V)||V.CI_NAME==="codeship"?1:t;if("TEAMCITY_VERSION"in V)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(V.TEAMCITY_VERSION)?1:0;if(V.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in V){let r=parseInt((V.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(V.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(V.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(V.TERM)||"COLORTERM"in V?1:t}function wa(i){let e=Er(i,i&&i.isTTY);return _r(e)}Un.exports={supportsColor:wa,stdout:_r(Er(!0,Fn.isatty(1))),stderr:_r(Er(!0,Fn.isatty(2)))}});var Bn=P((H,Ai)=>{"use strict";var ba=N("tty"),ki=N("util");H.init=Ta;H.log=ka;H.formatArgs=Ca;H.save=Aa;H.load=Ra;H.useColors=xa;H.destroy=ki.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");H.colors=[6,2,3,4,5,1];try{let i=Sr();i&&(i.stderr||i).level>=2&&(H.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(i){}H.inspectOpts=Object.keys(process.env).filter(i=>/^debug_/i.test(i)).reduce((i,e)=>{let t=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),i[t]=r,i},{});function xa(){return"colors"in H.inspectOpts?!!H.inspectOpts.colors:ba.isatty(process.stderr.fd)}function Ca(i){let{namespace:e,useColors:t}=this;if(t){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;i[0]=s+i[0].split(`
`).join(`
`+s),i.push(n+"m+"+Ai.exports.humanize(this.diff)+"\x1B[0m")}else i[0]=Na()+e+" "+i[0]}function Na(){return H.inspectOpts.hideDate?"":new Date().toISOString()+" "}function ka(...i){return process.stderr.write(ki.formatWithOptions(H.inspectOpts,...i)+`
`)}function Aa(i){i?process.env.DEBUG=i:delete process.env.DEBUG}function Ra(){return process.env.DEBUG}function Ta(i){i.inspectOpts={};let e=Object.keys(H.inspectOpts);for(let t=0;t<e.length;t++)i.inspectOpts[e[t]]=H.inspectOpts[e[t]]}Ai.exports=vr()(H);var{formatters:Dn}=Ai.exports;Dn.o=function(i){return this.inspectOpts.colors=this.useColors,ki.inspect(i,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};Dn.O=function(i){return this.inspectOpts.colors=this.useColors,ki.inspect(i,this.inspectOpts)}});var Ye=P((sf,wr)=>{"use strict";typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?wr.exports=In():wr.exports=Bn()});var Kn=P((df,Gn)=>{"use strict";var mt=N("fs"),jn=N("url"),Pa=N("child_process").spawn;Gn.exports=Cr;Cr.XMLHttpRequest=Cr;function Cr(i){"use strict";i=i||{};var e=this,t=N("http"),r=N("https"),n,s,o={},c=!1,h={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},f=Object.assign({},h),a=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],u=["TRACE","TRACK","CONNECT"],d=!1,_=!1,b=!1,g={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.status=null,this.statusText=null;var w=function(p){return c||p&&a.indexOf(p.toLowerCase())===-1},T=function(p){return p&&u.indexOf(p)===-1};this.open=function(p,y,B,A,M){if(this.abort(),_=!1,b=!1,!T(p))throw new Error("SecurityError: Request method not allowed");o={method:p,url:y.toString(),async:typeof B!="boolean"?!0:B,user:A||null,password:M||null},x(this.OPENED)},this.setDisableHeaderCheck=function(p){c=p},this.setRequestHeader=function(p,y){if(this.readyState!=this.OPENED)throw new Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!w(p))return console.warn('Refused to set unsafe header "'+p+'"'),!1;if(d)throw new Error("INVALID_STATE_ERR: send flag is true");return f[p]=y,!0},this.getResponseHeader=function(p){return typeof p=="string"&&this.readyState>this.OPENED&&s.headers[p.toLowerCase()]&&!_?s.headers[p.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||_)return"";var p="";for(var y in s.headers)y!=="set-cookie"&&y!=="set-cookie2"&&(p+=y+": "+s.headers[y]+`\r
`);return p.substr(0,p.length-2)},this.getRequestHeader=function(p){return typeof p=="string"&&f[p]?f[p]:""},this.send=function(p){if(this.readyState!=this.OPENED)throw new Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(d)throw new Error("INVALID_STATE_ERR: send has already been called");var y=!1,B=!1,A=jn.parse(o.url),M;switch(A.protocol){case"https:":y=!0;case"http:":M=A.hostname;break;case"file:":B=!0;break;case void 0:case"":M="localhost";break;default:throw new Error("Protocol not supported.")}if(B){if(o.method!=="GET")throw new Error("XMLHttpRequest: Only GET method is supported");if(o.async)mt.readFile(unescape(A.pathname),"utf8",function(ce,nt){ce?e.handleError(ce,ce.errno||-1):(e.status=200,e.responseText=nt,x(e.DONE))});else try{this.responseText=mt.readFileSync(unescape(A.pathname),"utf8"),this.status=200,x(e.DONE)}catch(ce){this.handleError(ce,ce.errno||-1)}return}var q=A.port||(y?443:80),ee=A.pathname+(A.search?A.search:"");if(f.Host=M,y&&q===443||q===80||(f.Host+=":"+A.port),o.user){typeof o.password=="undefined"&&(o.password="");var Ve=new Buffer(o.user+":"+o.password);f.Authorization="Basic "+Ve.toString("base64")}o.method==="GET"||o.method==="HEAD"?p=null:p?(f["Content-Length"]=Buffer.isBuffer(p)?p.length:Buffer.byteLength(p),f["Content-Type"]||(f["Content-Type"]="text/plain;charset=UTF-8")):o.method==="POST"&&(f["Content-Length"]=0);var He=i.agent||!1,X={host:M,port:q,path:ee,method:o.method,headers:f,agent:He};if(y&&(X.pfx=i.pfx,X.key=i.key,X.passphrase=i.passphrase,X.cert=i.cert,X.ca=i.ca,X.ciphers=i.ciphers,X.rejectUnauthorized=i.rejectUnauthorized!==!1),_=!1,o.async){var Pe=y?r.request:t.request;d=!0,e.dispatchEvent("readystatechange");var _e=function(ce){if(s=ce,s.statusCode===302||s.statusCode===303||s.statusCode===307){o.url=s.headers.location;var nt=jn.parse(o.url);M=nt.hostname;var Le={hostname:nt.hostname,port:nt.port,path:nt.path,method:s.statusCode===303?"GET":o.method,headers:f};y&&(Le.pfx=i.pfx,Le.key=i.key,Le.passphrase=i.passphrase,Le.cert=i.cert,Le.ca=i.ca,Le.ciphers=i.ciphers,Le.rejectUnauthorized=i.rejectUnauthorized!==!1),n=Pe(Le,_e).on("error",hn),n.end();return}s&&s.setEncoding&&s.setEncoding("utf8"),x(e.HEADERS_RECEIVED),e.status=s.statusCode,s.on("data",function(si){si&&(e.responseText+=si),d&&x(e.LOADING)}),s.on("end",function(){d&&(d=!1,x(e.DONE))}),s.on("error",function(si){e.handleError(si)})},hn=function(ce){e.handleError(ce)};n=Pe(X,_e).on("error",hn),i.autoUnref&&n.on("socket",ce=>{ce.unref()}),p&&n.write(p),n.end(),e.dispatchEvent("loadstart")}else{var kt=".node-xmlhttprequest-content-"+process.pid,At=".node-xmlhttprequest-sync-"+process.pid;mt.writeFileSync(At,"","utf8");for(var Bo="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(y?"s":"")+".request;var options = "+JSON.stringify(X)+";var responseText = '';var req = doRequest(options, function(response) {response.setEncoding('utf8');response.on('data', function(chunk) {  responseText += chunk;});response.on('end', function() {fs.writeFileSync('"+kt+"', 'NODE-XMLHTTPREQUEST-STATUS:' + response.statusCode + ',' + responseText, 'utf8');fs.unlinkSync('"+At+"');});response.on('error', function(error) {fs.writeFileSync('"+kt+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+At+"');});}).on('error', function(error) {fs.writeFileSync('"+kt+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+At+"');});"+(p?"req.write('"+JSON.stringify(p).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",Mo=Pa(process.argv[0],["-e",Bo]),Pl;mt.existsSync(At););if(e.responseText=mt.readFileSync(kt,"utf8"),Mo.stdin.end(),mt.unlinkSync(kt),e.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var qo=e.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,"");e.handleError(qo,503)}else e.status=e.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1"),e.responseText=e.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"),x(e.DONE)}},this.handleError=function(p,y){this.status=y||0,this.statusText=p,this.responseText=p.stack,_=!0,x(this.DONE)},this.abort=function(){n&&(n.abort(),n=null),f=Object.assign({},h),this.responseText="",this.responseXML="",_=b=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||d)&&this.readyState!==this.DONE&&(d=!1,x(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(p,y){p in g||(g[p]=[]),g[p].push(y)},this.removeEventListener=function(p,y){p in g&&(g[p]=g[p].filter(function(B){return B!==y}))},this.dispatchEvent=function(p){if(typeof e["on"+p]=="function"&&(this.readyState===this.DONE?setImmediate(function(){e["on"+p]()}):e["on"+p]()),p in g)for(let y=0,B=g[p].length;y<B;y++)this.readyState===this.DONE?setImmediate(function(){g[p][y].call(e)}):g[p][y].call(e)};var x=function(p){if(!(e.readyState===p||e.readyState===e.UNSENT&&b)&&(e.readyState=p,(o.async||e.readyState<e.OPENED||e.readyState===e.DONE)&&e.dispatchEvent("readystatechange"),e.readyState===e.DONE)){let y;b?y="abort":_?y="error":y="load",e.dispatchEvent(y),e.dispatchEvent("loadend")}}}});var ts=P((bf,es)=>{"use strict";var{Duplex:Da}=N("stream");function Qn(i){i.emit("close")}function Ba(){!this.destroyed&&this._writableState.finished&&this.destroy()}function Zn(i){this.removeListener("error",Zn),this.destroy(),this.listenerCount("error")===0&&this.emit("error",i)}function Ma(i,e){let t=!0,r=new Da(R(E({},e),{autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1}));return i.on("message",function(s,o){let c=!o&&r._readableState.objectMode?s.toString():s;r.push(c)||i.pause()}),i.once("error",function(s){r.destroyed||(t=!1,r.destroy(s))}),i.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(n,s){if(i.readyState===i.CLOSED){s(n),process.nextTick(Qn,r);return}let o=!1;i.once("error",function(h){o=!0,s(h)}),i.once("close",function(){o||s(n),process.nextTick(Qn,r)}),t&&i.terminate()},r._final=function(n){if(i.readyState===i.CONNECTING){i.once("open",function(){r._final(n)});return}i._socket!==null&&(i._socket._writableState.finished?(n(),r._readableState.endEmitted&&r.destroy()):(i._socket.once("finish",function(){n()}),i.close()))},r._read=function(){i.isPaused&&i.resume()},r._write=function(n,s,o){if(i.readyState===i.CONNECTING){i.once("open",function(){r._write(n,s,o)});return}i.send(n,o)},r.on("end",Ba),r.on("error",Zn),r}es.exports=Ma});var qe=P((Cf,is)=>{"use strict";is.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var qt=P((Nf,Ii)=>{"use strict";var{EMPTY_BUFFER:qa}=qe();function Wa(i,e){if(i.length===0)return qa;if(i.length===1)return i[0];let t=Buffer.allocUnsafe(e),r=0;for(let n=0;n<i.length;n++){let s=i[n];t.set(s,r),r+=s.length}return r<e?t.slice(0,r):t}function rs(i,e,t,r,n){for(let s=0;s<n;s++)t[r+s]=i[s]^e[s&3]}function ns(i,e){for(let t=0;t<i.length;t++)i[t]^=e[t&3]}function $a(i){return i.byteLength===i.buffer.byteLength?i.buffer:i.buffer.slice(i.byteOffset,i.byteOffset+i.byteLength)}function Rr(i){if(Rr.readOnly=!0,Buffer.isBuffer(i))return i;let e;return i instanceof ArrayBuffer?e=Buffer.from(i):ArrayBuffer.isView(i)?e=Buffer.from(i.buffer,i.byteOffset,i.byteLength):(e=Buffer.from(i),Rr.readOnly=!1),e}Ii.exports={concat:Wa,mask:rs,toArrayBuffer:$a,toBuffer:Rr,unmask:ns};if(!process.env.WS_NO_BUFFER_UTIL)try{let i=N("bufferutil");Ii.exports.mask=function(e,t,r,n,s){s<48?rs(e,t,r,n,s):i.mask(e,t,r,n,s)},Ii.exports.unmask=function(e,t){e.length<32?ns(e,t):i.unmask(e,t)}}catch(i){}});var as=P((kf,os)=>{"use strict";var ss=Symbol("kDone"),Tr=Symbol("kRun"),Or=class{constructor(e){this[ss]=()=>{this.pending--,this[Tr]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Tr]()}[Tr](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[ss])}}};os.exports=Or});var zt=P((Af,fs)=>{"use strict";var Wt=N("zlib"),cs=qt(),za=as(),{kStatusCode:ls}=qe(),Va=Buffer.from([0,0,255,255]),Fi=Symbol("permessage-deflate"),Re=Symbol("total-length"),$t=Symbol("callback"),We=Symbol("buffers"),Li=Symbol("error"),Pi,Ir=class{constructor(e,t,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,!Pi){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;Pi=new za(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[$t];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(n=>!(t.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(t.serverMaxWindowBits===!1||typeof t.serverMaxWindowBits=="number"&&t.serverMaxWindowBits>n.server_max_window_bits)||typeof t.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof t.serverMaxWindowBits=="number"&&(r.server_max_window_bits=t.serverMaxWindowBits),typeof t.clientMaxWindowBits=="number"?r.client_max_window_bits=t.clientMaxWindowBits:(r.client_max_window_bits===!0||t.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(this._options.clientNoContextTakeover===!1&&t.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!t.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(t.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return t}normalizeParams(e){return e.forEach(t=>{Object.keys(t).forEach(r=>{let n=t[r];if(n.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(n=n[0],r==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else if(r==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else throw new Error(`Unknown parameter "${r}"`);t[r]=n})}),e}decompress(e,t,r){Pi.add(n=>{this._decompress(e,t,(s,o)=>{n(),r(s,o)})})}compress(e,t,r){Pi.add(n=>{this._compress(e,t,(s,o)=>{n(),r(s,o)})})}_decompress(e,t,r){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Wt.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=Wt.createInflateRaw(R(E({},this._options.zlibInflateOptions),{windowBits:o})),this._inflate[Fi]=this,this._inflate[Re]=0,this._inflate[We]=[],this._inflate.on("error",ja),this._inflate.on("data",hs)}this._inflate[$t]=r,this._inflate.write(e),t&&this._inflate.write(Va),this._inflate.flush(()=>{let s=this._inflate[Li];if(s){this._inflate.close(),this._inflate=null,r(s);return}let o=cs.concat(this._inflate[We],this._inflate[Re]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[Re]=0,this._inflate[We]=[],t&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,o)})}_compress(e,t,r){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Wt.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=Wt.createDeflateRaw(R(E({},this._options.zlibDeflateOptions),{windowBits:o})),this._deflate[Re]=0,this._deflate[We]=[],this._deflate.on("data",Ha)}this._deflate[$t]=r,this._deflate.write(e),this._deflate.flush(Wt.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=cs.concat(this._deflate[We],this._deflate[Re]);t&&(s=s.slice(0,s.length-4)),this._deflate[$t]=null,this._deflate[Re]=0,this._deflate[We]=[],t&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,s)})}};fs.exports=Ir;function Ha(i){this[We].push(i),this[Re]+=i.length}function hs(i){if(this[Re]+=i.length,this[Fi]._maxPayload<1||this[Re]<=this[Fi]._maxPayload){this[We].push(i);return}this[Li]=new RangeError("Max payload size exceeded"),this[Li].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[Li][ls]=1009,this.removeListener("data",hs),this.reset()}function ja(i){this[Fi]._inflate=null,i[ls]=1007,this[$t](i)}});var Vt=P((Tf,Pr)=>{"use strict";var Ga=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function Ka(i){return i>=1e3&&i<=1014&&i!==1004&&i!==1005&&i!==1006||i>=3e3&&i<=4999}function us(i){let e=i.length,t=0;for(;t<e;)if(!(i[t]&128))t++;else if((i[t]&224)===192){if(t+1===e||(i[t+1]&192)!==128||(i[t]&254)===192)return!1;t+=2}else if((i[t]&240)===224){if(t+2>=e||(i[t+1]&192)!==128||(i[t+2]&192)!==128||i[t]===224&&(i[t+1]&224)===128||i[t]===237&&(i[t+1]&224)===160)return!1;t+=3}else if((i[t]&248)===240){if(t+3>=e||(i[t+1]&192)!==128||(i[t+2]&192)!==128||(i[t+3]&192)!==128||i[t]===240&&(i[t+1]&240)===128||i[t]===244&&i[t+1]>143||i[t]>244)return!1;t+=4}else return!1;return!0}Pr.exports={isValidStatusCode:Ka,isValidUTF8:us,tokenChars:Ga};if(!process.env.WS_NO_UTF_8_VALIDATE)try{let i=N("utf-8-validate");Pr.exports.isValidUTF8=function(e){return e.length<150?us(e):i(e)}}catch(i){}});var Dr=P((Of,_s)=>{"use strict";var{Writable:Ya}=N("stream"),ds=zt(),{BINARY_TYPES:Ja,EMPTY_BUFFER:ps,kStatusCode:Xa,kWebSocket:Qa}=qe(),{concat:Lr,toArrayBuffer:Za,unmask:ec}=qt(),{isValidStatusCode:tc,isValidUTF8:ms}=Vt(),Ht=0,gs=1,ys=2,vs=3,Fr=4,ic=5,Ur=class extends Ya{constructor(e={}){super(),this._binaryType=e.binaryType||Ja[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[Qa]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._state=Ht,this._loop=!1}_write(e,t,r){if(this._opcode===8&&this._state==Ht)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=r.slice(e),r.slice(0,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=t.length-e;e>=r.length?t.set(this._buffers.shift(),n):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=r.slice(e)),e-=r.length}while(e>0);return t}startLoop(e){let t;this._loop=!0;do switch(this._state){case Ht:t=this.getInfo();break;case gs:t=this.getPayloadLength16();break;case ys:t=this.getPayloadLength64();break;case vs:this.getMask();break;case Fr:t=this.getData(e);break;default:this._loop=!1;return}while(this._loop);e(t)}getInfo(){if(this._bufferedBytes<2){this._loop=!1;return}let e=this.consume(2);if(e[0]&48)return this._loop=!1,W(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");let t=(e[0]&64)===64;if(t&&!this._extensions[ds.extensionName])return this._loop=!1,W(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._fin=(e[0]&128)===128,this._opcode=e[0]&15,this._payloadLength=e[1]&127,this._opcode===0){if(t)return this._loop=!1,W(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(!this._fragmented)return this._loop=!1,W(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented)return this._loop=!1,W(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");this._compressed=t}else if(this._opcode>7&&this._opcode<11){if(!this._fin)return this._loop=!1,W(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");if(t)return this._loop=!1,W(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._payloadLength>125)return this._loop=!1,W(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH")}else return this._loop=!1,W(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(e[1]&128)===128,this._isServer){if(!this._masked)return this._loop=!1,W(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK")}else if(this._masked)return this._loop=!1,W(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");if(this._payloadLength===126)this._state=gs;else if(this._payloadLength===127)this._state=ys;else return this.haveLength()}getPayloadLength16(){if(this._bufferedBytes<2){this._loop=!1;return}return this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength()}getPayloadLength64(){if(this._bufferedBytes<8){this._loop=!1;return}let e=this.consume(8),t=e.readUInt32BE(0);return t>Math.pow(2,21)-1?(this._loop=!1,W(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH")):(this._payloadLength=t*Math.pow(2,32)+e.readUInt32BE(4),this.haveLength())}haveLength(){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return this._loop=!1,W(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");this._masked?this._state=vs:this._state=Fr}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Fr}getData(e){let t=ps;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&ec(t,this._mask)}if(this._opcode>7)return this.controlMessage(t);if(this._compressed){this._state=ic,this.decompress(t,e);return}return t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage()}decompress(e,t){this._extensions[ds.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return t(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return t(W(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(s)}let o=this.dataMessage();if(o)return t(o);this.startLoop(t)})}dataMessage(){if(this._fin){let e=this._messageLength,t=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let r;this._binaryType==="nodebuffer"?r=Lr(t,e):this._binaryType==="arraybuffer"?r=Za(Lr(t,e)):r=t,this.emit("message",r,!0)}else{let r=Lr(t,e);if(!this._skipUTF8Validation&&!ms(r))return this._loop=!1,W(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("message",r,!1)}}this._state=Ht}controlMessage(e){if(this._opcode===8)if(this._loop=!1,e.length===0)this.emit("conclude",1005,ps),this.end();else{if(e.length===1)return W(RangeError,"invalid payload length 1",!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");{let t=e.readUInt16BE(0);if(!tc(t))return W(RangeError,`invalid status code ${t}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");let r=e.slice(2);if(!this._skipUTF8Validation&&!ms(r))return W(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("conclude",t,r),this.end()}}else this._opcode===9?this.emit("ping",e):this.emit("pong",e);this._state=Ht}};_s.exports=Ur;function W(i,e,t,r,n){let s=new i(t?`Invalid WebSocket frame: ${e}`:e);return Error.captureStackTrace(s,W),s.code=n,s[Xa]=r,s}});var Mr=P((Lf,ws)=>{"use strict";var If=N("net"),Pf=N("tls"),{randomFillSync:rc}=N("crypto"),Es=zt(),{EMPTY_BUFFER:nc}=qe(),{isValidStatusCode:sc}=Vt(),{mask:Ss,toBuffer:gt}=qt(),me=Symbol("kByteLength"),oc=Buffer.alloc(4),Br=class i{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let r,n=!1,s=2,o=!1;t.mask&&(r=t.maskBuffer||oc,t.generateMask?t.generateMask(r):rc(r,0,4),o=(r[0]|r[1]|r[2]|r[3])===0,s=6);let c;typeof e=="string"?(!t.mask||o)&&t[me]!==void 0?c=t[me]:(e=Buffer.from(e),c=e.length):(c=e.length,n=t.mask&&t.readOnly&&!o);let h=c;c>=65536?(s+=8,h=127):c>125&&(s+=2,h=126);let f=Buffer.allocUnsafe(n?c+s:s);return f[0]=t.fin?t.opcode|128:t.opcode,t.rsv1&&(f[0]|=64),f[1]=h,h===126?f.writeUInt16BE(c,2):h===127&&(f[2]=f[3]=0,f.writeUIntBE(c,4,6)),t.mask?(f[1]|=128,f[s-4]=r[0],f[s-3]=r[1],f[s-2]=r[2],f[s-1]=r[3],o?[f,e]:n?(Ss(e,r,f,s,c),[f]):(Ss(e,r,e,0,c),[f,e])):[f,e]}close(e,t,r,n){let s;if(e===void 0)s=nc;else{if(typeof e!="number"||!sc(e))throw new TypeError("First argument must be a valid error code number");if(t===void 0||!t.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let c=Buffer.byteLength(t);if(c>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+c),s.writeUInt16BE(e,0),typeof t=="string"?s.write(t,2):s.set(t,2)}}let o={[me]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(i.frame(s,o),n)}ping(e,t,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=gt(e),n=e.length,s=gt.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[me]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(i.frame(e,o),r)}pong(e,t,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=gt(e),n=e.length,s=gt.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[me]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(i.frame(e,o),r)}send(e,t,r){let n=this._extensions[Es.extensionName],s=t.binary?2:1,o=t.compress,c,h;if(typeof e=="string"?(c=Buffer.byteLength(e),h=!1):(e=gt(e),c=e.length,h=gt.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=c>=n._threshold),this._compress=o):(o=!1,s=0),t.fin&&(this._firstFragment=!0),n){let f={[me]:c,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:h,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,f,r]):this.dispatch(e,this._compress,f,r)}else this.sendFrame(i.frame(e,{[me]:c,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:h,rsv1:!1}),r)}dispatch(e,t,r,n){if(!t){this.sendFrame(i.frame(e,r),n);return}let s=this._extensions[Es.extensionName];this._bufferedBytes+=r[me],this._deflating=!0,s.compress(e,r.fin,(o,c)=>{if(this._socket.destroyed){let h=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(h);for(let f=0;f<this._queue.length;f++){let a=this._queue[f],u=a[a.length-1];typeof u=="function"&&u(h)}return}this._bufferedBytes-=r[me],this._deflating=!1,r.readOnly=!1,this.sendFrame(i.frame(c,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][me],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][me],this._queue.push(e)}sendFrame(e,t){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}};ws.exports=Br});var Os=P((Ff,Ts)=>{"use strict";var{kForOnEventAttribute:jt,kListener:qr}=qe(),bs=Symbol("kCode"),xs=Symbol("kData"),Cs=Symbol("kError"),Ns=Symbol("kMessage"),ks=Symbol("kReason"),yt=Symbol("kTarget"),As=Symbol("kType"),Rs=Symbol("kWasClean"),Te=class{constructor(e){this[yt]=null,this[As]=e}get target(){return this[yt]}get type(){return this[As]}};Object.defineProperty(Te.prototype,"target",{enumerable:!0});Object.defineProperty(Te.prototype,"type",{enumerable:!0});var Je=class extends Te{constructor(e,t={}){super(e),this[bs]=t.code===void 0?0:t.code,this[ks]=t.reason===void 0?"":t.reason,this[Rs]=t.wasClean===void 0?!1:t.wasClean}get code(){return this[bs]}get reason(){return this[ks]}get wasClean(){return this[Rs]}};Object.defineProperty(Je.prototype,"code",{enumerable:!0});Object.defineProperty(Je.prototype,"reason",{enumerable:!0});Object.defineProperty(Je.prototype,"wasClean",{enumerable:!0});var vt=class extends Te{constructor(e,t={}){super(e),this[Cs]=t.error===void 0?null:t.error,this[Ns]=t.message===void 0?"":t.message}get error(){return this[Cs]}get message(){return this[Ns]}};Object.defineProperty(vt.prototype,"error",{enumerable:!0});Object.defineProperty(vt.prototype,"message",{enumerable:!0});var Gt=class extends Te{constructor(e,t={}){super(e),this[xs]=t.data===void 0?null:t.data}get data(){return this[xs]}};Object.defineProperty(Gt.prototype,"data",{enumerable:!0});var ac={addEventListener(i,e,t={}){for(let n of this.listeners(i))if(!t[jt]&&n[qr]===e&&!n[jt])return;let r;if(i==="message")r=function(s,o){let c=new Gt("message",{data:o?s:s.toString()});c[yt]=this,Ui(e,this,c)};else if(i==="close")r=function(s,o){let c=new Je("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});c[yt]=this,Ui(e,this,c)};else if(i==="error")r=function(s){let o=new vt("error",{error:s,message:s.message});o[yt]=this,Ui(e,this,o)};else if(i==="open")r=function(){let s=new Te("open");s[yt]=this,Ui(e,this,s)};else return;r[jt]=!!t[jt],r[qr]=e,t.once?this.once(i,r):this.on(i,r)},removeEventListener(i,e){for(let t of this.listeners(i))if(t[qr]===e&&!t[jt]){this.removeListener(i,t);break}}};Ts.exports={CloseEvent:Je,ErrorEvent:vt,Event:Te,EventTarget:ac,MessageEvent:Gt};function Ui(i,e,t){typeof i=="object"&&i.handleEvent?i.handleEvent.call(i,t):i.call(e,t)}});var Wr=P((Uf,Is)=>{"use strict";var{tokenChars:Kt}=Vt();function we(i,e,t){i[e]===void 0?i[e]=[t]:i[e].push(t)}function cc(i){let e=Object.create(null),t=Object.create(null),r=!1,n=!1,s=!1,o,c,h=-1,f=-1,a=-1,u=0;for(;u<i.length;u++)if(f=i.charCodeAt(u),o===void 0)if(a===-1&&Kt[f]===1)h===-1&&(h=u);else if(u!==0&&(f===32||f===9))a===-1&&h!==-1&&(a=u);else if(f===59||f===44){if(h===-1)throw new SyntaxError(`Unexpected character at index ${u}`);a===-1&&(a=u);let _=i.slice(h,a);f===44?(we(e,_,t),t=Object.create(null)):o=_,h=a=-1}else throw new SyntaxError(`Unexpected character at index ${u}`);else if(c===void 0)if(a===-1&&Kt[f]===1)h===-1&&(h=u);else if(f===32||f===9)a===-1&&h!==-1&&(a=u);else if(f===59||f===44){if(h===-1)throw new SyntaxError(`Unexpected character at index ${u}`);a===-1&&(a=u),we(t,i.slice(h,a),!0),f===44&&(we(e,o,t),t=Object.create(null),o=void 0),h=a=-1}else if(f===61&&h!==-1&&a===-1)c=i.slice(h,u),h=a=-1;else throw new SyntaxError(`Unexpected character at index ${u}`);else if(n){if(Kt[f]!==1)throw new SyntaxError(`Unexpected character at index ${u}`);h===-1?h=u:r||(r=!0),n=!1}else if(s)if(Kt[f]===1)h===-1&&(h=u);else if(f===34&&h!==-1)s=!1,a=u;else if(f===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${u}`);else if(f===34&&i.charCodeAt(u-1)===61)s=!0;else if(a===-1&&Kt[f]===1)h===-1&&(h=u);else if(h!==-1&&(f===32||f===9))a===-1&&(a=u);else if(f===59||f===44){if(h===-1)throw new SyntaxError(`Unexpected character at index ${u}`);a===-1&&(a=u);let _=i.slice(h,a);r&&(_=_.replace(/\\/g,""),r=!1),we(t,c,_),f===44&&(we(e,o,t),t=Object.create(null),o=void 0),c=void 0,h=a=-1}else throw new SyntaxError(`Unexpected character at index ${u}`);if(h===-1||s||f===32||f===9)throw new SyntaxError("Unexpected end of input");a===-1&&(a=u);let d=i.slice(h,a);return o===void 0?we(e,d,t):(c===void 0?we(t,d,!0):r?we(t,c,d.replace(/\\/g,"")):we(t,c,d),we(e,o,t)),e}function lc(i){return Object.keys(i).map(e=>{let t=i[e];return Array.isArray(t)||(t=[t]),t.map(r=>[e].concat(Object.keys(r).map(n=>{let s=r[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}Is.exports={format:lc,parse:cc}});var jr=P((Bf,zs)=>{"use strict";var hc=N("events"),fc=N("https"),uc=N("http"),Fs=N("net"),dc=N("tls"),{randomBytes:pc,createHash:mc}=N("crypto"),{Readable:Df}=N("stream"),{URL:$r}=N("url"),$e=zt(),gc=Dr(),yc=Mr(),{BINARY_TYPES:Ps,EMPTY_BUFFER:Di,GUID:vc,kForOnEventAttribute:zr,kListener:_c,kStatusCode:Ec,kWebSocket:J,NOOP:Us}=qe(),{EventTarget:{addEventListener:Sc,removeEventListener:wc}}=Os(),{format:bc,parse:xc}=Wr(),{toBuffer:Cc}=qt(),Nc=30*1e3,Ds=Symbol("kAborted"),Vr=[8,13],Oe=["CONNECTING","OPEN","CLOSING","CLOSED"],kc=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,D=class i extends hc{constructor(e,t,r){super(),this._binaryType=Ps[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=Di,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=i.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,t===void 0?t=[]:Array.isArray(t)||(typeof t=="object"&&t!==null?(r=t,t=[]):t=[t]),Bs(this,e,t,r)):this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(e){Ps.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let n=new gc({binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new yc(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[J]=this,e[J]=this,n.on("conclude",Tc),n.on("drain",Oc),n.on("error",Ic),n.on("message",Pc),n.on("ping",Lc),n.on("pong",Fc),e.setTimeout(0),e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",qs),e.on("data",Mi),e.on("end",Ws),e.on("error",$s),this._readyState=i.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=i.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[$e.extensionName]&&this._extensions[$e.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=i.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==i.CLOSED){if(this.readyState===i.CONNECTING)return ne(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===i.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=i.CLOSING,this._sender.close(e,t,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),Nc)}}pause(){this.readyState===i.CONNECTING||this.readyState===i.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=t=void 0):typeof t=="function"&&(r=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){Hr(this,e,r);return}t===void 0&&(t=!this._isServer),this._sender.ping(e||Di,t,r)}pong(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=t=void 0):typeof t=="function"&&(r=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){Hr(this,e,r);return}t===void 0&&(t=!this._isServer),this._sender.pong(e||Di,t,r)}resume(){this.readyState===i.CONNECTING||this.readyState===i.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"&&(r=t,t={}),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){Hr(this,e,r);return}let n=E({binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0},t);this._extensions[$e.extensionName]||(n.compress=!1),this._sender.send(e||Di,n,r)}terminate(){if(this.readyState!==i.CLOSED){if(this.readyState===i.CONNECTING)return ne(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=i.CLOSING,this._socket.destroy())}}};Object.defineProperty(D,"CONNECTING",{enumerable:!0,value:Oe.indexOf("CONNECTING")});Object.defineProperty(D.prototype,"CONNECTING",{enumerable:!0,value:Oe.indexOf("CONNECTING")});Object.defineProperty(D,"OPEN",{enumerable:!0,value:Oe.indexOf("OPEN")});Object.defineProperty(D.prototype,"OPEN",{enumerable:!0,value:Oe.indexOf("OPEN")});Object.defineProperty(D,"CLOSING",{enumerable:!0,value:Oe.indexOf("CLOSING")});Object.defineProperty(D.prototype,"CLOSING",{enumerable:!0,value:Oe.indexOf("CLOSING")});Object.defineProperty(D,"CLOSED",{enumerable:!0,value:Oe.indexOf("CLOSED")});Object.defineProperty(D.prototype,"CLOSED",{enumerable:!0,value:Oe.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(i=>{Object.defineProperty(D.prototype,i,{enumerable:!0})});["open","error","close","message"].forEach(i=>{Object.defineProperty(D.prototype,`on${i}`,{enumerable:!0,get(){for(let e of this.listeners(i))if(e[zr])return e[_c];return null},set(e){for(let t of this.listeners(i))if(t[zr]){this.removeListener(i,t);break}typeof e=="function"&&this.addEventListener(i,e,{[zr]:!0})}})});D.prototype.addEventListener=Sc;D.prototype.removeEventListener=wc;zs.exports=D;function Bs(i,e,t,r){let n=R(E({protocolVersion:Vr[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10},r),{createConnection:void 0,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0});if(!Vr.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${Vr.join(", ")})`);let s;if(e instanceof $r)s=e,i._url=e.href;else{try{s=new $r(e)}catch(g){throw new SyntaxError(`Invalid URL: ${e}`)}i._url=e}let o=s.protocol==="wss:",c=s.protocol==="ws+unix:",h;if(s.protocol!=="ws:"&&!o&&!c?h=`The URL's protocol must be one of "ws:", "wss:", or "ws+unix:"`:c&&!s.pathname?h="The URL's pathname is empty":s.hash&&(h="The URL contains a fragment identifier"),h){let g=new SyntaxError(h);if(i._redirects===0)throw g;Bi(i,g);return}let f=o?443:80,a=pc(16).toString("base64"),u=o?fc.request:uc.request,d=new Set,_;if(n.createConnection=o?Rc:Ac,n.defaultPort=n.defaultPort||f,n.port=s.port||f,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers=R(E({},n.headers),{"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":a,Connection:"Upgrade",Upgrade:"websocket"}),n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(_=new $e(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=bc({[$e.extensionName]:_.offer()})),t.length){for(let g of t){if(typeof g!="string"||!kc.test(g)||d.has(g))throw new SyntaxError("An invalid or duplicated subprotocol was specified");d.add(g)}n.headers["Sec-WebSocket-Protocol"]=t.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),c){let g=n.path.split(":");n.socketPath=g[0],n.path=g[1]}let b;if(n.followRedirects){if(i._redirects===0){i._originalIpc=c,i._originalSecure=o,i._originalHostOrSocketPath=c?n.socketPath:s.host;let g=r&&r.headers;if(r=R(E({},r),{headers:{}}),g)for(let[w,T]of Object.entries(g))r.headers[w.toLowerCase()]=T}else if(i.listenerCount("redirect")===0){let g=c?i._originalIpc?n.socketPath===i._originalHostOrSocketPath:!1:i._originalIpc?!1:s.host===i._originalHostOrSocketPath;(!g||i._originalSecure&&!o)&&(delete n.headers.authorization,delete n.headers.cookie,g||delete n.headers.host,n.auth=void 0)}n.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(n.auth).toString("base64")),b=i._req=u(n),i._redirects&&i.emit("redirect",i.url,b)}else b=i._req=u(n);n.timeout&&b.on("timeout",()=>{ne(i,b,"Opening handshake has timed out")}),b.on("error",g=>{b===null||b[Ds]||(b=i._req=null,Bi(i,g))}),b.on("response",g=>{let w=g.headers.location,T=g.statusCode;if(w&&n.followRedirects&&T>=300&&T<400){if(++i._redirects>n.maxRedirects){ne(i,b,"Maximum redirects exceeded");return}b.abort();let x;try{x=new $r(w,e)}catch(p){let y=new SyntaxError(`Invalid URL: ${w}`);Bi(i,y);return}Bs(i,x,t,r)}else i.emit("unexpected-response",b,g)||ne(i,b,`Unexpected server response: ${g.statusCode}`)}),b.on("upgrade",(g,w,T)=>{if(i.emit("upgrade",g),i.readyState!==D.CONNECTING)return;if(b=i._req=null,g.headers.upgrade.toLowerCase()!=="websocket"){ne(i,w,"Invalid Upgrade header");return}let x=mc("sha1").update(a+vc).digest("base64");if(g.headers["sec-websocket-accept"]!==x){ne(i,w,"Invalid Sec-WebSocket-Accept header");return}let p=g.headers["sec-websocket-protocol"],y;if(p!==void 0?d.size?d.has(p)||(y="Server sent an invalid subprotocol"):y="Server sent a subprotocol but none was requested":d.size&&(y="Server sent no subprotocol"),y){ne(i,w,y);return}p&&(i._protocol=p);let B=g.headers["sec-websocket-extensions"];if(B!==void 0){if(!_){ne(i,w,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let A;try{A=xc(B)}catch(q){ne(i,w,"Invalid Sec-WebSocket-Extensions header");return}let M=Object.keys(A);if(M.length!==1||M[0]!==$e.extensionName){ne(i,w,"Server indicated an extension that was not requested");return}try{_.accept(A[$e.extensionName])}catch(q){ne(i,w,"Invalid Sec-WebSocket-Extensions header");return}i._extensions[$e.extensionName]=_}i.setSocket(w,T,{generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})}),b.end()}function Bi(i,e){i._readyState=D.CLOSING,i.emit("error",e),i.emitClose()}function Ac(i){return i.path=i.socketPath,Fs.connect(i)}function Rc(i){return i.path=void 0,!i.servername&&i.servername!==""&&(i.servername=Fs.isIP(i.host)?"":i.host),dc.connect(i)}function ne(i,e,t){i._readyState=D.CLOSING;let r=new Error(t);Error.captureStackTrace(r,ne),e.setHeader?(e[Ds]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(Bi,i,r)):(e.destroy(r),e.once("error",i.emit.bind(i,"error")),e.once("close",i.emitClose.bind(i)))}function Hr(i,e,t){if(e){let r=Cc(e).length;i._socket?i._sender._bufferedBytes+=r:i._bufferedAmount+=r}if(t){let r=new Error(`WebSocket is not open: readyState ${i.readyState} (${Oe[i.readyState]})`);t(r)}}function Tc(i,e){let t=this[J];t._closeFrameReceived=!0,t._closeMessage=e,t._closeCode=i,t._socket[J]!==void 0&&(t._socket.removeListener("data",Mi),process.nextTick(Ms,t._socket),i===1005?t.close():t.close(i,e))}function Oc(){let i=this[J];i.isPaused||i._socket.resume()}function Ic(i){let e=this[J];e._socket[J]!==void 0&&(e._socket.removeListener("data",Mi),process.nextTick(Ms,e._socket),e.close(i[Ec])),e.emit("error",i)}function Ls(){this[J].emitClose()}function Pc(i,e){this[J].emit("message",i,e)}function Lc(i){let e=this[J];e.pong(i,!e._isServer,Us),e.emit("ping",i)}function Fc(i){this[J].emit("pong",i)}function Ms(i){i.resume()}function qs(){let i=this[J];this.removeListener("close",qs),this.removeListener("data",Mi),this.removeListener("end",Ws),i._readyState=D.CLOSING;let e;!this._readableState.endEmitted&&!i._closeFrameReceived&&!i._receiver._writableState.errorEmitted&&(e=i._socket.read())!==null&&i._receiver.write(e),i._receiver.end(),this[J]=void 0,clearTimeout(i._closeTimer),i._receiver._writableState.finished||i._receiver._writableState.errorEmitted?i.emitClose():(i._receiver.on("error",Ls),i._receiver.on("finish",Ls))}function Mi(i){this[J]._receiver.write(i)||this.pause()}function Ws(){let i=this[J];i._readyState=D.CLOSING,i._receiver.end(),this.end()}function $s(){let i=this[J];this.removeListener("error",$s),this.on("error",Us),i&&(i._readyState=D.CLOSING,this.destroy())}});var Hs=P((qf,Vs)=>{"use strict";var{tokenChars:Uc}=Vt();function Dc(i){let e=new Set,t=-1,r=-1,n=0;for(n;n<i.length;n++){let o=i.charCodeAt(n);if(r===-1&&Uc[o]===1)t===-1&&(t=n);else if(n!==0&&(o===32||o===9))r===-1&&t!==-1&&(r=n);else if(o===44){if(t===-1)throw new SyntaxError(`Unexpected character at index ${n}`);r===-1&&(r=n);let c=i.slice(t,r);if(e.has(c))throw new SyntaxError(`The "${c}" subprotocol is duplicated`);e.add(c),t=r=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(t===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let s=i.slice(t,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Vs.exports={parse:Dc}});var Qs=P((Vf,Xs)=>{"use strict";var Bc=N("events"),qi=N("http"),Wf=N("https"),$f=N("net"),zf=N("tls"),{createHash:Mc}=N("crypto"),js=Wr(),Xe=zt(),qc=Hs(),Wc=jr(),{GUID:$c,kWebSocket:zc}=qe(),Vc=/^[+/0-9A-Za-z]{22}==$/,Gs=0,Ks=1,Js=2,Gr=class extends Bc{constructor(e,t){if(super(),e=E({maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:Wc},e),e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=qi.createServer((r,n)=>{let s=qi.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=Hc(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Gs}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===Js){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(Yt,this);return}if(e&&this.once("close",e),this._state!==Ks)if(this._state=Ks,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(Yt,this):process.nextTick(Yt,this);else{let t=this._server;this._removeListeners(),this._removeListeners=this._server=null,t.close(()=>{Yt(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((t!==-1?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,n){t.on("error",Ys);let s=e.headers["sec-websocket-key"],o=+e.headers["sec-websocket-version"];if(e.method!=="GET"){Qe(this,e,t,405,"Invalid HTTP method");return}if(e.headers.upgrade.toLowerCase()!=="websocket"){Qe(this,e,t,400,"Invalid Upgrade header");return}if(!s||!Vc.test(s)){Qe(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(o!==8&&o!==13){Qe(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){Jt(t,400);return}let c=e.headers["sec-websocket-protocol"],h=new Set;if(c!==void 0)try{h=qc.parse(c)}catch(u){Qe(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],a={};if(this.options.perMessageDeflate&&f!==void 0){let u=new Xe(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let d=js.parse(f);d[Xe.extensionName]&&(u.accept(d[Xe.extensionName]),a[Xe.extensionName]=u)}catch(d){Qe(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let u={origin:e.headers[`${o===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(u,(d,_,b,g)=>{if(!d)return Jt(t,_||401,b,g);this.completeUpgrade(a,s,h,e,t,r,n)});return}if(!this.options.verifyClient(u))return Jt(t,401)}this.completeUpgrade(a,s,h,e,t,r,n)}completeUpgrade(e,t,r,n,s,o,c){if(!s.readable||!s.writable)return s.destroy();if(s[zc])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Gs)return Jt(s,503);let f=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${Mc("sha1").update(t+$c).digest("base64")}`],a=new this.options.WebSocket(null);if(r.size){let u=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;u&&(f.push(`Sec-WebSocket-Protocol: ${u}`),a._protocol=u)}if(e[Xe.extensionName]){let u=e[Xe.extensionName].params,d=js.format({[Xe.extensionName]:[u]});f.push(`Sec-WebSocket-Extensions: ${d}`),a._extensions=e}this.emit("headers",f,n),s.write(f.concat(`\r
`).join(`\r
`)),s.removeListener("error",Ys),a.setSocket(s,o,{maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(a),a.on("close",()=>{this.clients.delete(a),this._shouldEmitClose&&!this.clients.size&&process.nextTick(Yt,this)})),c(a,n)}};Xs.exports=Gr;function Hc(i,e){for(let t of Object.keys(e))i.on(t,e[t]);return function(){for(let r of Object.keys(e))i.removeListener(r,e[r])}}function Yt(i){i._state=Js,i.emit("close")}function Ys(){this.destroy()}function Jt(i,e,t,r){t=t||qi.STATUS_CODES[e],r=E({Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(t)},r),i.once("finish",i.destroy),i.end(`HTTP/1.1 ${e} ${qi.STATUS_CODES[e]}\r
`+Object.keys(r).map(n=>`${n}: ${r[n]}`).join(`\r
`)+`\r
\r
`+t)}function Qe(i,e,t,r,n){if(i.listenerCount("wsClientError")){let s=new Error(n);Error.captureStackTrace(s,Qe),i.emit("wsClientError",s,t,e)}else Jt(t,r,n)}});var Yr=P((Nu,oo)=>{"use strict";function tl(i){t.debug=t,t.default=t,t.coerce=h,t.disable=s,t.enable=n,t.enabled=o,t.humanize=yr(),t.destroy=f,Object.keys(i).forEach(a=>{t[a]=i[a]}),t.names=[],t.skips=[],t.formatters={};function e(a){let u=0;for(let d=0;d<a.length;d++)u=(u<<5)-u+a.charCodeAt(d),u|=0;return t.colors[Math.abs(u)%t.colors.length]}t.selectColor=e;function t(a){let u,d=null,_,b;function g(...w){if(!g.enabled)return;let T=g,x=Number(new Date),p=x-(u||x);T.diff=p,T.prev=u,T.curr=x,u=x,w[0]=t.coerce(w[0]),typeof w[0]!="string"&&w.unshift("%O");let y=0;w[0]=w[0].replace(/%([a-zA-Z%])/g,(A,M)=>{if(A==="%%")return"%";y++;let q=t.formatters[M];if(typeof q=="function"){let ee=w[y];A=q.call(T,ee),w.splice(y,1),y--}return A}),t.formatArgs.call(T,w),(T.log||t.log).apply(T,w)}return g.namespace=a,g.useColors=t.useColors(),g.color=t.selectColor(a),g.extend=r,g.destroy=t.destroy,Object.defineProperty(g,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(_!==t.namespaces&&(_=t.namespaces,b=t.enabled(a)),b),set:w=>{d=w}}),typeof t.init=="function"&&t.init(g),g}function r(a,u){let d=t(this.namespace+(typeof u=="undefined"?":":u)+a);return d.log=this.log,d}function n(a){t.save(a),t.namespaces=a,t.names=[],t.skips=[];let u,d=(typeof a=="string"?a:"").split(/[\s,]+/),_=d.length;for(u=0;u<_;u++)d[u]&&(a=d[u].replace(/\*/g,".*?"),a[0]==="-"?t.skips.push(new RegExp("^"+a.slice(1)+"$")):t.names.push(new RegExp("^"+a+"$")))}function s(){let a=[...t.names.map(c),...t.skips.map(c).map(u=>"-"+u)].join(",");return t.enable(""),a}function o(a){if(a[a.length-1]==="*")return!0;let u,d;for(u=0,d=t.skips.length;u<d;u++)if(t.skips[u].test(a))return!1;for(u=0,d=t.names.length;u<d;u++)if(t.names[u].test(a))return!0;return!1}function c(a){return a.toString().substring(2,a.toString().length-2).replace(/\.\*\?$/,"*")}function h(a){return a instanceof Error?a.stack||a.message:a}function f(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}oo.exports=tl});var ao=P((se,Vi)=>{"use strict";se.formatArgs=rl;se.save=nl;se.load=sl;se.useColors=il;se.storage=ol();se.destroy=(()=>{let i=!1;return()=>{i||(i=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();se.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function il(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function rl(i){if(i[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+i[0]+(this.useColors?"%c ":" ")+"+"+Vi.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;i.splice(1,0,e,"color: inherit");let t=0,r=0;i[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(t++,n==="%c"&&(r=t))}),i.splice(r,0,e)}se.log=console.debug||console.log||(()=>{});function nl(i){try{i?se.storage.setItem("debug",i):se.storage.removeItem("debug")}catch(e){}}function sl(){let i;try{i=se.storage.getItem("debug")}catch(e){}return!i&&typeof process!="undefined"&&"env"in process&&(i=process.env.DEBUG),i}function ol(){try{return localStorage}catch(i){}}Vi.exports=Yr()(se);var{formatters:al}=Vi.exports;al.j=function(i){try{return JSON.stringify(i)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var lo=P((j,ji)=>{"use strict";var cl=N("tty"),Hi=N("util");j.init=ml;j.log=ul;j.formatArgs=hl;j.save=dl;j.load=pl;j.useColors=ll;j.destroy=Hi.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");j.colors=[6,2,3,4,5,1];try{let i=Sr();i&&(i.stderr||i).level>=2&&(j.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(i){}j.inspectOpts=Object.keys(process.env).filter(i=>/^debug_/i.test(i)).reduce((i,e)=>{let t=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),i[t]=r,i},{});function ll(){return"colors"in j.inspectOpts?!!j.inspectOpts.colors:cl.isatty(process.stderr.fd)}function hl(i){let{namespace:e,useColors:t}=this;if(t){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;i[0]=s+i[0].split(`
`).join(`
`+s),i.push(n+"m+"+ji.exports.humanize(this.diff)+"\x1B[0m")}else i[0]=fl()+e+" "+i[0]}function fl(){return j.inspectOpts.hideDate?"":new Date().toISOString()+" "}function ul(...i){return process.stderr.write(Hi.formatWithOptions(j.inspectOpts,...i)+`
`)}function dl(i){i?process.env.DEBUG=i:delete process.env.DEBUG}function pl(){return process.env.DEBUG}function ml(i){i.inspectOpts={};let e=Object.keys(j.inspectOpts);for(let t=0;t<e.length;t++)i.inspectOpts[e[t]]=j.inspectOpts[e[t]]}ji.exports=Yr()(j);var{formatters:co}=ji.exports;co.o=function(i){return this.inspectOpts.colors=this.useColors,Hi.inspect(i,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};co.O=function(i){return this.inspectOpts.colors=this.useColors,Hi.inspect(i,this.inspectOpts)}});var Zt=P((ku,Jr)=>{"use strict";typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?Jr.exports=ao():Jr.exports=lo()});var Yo="v1",Jo=`${PACKAGE_NAME}@${PACKAGE_VERSION}`,ci=class{constructor(e={}){this.DEFAULT_BACKEND_URL=typeof window!="undefined"&&window.NOVU_LOCAL_BACKEND_URL||"https://api.novu.co";let{apiVersion:t=Yo,apiUrl:r=this.DEFAULT_BACKEND_URL,userAgent:n=Jo,headers:s={}}=e||{};this.apiVersion=t,this.apiUrl=`${r}/${t}`,this.headers=E({"Novu-API-Version":NOVU_API_VERSION,"Content-Type":"application/json","User-Agent":n},s)}setAuthorizationToken(e){this.headers.Authorization=`Bearer ${e}`}setKeylessHeader(e){var r;let t=e||typeof window!="undefined"&&((r=window.localStorage)==null?void 0:r.getItem("novu_keyless_application_identifier"));!t||!t.startsWith("pk_keyless_")||(this.headers["Novu-Application-Identifier"]=t)}setHeaders(e){this.headers=E(E({},this.headers),e)}get(e,t,r=!0){return m(this,null,function*(){return this.doFetch({path:e,searchParams:t,options:{method:"GET"},unwrapEnvelope:r})})}post(e,t,r){return m(this,null,function*(){return this.doFetch({path:e,options:{method:"POST",body:t,headers:r==null?void 0:r.headers}})})}patch(e,t){return m(this,null,function*(){return this.doFetch({path:e,options:{method:"PATCH",body:t}})})}delete(e,t){return m(this,null,function*(){return this.doFetch({path:e,options:{method:"DELETE",body:t}})})}doFetch(s){return m(this,arguments,function*({path:e,searchParams:t,options:r,unwrapEnvelope:n=!0}){let o=Xo(this.apiUrl,e,t?`?${t.toString()}`:""),c={method:(r==null?void 0:r.method)||"GET",headers:E(E({},this.headers),(r==null?void 0:r.headers)||{}),body:r!=null&&r.body?JSON.stringify(r.body):void 0},h=yield fetch(o,c);if(!h.ok){let a=yield h.json();throw new Error(`${this.headers["User-Agent"]} error. Status: ${h.status}, Message: ${a.message}`)}if(h.status===204)return;let f=yield h.json();return n?f.data:f})}};function Xo(...i){return i.reduce((e,t)=>(t&&e.push(t.replace(new RegExp("(?<!https?:)\\/+","g"),"/").replace(/^\/+|\/+$/g,"")),e),[]).join("/").replace(/\/\?/,"?")}var st="/inbox",te=`${st}/notifications`,L,li=class{constructor(e={}){this.isSessionInitialized=!1;v(this,L);S(this,L,new ci(e))}initializeSession(n){return m(this,arguments,function*({applicationIdentifier:e,subscriberHash:t,subscriber:r}){let s=yield l(this,L).post(`${st}/session`,{applicationIdentifier:e,subscriberHash:t,subscriber:r});return l(this,L).setAuthorizationToken(s.token),l(this,L).setKeylessHeader(s.applicationIdentifier),this.isSessionInitialized=!0,s})}fetchNotifications({after:e,archived:t,limit:r=10,offset:n,read:s,tags:o,snoozed:c,data:h}){let f=new URLSearchParams(`limit=${r}`);return e&&f.append("after",e),n&&f.append("offset",`${n}`),o&&o.forEach(a=>f.append("tags[]",a)),s!==void 0&&f.append("read",`${s}`),t!==void 0&&f.append("archived",`${t}`),c!==void 0&&f.append("snoozed",`${c}`),h!==void 0&&f.append("data",JSON.stringify(h)),l(this,L).get(te,f,!1)}count({filters:e}){return l(this,L).get(`${te}/count`,new URLSearchParams({filters:JSON.stringify(e)}),!1)}read(e){return l(this,L).patch(`${te}/${e}/read`)}unread(e){return l(this,L).patch(`${te}/${e}/unread`)}archive(e){return l(this,L).patch(`${te}/${e}/archive`)}unarchive(e){return l(this,L).patch(`${te}/${e}/unarchive`)}snooze(e,t){return l(this,L).patch(`${te}/${e}/snooze`,{snoozeUntil:t})}unsnooze(e){return l(this,L).patch(`${te}/${e}/unsnooze`)}readAll({tags:e,data:t}){return l(this,L).post(`${te}/read`,{tags:e,data:t?JSON.stringify(t):void 0})}archiveAll({tags:e,data:t}){return l(this,L).post(`${te}/archive`,{tags:e,data:t?JSON.stringify(t):void 0})}archiveAllRead({tags:e,data:t}){return l(this,L).post(`${te}/read-archive`,{tags:e,data:t?JSON.stringify(t):void 0})}completeAction({actionType:e,notificationId:t}){return l(this,L).patch(`${te}/${t}/complete`,{actionType:e})}revertAction({actionType:e,notificationId:t}){return l(this,L).patch(`${te}/${t}/revert`,{actionType:e})}fetchPreferences(e){let t=new URLSearchParams;e&&e.forEach(n=>t.append("tags[]",n));let r=t.size?`?${t.toString()}`:"";return l(this,L).get(`${st}/preferences${r}`)}bulkUpdatePreferences(e){return l(this,L).patch(`${st}/preferences/bulk`,{preferences:e})}updateGlobalPreferences(e){return l(this,L).patch(`${st}/preferences`,e)}updateWorkflowPreferences({workflowId:e,channels:t}){return l(this,L).patch(`${st}/preferences/${e}`,t)}triggerHelloWorldEvent(){let e={name:"hello-world",to:{subscriberId:"keyless-subscriber-id"},payload:{subject:"Novu Keyless Environment",body:"You're using a keyless demo environment. For full access to Novu features and cloud integration, obtain your API key.",primaryActionText:"Obtain API Key",primaryActionUrl:"https://go.novu.co/keyless",secondaryActionText:"Explore Documentation",secondaryActionUrl:"https://go.novu.co/keyless-docs"}};return l(this,L).post("/inbox/events",e)}};L=new WeakMap;function pn(i){return{all:i=i||new Map,on:function(e,t){var r=i.get(e);r?r.push(t):i.set(e,[t])},off:function(e,t){var r=i.get(e);r&&(t?r.splice(r.indexOf(t)>>>0,1):i.set(e,[]))},emit:function(e,t){var r=i.get(e);r&&r.slice().map(function(n){n(t)}),(r=i.get("*"))&&r.slice().map(function(n){n(e,t)})}}}var je,hi=class{constructor(){v(this,je);S(this,je,pn())}on(e,t){return l(this,je).on(e,t),()=>{this.off(e,t)}}off(e,t){l(this,je).off(e,t)}emit(e,t){l(this,je).emit(e,t)}};je=new WeakMap;var k=class extends Error{constructor(e,t){super(e),this.originalError=t}};var Fe,ot,Ee=class{constructor({inboxServiceInstance:e,eventEmitterInstance:t}){v(this,Fe,[]);v(this,ot);this._emitter=t,this._inboxService=e,this._emitter.on("session.initialize.resolved",({error:r,data:n})=>{n?(this.onSessionSuccess(n),l(this,Fe).forEach(c=>m(this,[c],function*({fn:s,resolve:o}){o(yield s())})),S(this,Fe,[])):r&&(this.onSessionError(r),S(this,ot,r),l(this,Fe).forEach(({resolve:s})=>{s({error:new k("Failed to initialize session, please contact the support",r)})}),S(this,Fe,[]))})}onSessionSuccess(e){}onSessionError(e){}callWithSession(e){return m(this,null,function*(){return this._inboxService.isSessionInitialized?e():l(this,ot)?Promise.resolve({error:new k("Failed to initialize session, please contact the support",l(this,ot))}):new Promise((t,r)=>{l(this,Fe).push({fn:e,resolve:t,reject:r})})})}};Fe=new WeakMap,ot=new WeakMap;var Y,Q,U=class{constructor(e,t,r){v(this,Y);v(this,Q);S(this,Y,t),S(this,Q,r),this.id=e.id,this.subject=e.subject,this.body=e.body,this.to=e.to,this.isRead=e.isRead,this.isArchived=e.isArchived,this.isSnoozed=e.isSnoozed,this.snoozedUntil=e.snoozedUntil,this.deliveredAt=e.deliveredAt,this.createdAt=e.createdAt,this.readAt=e.readAt,this.archivedAt=e.archivedAt,this.avatar=e.avatar,this.primaryAction=e.primaryAction,this.secondaryAction=e.secondaryAction,this.channelType=e.channelType,this.tags=e.tags,this.redirect=e.redirect,this.data=e.data,this.workflow=e.workflow}read(){return fi({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this}})}unread(){return ui({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this}})}archive(){return di({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this}})}unarchive(){return pi({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this}})}snooze(e){return mi({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this,snoozeUntil:e}})}unsnooze(){return gi({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this}})}completePrimary(){if(!this.primaryAction)throw new Error("Primary action is not available");return at({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this},actionType:"primary"})}completeSecondary(){if(!this.primaryAction)throw new Error("Secondary action is not available");return at({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this},actionType:"secondary"})}revertPrimary(){if(!this.primaryAction)throw new Error("Primary action is not available");return ct({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this},actionType:"primary"})}revertSecondary(){if(!this.primaryAction)throw new Error("Secondary action is not available");return ct({emitter:l(this,Y),apiService:l(this,Q),args:{notification:this},actionType:"secondary"})}on(e,t){let r=l(this,Y).on(e,t);return()=>{r()}}off(e,t){l(this,Y).off(e,t)}};Y=new WeakMap,Q=new WeakMap;var fi=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isRead:!0,readAt:new Date().toISOString(),isArchived:!1,archivedAt:void 0},{emitter:i,apiService:e});try{i.emit("notification.read.pending",{args:t,data:s});let o=yield e.read(n),c=new U(o,i,e);return i.emit("notification.read.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.read.resolved",{args:t,error:o}),{error:new k("Failed to read notification",o)}}}),ui=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isRead:!1,readAt:null,isArchived:!1,archivedAt:void 0},{emitter:i,apiService:e});try{i.emit("notification.unread.pending",{args:t,data:s});let o=yield e.unread(n),c=new U(o,i,e);return i.emit("notification.unread.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.unread.resolved",{args:t,error:o}),{error:new k("Failed to unread notification",o)}}}),di=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isArchived:!0,archivedAt:new Date().toISOString(),isRead:!0,readAt:new Date().toISOString()},{emitter:i,apiService:e});try{i.emit("notification.archive.pending",{args:t,data:s});let o=yield e.archive(n),c=new U(o,i,e);return i.emit("notification.archive.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.archive.resolved",{args:t,error:o}),{error:new k("Failed to archive notification",o)}}}),pi=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isArchived:!1,archivedAt:null,isRead:!0,readAt:new Date().toISOString()},{emitter:i,apiService:e});try{i.emit("notification.unarchive.pending",{args:t,data:s});let o=yield e.unarchive(n),c=new U(o,i,e);return i.emit("notification.unarchive.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.unarchive.resolved",{args:t,error:o}),{error:new k("Failed to unarchive notification",o)}}}),mi=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isSnoozed:!0,snoozedUntil:t.snoozeUntil},{emitter:i,apiService:e});try{i.emit("notification.snooze.pending",{args:t,data:s});let o=yield e.snooze(n,t.snoozeUntil),c=new U(o,i,e);return i.emit("notification.snooze.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.snooze.resolved",{args:t,error:o}),{error:new k("Failed to snooze notification",o)}}}),gi=r=>m(void 0,[r],function*({emitter:i,apiService:e,args:t}){let{notificationId:n,optimisticValue:s}=Ue(t,{isSnoozed:!1,snoozedUntil:null},{emitter:i,apiService:e});try{i.emit("notification.unsnooze.pending",{args:t,data:s});let o=yield e.unsnooze(n),c=new U(o,i,e);return i.emit("notification.unsnooze.resolved",{args:t,data:c}),{data:c}}catch(o){return i.emit("notification.unsnooze.resolved",{args:t,error:o}),{error:new k("Failed to unsnooze notification",o)}}}),at=n=>m(void 0,[n],function*({emitter:i,apiService:e,args:t,actionType:r}){let s=r==="primary"?{primaryAction:R(E({},"notification"in t?t.notification.primaryAction:{}),{isCompleted:!0})}:{secondaryAction:R(E({},"notification"in t?t.notification.secondaryAction:{}),{isCompleted:!0})},{notificationId:o,optimisticValue:c}=Ue(t,s,{emitter:i,apiService:e});try{i.emit("notification.complete_action.pending",{args:t,data:c});let h=yield e.completeAction({actionType:r,notificationId:o}),f=new U(h,i,e);return i.emit("notification.complete_action.resolved",{args:t,data:f}),{data:f}}catch(h){return i.emit("notification.complete_action.resolved",{args:t,error:h}),{error:new k(`Failed to complete ${r} action on the notification`,h)}}}),ct=n=>m(void 0,[n],function*({emitter:i,apiService:e,args:t,actionType:r}){let s=r==="primary"?{primaryAction:R(E({},"notification"in t?t.notification.primaryAction:{}),{isCompleted:!1})}:{secondaryAction:R(E({},"notification"in t?t.notification.secondaryAction:{}),{isCompleted:!1})},{notificationId:o,optimisticValue:c}=Ue(t,s,{emitter:i,apiService:e});try{i.emit("notification.revert_action.pending",{args:t,data:c});let h=yield e.revertAction({actionType:r,notificationId:o}),f=new U(h,i,e);return i.emit("notification.revert_action.resolved",{args:t,data:f}),{data:f}}catch(h){return i.emit("notification.revert_action.resolved",{args:t,error:h}),{error:new k("Failed to fetch notifications",h)}}}),Ue=(i,e,t)=>"notification"in i?{notificationId:i.notification.id,optimisticValue:new U(E(E({},i.notification),e),t.emitter,t.apiService)}:{notificationId:i.notificationId},mn=s=>m(void 0,[s],function*({emitter:i,inboxService:e,notificationsCache:t,tags:r,data:n}){try{let c=t.getUniqueNotifications({tags:r,data:n}).map(h=>new U(R(E({},h),{isRead:!0,readAt:new Date().toISOString(),isArchived:!1,archivedAt:void 0}),i,e));return i.emit("notifications.read_all.pending",{args:{tags:r,data:n},data:c}),yield e.readAll({tags:r,data:n}),i.emit("notifications.read_all.resolved",{args:{tags:r,data:n},data:c}),{}}catch(o){return i.emit("notifications.read_all.resolved",{args:{tags:r,data:n},error:o}),{error:new k("Failed to read all notifications",o)}}}),gn=s=>m(void 0,[s],function*({emitter:i,inboxService:e,notificationsCache:t,tags:r,data:n}){try{let c=t.getUniqueNotifications({tags:r,data:n}).map(h=>new U(R(E({},h),{isRead:!0,readAt:new Date().toISOString(),isArchived:!0,archivedAt:new Date().toISOString()}),i,e));return i.emit("notifications.archive_all.pending",{args:{tags:r,data:n},data:c}),yield e.archiveAll({tags:r,data:n}),i.emit("notifications.archive_all.resolved",{args:{tags:r,data:n},data:c}),{}}catch(o){return i.emit("notifications.archive_all.resolved",{args:{tags:r,data:n},error:o}),{error:new k("Failed to archive all notifications",o)}}}),yn=s=>m(void 0,[s],function*({emitter:i,inboxService:e,notificationsCache:t,tags:r,data:n}){try{let c=t.getUniqueNotifications({tags:r,data:n,read:!0}).map(h=>new U(R(E({},h),{isArchived:!0,archivedAt:new Date().toISOString()}),i,e));return i.emit("notifications.archive_all_read.pending",{args:{tags:r,data:n},data:c}),yield e.archiveAllRead({tags:r,data:n}),i.emit("notifications.archive_all_read.resolved",{args:{tags:r,data:n},data:c}),{}}catch(o){return i.emit("notifications.archive_all_read.resolved",{args:{tags:r,data:n},error:o}),{error:new k("Failed to archive all read notifications",o)}}});var vn=(i,e)=>i===e?!0:!i||!e||i.length!==e.length?!1:i.every((t,r)=>t===e[r]);var sh=["seen","unseen"],oh=["read","unread"],lr=(i,e)=>vn(i,e)||!i&&(e==null?void 0:e.length)===0||(i==null?void 0:i.length)===0&&!e,hr=(i,e)=>{if(!i&&!e)return!0;if(!i||!e)return!1;try{return JSON.stringify(i)===JSON.stringify(e)}catch(t){return!1}},fr=(i,e)=>hr(i.data,e.data)&&lr(i.tags,e.tags)&&i.read===e.read&&i.archived===e.archived&&i.snoozed===e.snoozed;var fe,lt=class{constructor(){v(this,fe);S(this,fe,new Map)}get(e){return l(this,fe).get(e)}getValues(){return Array.from(l(this,fe).values())}entries(){return Array.from(l(this,fe).entries())}keys(){return Array.from(l(this,fe).keys())}set(e,t){l(this,fe).set(e,t)}remove(e){l(this,fe).delete(e)}clear(){l(this,fe).clear()}};fe=new WeakMap;var _n=({tags:i,data:e,read:t,archived:r,snoozed:n,limit:s,offset:o,after:c})=>Object.entries({tags:i,data:e,read:t,archived:r,snoozed:n,limit:s,offset:o,after:c}).filter(([h,f])=>f!=null&&!(Array.isArray(f)&&f.length===0)).reduce((h,[f,a])=>(h[f]=a,h),{}),ur=({tags:i,data:e,read:t,archived:r,snoozed:n,limit:s,offset:o,after:c})=>JSON.stringify(_n({tags:i,data:e,read:t,archived:r,snoozed:n,limit:s,offset:o,after:c})),Qo=({tags:i,data:e,read:t,archived:r,snoozed:n})=>JSON.stringify(_n({tags:i,data:e,read:t,archived:r,snoozed:n})),ht=i=>JSON.parse(i),Zo=["notification.read.pending","notification.read.resolved","notification.unread.pending","notification.unread.resolved","notification.complete_action.pending","notification.complete_action.resolved","notification.revert_action.pending","notification.revert_action.resolved","notifications.read_all.pending","notifications.read_all.resolved"],ea=["notification.archive.pending","notification.unarchive.pending","notification.snooze.pending","notification.unsnooze.pending","notifications.archive_all.pending","notifications.archive_all_read.pending"],De,z,Rt=class{constructor({emitter:e}){v(this,De);v(this,z);this.updateNotification=(e,t)=>{let r=l(this,z).get(e);if(!r)return!1;let n=r.notifications.findIndex(o=>o.id===t.id);if(n===-1)return!1;let s=[...r.notifications];return s[n]=t,l(this,z).set(e,R(E({},r),{notifications:s})),!0};this.removeNotification=(e,t)=>{let r=l(this,z).get(e);if(!r)return!1;let n=r.notifications.findIndex(o=>o.id===t.id);if(n===-1)return!1;let s=[...r.notifications];return s.splice(n,1),l(this,z).set(e,R(E({},r),{notifications:s})),!0};this.handleNotificationEvent=({remove:e}={remove:!1})=>({data:t})=>{if(!t)return;let r=Array.isArray(t)?t:[t],n=new Set;l(this,z).keys().forEach(s=>{r.forEach(o=>{let c=!1;e?c=this.removeNotification(s,o):c=this.updateNotification(s,o),c&&n.add(Qo(ht(s)))})}),n.forEach(s=>{let o=this.getAggregated(ht(s));l(this,De).emit("notifications.list.updated",{data:o})})};S(this,De,e),Zo.forEach(t=>{l(this,De).on(t,this.handleNotificationEvent())}),ea.forEach(t=>{l(this,De).on(t,this.handleNotificationEvent({remove:!0}))}),S(this,z,new lt)}getAggregated(e){return l(this,z).keys().filter(r=>{let n=ht(r);return fr(n,e)}).map(r=>l(this,z).get(r)).reduce((r,n)=>n?{hasMore:n.hasMore,filter:n.filter,notifications:[...r.notifications,...n.notifications]}:r,{hasMore:!1,filter:{},notifications:[]})}has(e){return l(this,z).get(ur(e))!==void 0}set(e,t){l(this,z).set(ur(e),t)}update(e,t){this.set(e,t);let r=this.getAggregated(ht(ur(e)));l(this,De).emit("notifications.list.updated",{data:r})}getAll(e){if(this.has(e))return this.getAggregated({tags:e.tags,data:e.data,read:e.read,snoozed:e.snoozed,archived:e.archived})}getUniqueNotifications({tags:e,read:t,data:r}){let n=l(this,z).keys(),s=new Map;return n.forEach(o=>{let c=ht(o);if(lr(e,c.tags)&&hr(r,c.data)){let h=l(this,z).get(o);if(!h)return;h.notifications.filter(f=>typeof t=="undefined"||t===f.isRead).forEach(f=>s.set(f.id,f))}}),Array.from(s.values())}clear(e){l(this,z).keys().forEach(r=>{fr(ht(r),e)&&l(this,z).remove(r)})}clearAll(){l(this,z).clear()}};De=new WeakMap,z=new WeakMap;var Tt,yi=class extends Ee{constructor({useCache:t,inboxServiceInstance:r,eventEmitterInstance:n}){super({eventEmitterInstance:n,inboxServiceInstance:r});v(this,Tt);this.cache=new Rt({emitter:n}),S(this,Tt,t)}list(){return m(this,arguments,function*(n={}){var s=n,{limit:t=10}=s,r=ar(s,["limit"]);return this.callWithSession(()=>m(this,null,function*(){let o=E({limit:t},r);try{let c="useCache"in o?o.useCache:l(this,Tt),h=c?this.cache.getAll(o):void 0;if(this._emitter.emit("notifications.list.pending",{args:o,data:h}),!h){let f=yield this._inboxService.fetchNotifications(E({limit:t},r));h={hasMore:f.hasMore,filter:f.filter,notifications:f.data.map(a=>new U(a,this._emitter,this._inboxService))},c&&(this.cache.set(o,h),h=this.cache.getAll(o))}return this._emitter.emit("notifications.list.resolved",{args:o,data:h}),{data:h}}catch(c){return this._emitter.emit("notifications.list.resolved",{args:o,error:c}),{error:new k("Failed to fetch notifications",c)}}}))})}count(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){let r=t&&"filters"in t?t.filters:[E({},t)];try{this._emitter.emit("notifications.count.pending",{args:t});let n=yield this._inboxService.count({filters:r}),s=t&&"filters"in t?{counts:n.data}:n.data[0];return this._emitter.emit("notifications.count.resolved",{args:t,data:s}),{data:s}}catch(n){return this._emitter.emit("notifications.count.resolved",{args:t,error:n}),{error:new k("Failed to count notifications",n)}}}))})}read(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return fi({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}unread(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return ui({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}archive(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return di({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}unarchive(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return pi({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}snooze(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return mi({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}unsnooze(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return gi({emitter:this._emitter,apiService:this._inboxService,args:t})}))})}completePrimary(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return at({emitter:this._emitter,apiService:this._inboxService,args:t,actionType:"primary"})}))})}completeSecondary(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return at({emitter:this._emitter,apiService:this._inboxService,args:t,actionType:"secondary"})}))})}revertPrimary(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return ct({emitter:this._emitter,apiService:this._inboxService,args:t,actionType:"primary"})}))})}revertSecondary(t){return m(this,null,function*(){return this.callWithSession(()=>m(this,null,function*(){return ct({emitter:this._emitter,apiService:this._inboxService,args:t,actionType:"secondary"})}))})}readAll(){return m(this,arguments,function*({tags:t,data:r}={}){return this.callWithSession(()=>m(this,null,function*(){return mn({emitter:this._emitter,inboxService:this._inboxService,notificationsCache:this.cache,tags:t,data:r})}))})}archiveAll(){return m(this,arguments,function*({tags:t,data:r}={}){return this.callWithSession(()=>m(this,null,function*(){return gn({emitter:this._emitter,inboxService:this._inboxService,notificationsCache:this.cache,tags:t,data:r})}))})}archiveAllRead(){return m(this,arguments,function*({tags:t,data:r}={}){return this.callWithSession(()=>m(this,null,function*(){return yn({emitter:this._emitter,inboxService:this._inboxService,notificationsCache:this.cache,tags:t,data:r})}))})}clearCache({filter:t}={}){return t?this.cache.clear(t!=null?t:{}):this.cache.clearAll()}triggerHelloWorldEvent(){return m(this,null,function*(){return this._inboxService.triggerHelloWorldEvent()})}};Tt=new WeakMap;var vi=s=>m(void 0,[s],function*({emitter:i,apiService:e,cache:t,useCache:r,args:n}){var h;let{channels:o}=n,c="workflowId"in n?n.workflowId:(h=n.preference.workflow)==null?void 0:h.id;try{i.emit("preference.update.pending",{args:n,data:"preference"in n?new Se(R(E({},n.preference),{channels:E(E({},n.preference.channels),o)}),{emitterInstance:i,inboxServiceInstance:e,cache:t,useCache:r}):void 0});let f;c?f=yield e.updateWorkflowPreferences({workflowId:c,channels:o}):(ta({emitter:i,apiService:e,cache:t,useCache:r,args:n}),f=yield e.updateGlobalPreferences(o));let a=new Se(f,{emitterInstance:i,inboxServiceInstance:e,cache:t,useCache:r});return i.emit("preference.update.resolved",{args:n,data:a}),{data:a}}catch(f){return i.emit("preference.update.resolved",{args:n,error:f}),{error:new k("Failed to update preference",f)}}}),En=s=>m(void 0,[s],function*({emitter:i,apiService:e,cache:t,useCache:r,args:n}){if(n.find(c=>"preference"in c&&c.preference.level==="global"))return{error:new k("Global preference is not supported in bulk update","")};try{let c=n.map(u=>"preference"in u?new Se(R(E({},u.preference),{channels:E(E({},u.preference.channels),u.channels)}),{emitterInstance:i,inboxServiceInstance:e,cache:t,useCache:r}):void 0).filter(u=>u!==void 0);i.emit("preferences.bulk_update.pending",{args:n,data:c});let h=n.map(u=>{var d,_,b,g;return E({workflowId:"workflowId"in u?u.workflowId:(g=(b=(d=u.preference.workflow)==null?void 0:d.id)!=null?b:(_=u.preference.workflow)==null?void 0:_.identifier)!=null?g:""},u.channels)}),a=(yield e.bulkUpdatePreferences(h)).map(u=>new Se(u,{emitterInstance:i,inboxServiceInstance:e,cache:t,useCache:r}));return i.emit("preferences.bulk_update.resolved",{args:n,data:a}),{data:a}}catch(c){return i.emit("preferences.bulk_update.resolved",{args:n,error:c}),{error:new k("Failed to bulk update preferences",c)}}}),ta=({emitter:i,apiService:e,cache:t,useCache:r,args:n})=>{let s=r?t==null?void 0:t.getAll({}):void 0;s==null||s.forEach(o=>{var c,h;if(o.level==="template"){let f=R(E({},o),{channels:Object.entries(o.channels).reduce((u,[d,_])=>{var g;let b=d;return u[b]=(g=n.channels[b])!=null?g:_,u},{})}),a="preference"in n?new Se(f,{emitterInstance:i,inboxServiceInstance:e,cache:t,useCache:r}):void 0;a&&i.emit("preference.update.pending",{args:{workflowId:(h=(c=o.workflow)==null?void 0:c.id)!=null?h:"",channels:a.channels},data:a})}})};var Ot,It,Pt,Lt,Se=class{constructor(e,{emitterInstance:t,inboxServiceInstance:r,cache:n,useCache:s}){v(this,Ot);v(this,It);v(this,Pt);v(this,Lt);S(this,Ot,t),S(this,It,r),S(this,Pt,n),S(this,Lt,s),this.level=e.level,this.enabled=e.enabled,this.channels=e.channels,this.workflow=e.workflow}update({channels:e,channelPreferences:t}){var r;return vi({emitter:l(this,Ot),apiService:l(this,It),cache:l(this,Pt),useCache:l(this,Lt),args:{workflowId:(r=this.workflow)==null?void 0:r.id,channels:e||t,preference:this}})}};Ot=new WeakMap,It=new WeakMap,Pt=new WeakMap,Lt=new WeakMap;var ia=["preference.update.pending","preference.update.resolved","preferences.bulk_update.pending","preferences.bulk_update.resolved"],ra=({tags:i})=>Object.entries({tags:i}).reduce((e,[t,r])=>(r==null||Array.isArray(r)&&r.length===0||(e[t]=r),e),{}),dr=({tags:i})=>JSON.stringify(ra({tags:i})),ft,ie,_i=class{constructor({emitterInstance:e}){v(this,ft);v(this,ie);this.updatePreference=(e,t)=>{let r=l(this,ie).get(e);if(!r)return!1;let n=r.findIndex(o=>{var c,h;return((c=o.workflow)==null?void 0:c.id)===((h=t.workflow)==null?void 0:h.id)||o.level===t.level&&t.level==="global"});if(n===-1)return!1;let s=[...r];return s[n]=t,l(this,ie).set(e,s),!0};this.handlePreferenceEvent=({data:e})=>{if(!e)return;let t=Array.isArray(e)?e:[e],r=new Set;l(this,ie).keys().forEach(n=>{t.forEach(s=>{let o=this.updatePreference(n,s),c=l(this,ie).get(n);!o||!c||r.add(n)})}),r.forEach(n=>{var s;l(this,ft).emit("preferences.list.updated",{data:(s=l(this,ie).get(n))!=null?s:[]})})};S(this,ft,e),ia.forEach(t=>{l(this,ft).on(t,this.handlePreferenceEvent)}),S(this,ie,new lt)}has(e){return l(this,ie).get(dr(e))!==void 0}set(e,t){l(this,ie).set(dr(e),t)}getAll(e){if(this.has(e))return l(this,ie).get(dr(e))}clearAll(){l(this,ie).clear()}};ft=new WeakMap,ie=new WeakMap;var Ne,Ei=class extends Ee{constructor({useCache:t,inboxServiceInstance:r,eventEmitterInstance:n}){super({eventEmitterInstance:n,inboxServiceInstance:r});v(this,Ne);this.cache=new _i({emitterInstance:this._emitter}),S(this,Ne,t)}list(){return m(this,arguments,function*(t={}){return this.callWithSession(()=>m(this,null,function*(){try{let r=l(this,Ne)?this.cache.getAll(t):void 0;return this._emitter.emit("preferences.list.pending",{args:t,data:r}),r||(r=(yield this._inboxService.fetchPreferences(t.tags)).map(s=>new Se(s,{emitterInstance:this._emitter,inboxServiceInstance:this._inboxService,cache:this.cache,useCache:l(this,Ne)})),l(this,Ne)&&(this.cache.set(t,r),r=this.cache.getAll(t))),this._emitter.emit("preferences.list.resolved",{args:t,data:r}),{data:r}}catch(r){throw this._emitter.emit("preferences.list.resolved",{args:t,error:r}),r}}))})}update(t){return m(this,null,function*(){return this.callWithSession(()=>vi({emitter:this._emitter,apiService:this._inboxService,cache:this.cache,useCache:l(this,Ne),args:t}))})}bulkUpdate(t){return m(this,null,function*(){return this.callWithSession(()=>En({emitter:this._emitter,apiService:this._inboxService,cache:this.cache,useCache:l(this,Ne),args:t}))})}};Ne=new WeakMap;var Ge,Ft,ue,Si=class{constructor(e,t,r){v(this,Ge);v(this,Ft);v(this,ue);S(this,Ge,r),S(this,Ft,t),S(this,ue,e)}get applicationIdentifier(){return l(this,ue).applicationIdentifier}get subscriberId(){var e;return(e=l(this,ue).subscriber)==null?void 0:e.subscriberId}handleApplicationIdentifier(e,t){if(typeof window=="undefined"||!window.localStorage)return null;let r="novu_keyless_application_identifier";switch(e){case"get":return window.localStorage.getItem(r);case"store":return t&&window.localStorage.setItem(r,t),null;case"delete":return window.localStorage.removeItem(r),null;default:return null}}initialize(e){return m(this,null,function*(){var t,r;try{e&&S(this,ue,e);let{subscriber:n,subscriberHash:s,applicationIdentifier:o}=l(this,ue),c=o;if(c)this.handleApplicationIdentifier("delete");else{let f=this.handleApplicationIdentifier("get");f&&(c=f)}l(this,Ge).emit("session.initialize.pending",{args:l(this,ue)});let h=yield l(this,Ft).initializeSession({applicationIdentifier:c,subscriberHash:s,subscriber:n});(t=h==null?void 0:h.applicationIdentifier)!=null&&t.startsWith("pk_keyless_")&&this.handleApplicationIdentifier("store",h.applicationIdentifier),(r=h==null?void 0:h.applicationIdentifier)!=null&&r.startsWith("pk_keyless_")||this.handleApplicationIdentifier("delete"),l(this,Ge).emit("session.initialize.resolved",{args:l(this,ue),data:h})}catch(n){l(this,Ge).emit("session.initialize.resolved",{args:l(this,ue),error:n})}})}};Ge=new WeakMap,Ft=new WeakMap,ue=new WeakMap;var de=Object.create(null);de.open="0";de.close="1";de.ping="2";de.pong="3";de.message="4";de.upgrade="5";de.noop="6";var Ut=Object.create(null);Object.keys(de).forEach(i=>{Ut[de[i]]=i});var Dt={type:"error",data:"parser error"};var Bt=({type:i,data:e},t,r)=>e instanceof ArrayBuffer||ArrayBuffer.isView(e)?r(t?e:"b"+Sn(e,!0).toString("base64")):r(de[i]+(e||"")),Sn=(i,e)=>Buffer.isBuffer(i)||i instanceof Uint8Array&&!e?i:i instanceof ArrayBuffer?Buffer.from(i):Buffer.from(i.buffer,i.byteOffset,i.byteLength),pr;function wn(i,e){if(i.data instanceof ArrayBuffer||ArrayBuffer.isView(i.data))return e(Sn(i.data,!1));Bt(i,!0,t=>{pr||(pr=new TextEncoder),e(pr.encode(t))})}var Mt=(i,e)=>{if(typeof i!="string")return{type:"message",data:bn(i,e)};let t=i.charAt(0);if(t==="b"){let r=Buffer.from(i.substring(1),"base64");return{type:"message",data:bn(r,e)}}return Ut[t]?i.length>1?{type:Ut[t],data:i.substring(1)}:{type:Ut[t]}:Dt},bn=(i,e)=>{switch(e){case"arraybuffer":return i instanceof ArrayBuffer?i:Buffer.isBuffer(i)?i.buffer.slice(i.byteOffset,i.byteOffset+i.byteLength):i.buffer;case"nodebuffer":default:return Buffer.isBuffer(i)?i:Buffer.from(i)}};var xn="",Cn=(i,e)=>{let t=i.length,r=new Array(t),n=0;i.forEach((s,o)=>{Bt(s,!1,c=>{r[o]=c,++n===t&&e(r.join(xn))})})},Nn=(i,e)=>{let t=i.split(xn),r=[];for(let n=0;n<t.length;n++){let s=Mt(t[n],e);if(r.push(s),s.type==="error")break}return r};function kn(){return new TransformStream({transform(i,e){wn(i,t=>{let r=t.length,n;if(r<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,r);else if(r<65536){n=new Uint8Array(3);let s=new DataView(n.buffer);s.setUint8(0,126),s.setUint16(1,r)}else{n=new Uint8Array(9);let s=new DataView(n.buffer);s.setUint8(0,127),s.setBigUint64(1,BigInt(r))}i.data&&typeof i.data!="string"&&(n[0]|=128),e.enqueue(n),e.enqueue(t)})}})}var mr;function wi(i){return i.reduce((e,t)=>e+t.length,0)}function bi(i,e){if(i[0].length===e)return i.shift();let t=new Uint8Array(e),r=0;for(let n=0;n<e;n++)t[n]=i[0][r++],r===i[0].length&&(i.shift(),r=0);return i.length&&r<i[0].length&&(i[0]=i[0].slice(r)),t}function An(i,e){mr||(mr=new TextDecoder);let t=[],r=0,n=-1,s=!1;return new TransformStream({transform(o,c){for(t.push(o);;){if(r===0){if(wi(t)<1)break;let h=bi(t,1);s=(h[0]&128)===128,n=h[0]&127,n<126?r=3:n===126?r=1:r=2}else if(r===1){if(wi(t)<2)break;let h=bi(t,2);n=new DataView(h.buffer,h.byteOffset,h.length).getUint16(0),r=3}else if(r===2){if(wi(t)<8)break;let h=bi(t,8),f=new DataView(h.buffer,h.byteOffset,h.length),a=f.getUint32(0);if(a>Math.pow(2,21)-1){c.enqueue(Dt);break}n=a*Math.pow(2,32)+f.getUint32(4),r=3}else{if(wi(t)<n)break;let h=bi(t,n);c.enqueue(Mt(s?h:mr.decode(h),e)),r=0}if(n===0||n>i){c.enqueue(Dt);break}}}})}var gr=4;function I(i){if(i)return na(i)}function na(i){for(var e in I.prototype)i[e]=I.prototype[e];return i}I.prototype.on=I.prototype.addEventListener=function(i,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+i]=this._callbacks["$"+i]||[]).push(e),this};I.prototype.once=function(i,e){function t(){this.off(i,t),e.apply(this,arguments)}return t.fn=e,this.on(i,t),this};I.prototype.off=I.prototype.removeListener=I.prototype.removeAllListeners=I.prototype.removeEventListener=function(i,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var t=this._callbacks["$"+i];if(!t)return this;if(arguments.length==1)return delete this._callbacks["$"+i],this;for(var r,n=0;n<t.length;n++)if(r=t[n],r===e||r.fn===e){t.splice(n,1);break}return t.length===0&&delete this._callbacks["$"+i],this};I.prototype.emit=function(i){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),t=this._callbacks["$"+i],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(t){t=t.slice(0);for(var r=0,n=t.length;r<n;++r)t[r].apply(this,e)}return this};I.prototype.emitReserved=I.prototype.emit;I.prototype.listeners=function(i){return this._callbacks=this._callbacks||{},this._callbacks["$"+i]||[]};I.prototype.hasListeners=function(i){return!!this.listeners(i).length};var pe=global;function xi(i,...e){return e.reduce((t,r)=>(i.hasOwnProperty(r)&&(t[r]=i[r]),t),{})}var sa=pe.setTimeout,oa=pe.clearTimeout;function ke(i,e){e.useNativeTimers?(i.setTimeoutFn=sa.bind(pe),i.clearTimeoutFn=oa.bind(pe)):(i.setTimeoutFn=pe.setTimeout.bind(pe),i.clearTimeoutFn=pe.clearTimeout.bind(pe))}var aa=1.33;function Rn(i){return typeof i=="string"?ca(i):Math.ceil((i.byteLength||i.size)*aa)}function ca(i){let e=0,t=0;for(let r=0,n=i.length;r<n;r++)e=i.charCodeAt(r),e<128?t+=1:e<2048?t+=2:e<55296||e>=57344?t+=3:(r++,t+=4);return t}var Wn=K(Ye(),1);function Mn(i){let e="";for(let t in i)i.hasOwnProperty(t)&&(e.length&&(e+="&"),e+=encodeURIComponent(t)+"="+encodeURIComponent(i[t]));return e}function qn(i){let e={},t=i.split("&");for(let r=0,n=t.length;r<n;r++){let s=t[r].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}var Oa=(0,Wn.default)("engine.io-client:transport"),br=class extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}},Ae=class extends I{constructor(e){super(),this.writable=!1,ke(this,e),this.opts=e,this.query=e.query,this.socket=e.socket}onError(e,t,r){return super.emitReserved("error",new br(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(e){this.readyState==="open"?this.write(e):Oa("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=Mt(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return e.indexOf(":")===-1?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(e){let t=Mn(e);return t.length?"?"+t:""}};var Xn=K(Ye(),1);var Hn="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),xr=64,Ia={},$n=0,Ri=0,zn;function Vn(i){let e="";do e=Hn[i%xr]+e,i=Math.floor(i/xr);while(i>0);return e}function Ti(){let i=Vn(+new Date);return i!==zn?($n=0,zn=i):i+"."+Vn($n++)}for(;Ri<xr;Ri++)Ia[Hn[Ri]]=Ri;var kr=K(Kn(),1),Ar=kr.default||kr;function Yn(){return new Nr}function La(i){let e=i.split("; "),t=e[0].indexOf("=");if(t===-1)return;let r=e[0].substring(0,t).trim();if(!r.length)return;let n=e[0].substring(t+1).trim();n.charCodeAt(0)===34&&(n=n.slice(1,-1));let s={name:r,value:n};for(let o=1;o<e.length;o++){let c=e[o].split("=");if(c.length!==2)continue;let h=c[0].trim(),f=c[1].trim();switch(h){case"Expires":s.expires=new Date(f);break;case"Max-Age":let a=new Date;a.setUTCSeconds(a.getUTCSeconds()+parseInt(f,10)),s.expires=a;break;default:}}return s}var Nr=class{constructor(){this.cookies=new Map}parseCookies(e){let t=e.getResponseHeader("set-cookie");t&&t.forEach(r=>{let n=La(r);n&&this.cookies.set(n.name,n)})}addCookies(e){let t=[];this.cookies.forEach((r,n)=>{var s;((s=r.expires)===null||s===void 0?void 0:s.getTime())<Date.now()?this.cookies.delete(n):t.push(`${n}=${r.value}`)}),t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}};var Z=(0,Xn.default)("engine.io-client:polling");function Fa(){}var Ua=function(){return new Ar({xdomain:!1}).responseType!=null}(),Oi=class extends Ae{constructor(e){if(super(e),this.polling=!1,typeof location!="undefined"){let r=location.protocol==="https:",n=location.port;n||(n=r?"443":"80"),this.xd=typeof location!="undefined"&&e.hostname!==location.hostname||n!==e.port}let t=e&&e.forceBase64;this.supportsBinary=Ua&&!t,this.opts.withCredentials&&(this.cookieJar=Yn())}get name(){return"polling"}doOpen(){this.poll()}pause(e){this.readyState="pausing";let t=()=>{Z("paused"),this.readyState="paused",e()};if(this.polling||!this.writable){let r=0;this.polling&&(Z("we are currently polling - waiting to pause"),r++,this.once("pollComplete",function(){Z("pre-pause polling complete"),--r||t()})),this.writable||(Z("we are currently writing - waiting to pause"),r++,this.once("drain",function(){Z("pre-pause writing complete"),--r||t()}))}else t()}poll(){Z("polling"),this.polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){Z("polling got data %s",e);let t=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};Nn(e,this.socket.binaryType).forEach(t),this.readyState!=="closed"&&(this.polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"?this.poll():Z('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{Z("writing close packet"),this.write([{type:"close"}])};this.readyState==="open"?(Z("transport open - closing"),e()):(Z("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,Cn(e,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return this.opts.timestampRequests!==!1&&(t[this.opts.timestampParam]=Ti()),!this.supportsBinary&&!t.sid&&(t.b64=1),this.createUri(e,t)}request(e={}){return Object.assign(e,{xd:this.xd,cookieJar:this.cookieJar},this.opts),new Me(this.uri(),e)}doWrite(e,t){let r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(n,s)=>{this.onError("xhr post error",n,s)})}doPoll(){Z("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(t,r)=>{this.onError("xhr poll error",t,r)}),this.pollXhr=e}},Me=class i extends I{constructor(e,t){super(),ke(this,t),this.opts=t,this.method=t.method||"GET",this.uri=e,this.data=t.data!==void 0?t.data:null,this.create()}create(){var e;let t=xi(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this.opts.xd;let r=this.xhr=new Ar(t);try{Z("xhr open %s: %s",this.method,this.uri),r.open(this.method,this.uri,!0);try{if(this.opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let n in this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(n)&&r.setRequestHeader(n,this.opts.extraHeaders[n])}}catch(n){}if(this.method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(n){}try{r.setRequestHeader("Accept","*/*")}catch(n){}(e=this.opts.cookieJar)===null||e===void 0||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(r.timeout=this.opts.requestTimeout),r.onreadystatechange=()=>{var n;r.readyState===3&&((n=this.opts.cookieJar)===null||n===void 0||n.parseCookies(r)),r.readyState===4&&(r.status===200||r.status===1223?this.onLoad():this.setTimeoutFn(()=>{this.onError(typeof r.status=="number"?r.status:0)},0))},Z("xhr data %s",this.data),r.send(this.data)}catch(n){this.setTimeoutFn(()=>{this.onError(n)},0);return}typeof document!="undefined"&&(this.index=i.requestsCount++,i.requests[this.index]=this)}onError(e){this.emitReserved("error",e,this.xhr),this.cleanup(!0)}cleanup(e){if(!(typeof this.xhr=="undefined"||this.xhr===null)){if(this.xhr.onreadystatechange=Fa,e)try{this.xhr.abort()}catch(t){}typeof document!="undefined"&&delete i.requests[this.index],this.xhr=null}}onLoad(){let e=this.xhr.responseText;e!==null&&(this.emitReserved("data",e),this.emitReserved("success"),this.cleanup())}abort(){this.cleanup()}};Me.requestsCount=0;Me.requests={};if(typeof document!="undefined"){if(typeof attachEvent=="function")attachEvent("onunload",Jn);else if(typeof addEventListener=="function"){let i="onpagehide"in pe?"pagehide":"unload";addEventListener(i,Jn,!1)}}function Jn(){for(let i in Me.requests)Me.requests.hasOwnProperty(i)&&Me.requests[i].abort()}var jc=K(ts(),1),Gc=K(Dr(),1),Kc=K(Mr(),1),Zs=K(jr(),1),Yc=K(Qs(),1);var eo=Zs.default;var Xt=eo,Wi=!1,to="nodebuffer",Ze=process.nextTick;var ro=K(Ye(),1);var Jc=(0,ro.default)("engine.io-client:websocket"),io=typeof navigator!="undefined"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative",$i=class extends Ae{constructor(e){super(e),this.supportsBinary=!e.forceBase64}get name(){return"websocket"}doOpen(){if(!this.check())return;let e=this.uri(),t=this.opts.protocols,r=io?{}:xi(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=Wi&&!io?t?new Xt(e,t):new Xt(e):new Xt(e,t,r)}catch(n){return this.emitReserved("error",n)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;Bt(r,this.supportsBinary,s=>{let o={};Wi||(r.options&&(o.compress=r.options.compress),this.opts.perMessageDeflate&&(typeof s=="string"?Buffer.byteLength(s):s.length)<this.opts.perMessageDeflate.threshold&&(o.compress=!1));try{Wi?this.ws.send(s):this.ws.send(s,o)}catch(c){Jc("websocket closed before onclose event")}n&&Ze(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws!="undefined"&&(this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=Ti()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}check(){return!!Xt}};var no=K(Ye(),1),Qt=(0,no.default)("engine.io-client:webtransport"),zi=class extends Ae{get name(){return"webtransport"}doOpen(){typeof WebTransport=="function"&&(this.transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name]),this.transport.closed.then(()=>{Qt("transport closed gracefully"),this.onClose()}).catch(e=>{Qt("transport closed due to %s",e),this.onError("webtransport error",e)}),this.transport.ready.then(()=>{this.transport.createBidirectionalStream().then(e=>{let t=An(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),n=kn();n.readable.pipeTo(e.writable),this.writer=n.writable.getWriter();let s=()=>{r.read().then(({done:c,value:h})=>{if(c){Qt("session is closed");return}Qt("received chunk: %o",h),this.onPacket(h),s()}).catch(c=>{Qt("an error occurred while reading: %s",c)})};s();let o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this.writer.write(o).then(()=>this.onOpen())})}))}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;this.writer.write(r).then(()=>{n&&Ze(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;(e=this.transport)===null||e===void 0||e.close()}};var Kr={websocket:$i,webtransport:zi,polling:Oi};var Xc=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Qc=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function _t(i){let e=i,t=i.indexOf("["),r=i.indexOf("]");t!=-1&&r!=-1&&(i=i.substring(0,t)+i.substring(t,r).replace(/:/g,";")+i.substring(r,i.length));let n=Xc.exec(i||""),s={},o=14;for(;o--;)s[Qc[o]]=n[o]||"";return t!=-1&&r!=-1&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=Zc(s,s.path),s.queryKey=el(s,s.query),s}function Zc(i,e){let t=/\/{2,9}/g,r=e.replace(t,"/").split("/");return(e.slice(0,1)=="/"||e.length===0)&&r.splice(0,1),e.slice(-1)=="/"&&r.splice(r.length-1,1),r}function el(i,e){let t={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,n,s){n&&(t[n]=s)}),t}var so=K(Ye(),1);var F=(0,so.default)("engine.io-client:socket"),et=class i extends I{constructor(e,t={}){super(),this.binaryType=to,this.writeBuffer=[],e&&typeof e=="object"&&(t=e,e=null),e?(e=_t(e),t.hostname=e.host,t.secure=e.protocol==="https"||e.protocol==="wss",t.port=e.port,e.query&&(t.query=e.query)):t.host&&(t.hostname=_t(t.host).host),ke(this,t),this.secure=t.secure!=null?t.secure:typeof location!="undefined"&&location.protocol==="https:",t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||(typeof location!="undefined"?location.hostname:"localhost"),this.port=t.port||(typeof location!="undefined"&&location.port?location.port:this.secure?"443":"80"),this.transports=t.transports||["polling","websocket","webtransport"],this.writeBuffer=[],this.prevBufferLen=0,this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=qn(this.opts.query)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingTimeoutTimer=null,typeof addEventListener=="function"&&(this.opts.closeOnBeforeunload&&(this.beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this.beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this.offlineEventListener=()=>{this.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",this.offlineEventListener,!1))),this.open()}createTransport(e){F('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=gr,t.transport=e,this.id&&(t.sid=this.id);let r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return F("options: %j",r),new Kr[e](r)}open(){let e;if(this.opts.rememberUpgrade&&i.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1)e="websocket";else if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}else e=this.transports[0];this.readyState="opening";try{e=this.createTransport(e)}catch(t){F("error while creating transport: %s",t),this.transports.shift(),this.open();return}e.open(),this.setTransport(e)}setTransport(e){F("setting transport %s",e.name),this.transport&&(F("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",t=>this.onClose("transport close",t))}probe(e){F('probing transport "%s"',e);let t=this.createTransport(e),r=!1;i.priorWebsocketSuccess=!1;let n=()=>{r||(F('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",u=>{if(!r)if(u.type==="pong"&&u.data==="probe"){if(F('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;i.priorWebsocketSuccess=t.name==="websocket",F('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{r||this.readyState!=="closed"&&(F("changing transport and sending upgrade packet"),a(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{F('probe transport "%s" failed',e);let d=new Error("probe error");d.transport=t.name,this.emitReserved("upgradeError",d)}}))};function s(){r||(r=!0,a(),t.close(),t=null)}let o=u=>{let d=new Error("probe error: "+u);d.transport=t.name,s(),F('probe transport "%s" failed because of error: %s',e,u),this.emitReserved("upgradeError",d)};function c(){o("transport closed")}function h(){o("socket closed")}function f(u){t&&u.name!==t.name&&(F('"%s" works - aborting "%s"',u.name,t.name),s())}let a=()=>{t.removeListener("open",n),t.removeListener("error",o),t.removeListener("close",c),this.off("close",h),this.off("upgrading",f)};t.once("open",n),t.once("error",o),t.once("close",c),this.once("close",h),this.once("upgrading",f),this.upgrades.indexOf("webtransport")!==-1&&e!=="webtransport"?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onOpen(){if(F("socket open"),this.readyState="open",i.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush(),this.readyState==="open"&&this.opts.upgrade){F("starting upgrade probes");let e=0,t=this.upgrades.length;for(;e<t;e++)this.probe(this.upgrades[e])}}onPacket(e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(F('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),this.resetPingTimeout(),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":let t=new Error("server error");t.code=e.data,this.onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data);break}else F('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.maxPayload=e.maxPayload,this.onOpen(),this.readyState!=="closed"&&this.resetPingTimeout()}resetPingTimeout(){this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn(()=>{this.onClose("ping timeout")},this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}onDrain(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this.getWritablePackets();F("flushing %d packets in socket",e.length),this.transport.send(e),this.prevBufferLen=e.length,this.emitReserved("flush")}}getWritablePackets(){if(!(this.maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let r=0;r<this.writeBuffer.length;r++){let n=this.writeBuffer[r].data;if(n&&(t+=Rn(n)),r>0&&t>this.maxPayload)return F("only send %d out of %d packets",r,this.writeBuffer.length),this.writeBuffer.slice(0,r);t+=2}return F("payload size is %d (max: %d)",t,this.maxPayload),this.writeBuffer}write(e,t,r){return this.sendPacket("message",e,t,r),this}send(e,t,r){return this.sendPacket("message",e,t,r),this}sendPacket(e,t,r,n){if(typeof t=="function"&&(n=t,t=void 0),typeof r=="function"&&(n=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;let s={type:e,data:t,options:r};this.emitReserved("packetCreate",s),this.writeBuffer.push(s),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this.onClose("forced close"),F("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}onError(e){F("socket error %j",e),i.priorWebsocketSuccess=!1,this.emitReserved("error",e),this.onClose("transport error",e)}onClose(e,t){(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")&&(F('socket close with reason: "%s"',e),this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),typeof removeEventListener=="function"&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this.prevBufferLen=0)}filterUpgrades(e){let t=[],r=0,n=e.length;for(;r<n;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}};et.protocol=gr;var _u=et.protocol;var fo=K(Zt(),1),ho=(0,fo.default)("socket.io-client:url");function uo(i,e="",t){let r=i;t=t||typeof location!="undefined"&&location,i==null&&(i=t.protocol+"//"+t.host),typeof i=="string"&&(i.charAt(0)==="/"&&(i.charAt(1)==="/"?i=t.protocol+i:i=t.host+i),/^(https?|wss?):\/\//.test(i)||(ho("protocol-less url %s",i),typeof t!="undefined"?i=t.protocol+"//"+i:i="https://"+i),ho("parse %s",i),r=_t(i)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let s=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+s+":"+r.port+e,r.href=r.protocol+"://"+s+(t&&t.port===r.port?"":":"+r.port),r}var nn={};Go(nn,{Decoder:()=>tn,Encoder:()=>en,PacketType:()=>C,protocol:()=>_o});var gl=typeof ArrayBuffer=="function",yl=i=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(i):i.buffer instanceof ArrayBuffer,po=Object.prototype.toString,vl=typeof Blob=="function"||typeof Blob!="undefined"&&po.call(Blob)==="[object BlobConstructor]",_l=typeof File=="function"||typeof File!="undefined"&&po.call(File)==="[object FileConstructor]";function ti(i){return gl&&(i instanceof ArrayBuffer||yl(i))||vl&&i instanceof Blob||_l&&i instanceof File}function ei(i,e){if(!i||typeof i!="object")return!1;if(Array.isArray(i)){for(let t=0,r=i.length;t<r;t++)if(ei(i[t]))return!0;return!1}if(ti(i))return!0;if(i.toJSON&&typeof i.toJSON=="function"&&arguments.length===1)return ei(i.toJSON(),!0);for(let t in i)if(Object.prototype.hasOwnProperty.call(i,t)&&ei(i[t]))return!0;return!1}function mo(i){let e=[],t=i.data,r=i;return r.data=Xr(t,e),r.attachments=e.length,{packet:r,buffers:e}}function Xr(i,e){if(!i)return i;if(ti(i)){let t={_placeholder:!0,num:e.length};return e.push(i),t}else if(Array.isArray(i)){let t=new Array(i.length);for(let r=0;r<i.length;r++)t[r]=Xr(i[r],e);return t}else if(typeof i=="object"&&!(i instanceof Date)){let t={};for(let r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=Xr(i[r],e));return t}return i}function go(i,e){return i.data=Qr(i.data,e),delete i.attachments,i}function Qr(i,e){if(!i)return i;if(i&&i._placeholder===!0){if(typeof i.num=="number"&&i.num>=0&&i.num<e.length)return e[i.num];throw new Error("illegal attachments")}else if(Array.isArray(i))for(let t=0;t<i.length;t++)i[t]=Qr(i[t],e);else if(typeof i=="object")for(let t in i)Object.prototype.hasOwnProperty.call(i,t)&&(i[t]=Qr(i[t],e));return i}var vo=K(Ye(),1),Zr=(0,vo.default)("socket.io-parser"),El=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],_o=5,C;(function(i){i[i.CONNECT=0]="CONNECT",i[i.DISCONNECT=1]="DISCONNECT",i[i.EVENT=2]="EVENT",i[i.ACK=3]="ACK",i[i.CONNECT_ERROR=4]="CONNECT_ERROR",i[i.BINARY_EVENT=5]="BINARY_EVENT",i[i.BINARY_ACK=6]="BINARY_ACK"})(C||(C={}));var en=class{constructor(e){this.replacer=e}encode(e){return Zr("encoding packet %j",e),(e.type===C.EVENT||e.type===C.ACK)&&ei(e)?this.encodeAsBinary({type:e.type===C.EVENT?C.BINARY_EVENT:C.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===C.BINARY_EVENT||e.type===C.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&e.nsp!=="/"&&(t+=e.nsp+","),e.id!=null&&(t+=e.id),e.data!=null&&(t+=JSON.stringify(e.data,this.replacer)),Zr("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=mo(e),r=this.encodeAsString(t.packet),n=t.buffers;return n.unshift(r),n}};function yo(i){return Object.prototype.toString.call(i)==="[object Object]"}var tn=class i extends I{constructor(e){super(),this.reviver=e}add(e){let t;if(typeof e=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);let r=t.type===C.BINARY_EVENT;r||t.type===C.BINARY_ACK?(t.type=r?C.EVENT:C.ACK,this.reconstructor=new rn(t),t.attachments===0&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ti(e)||e.base64)if(this.reconstructor)t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+e)}decodeString(e){let t=0,r={type:Number(e.charAt(0))};if(C[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===C.BINARY_EVENT||r.type===C.BINARY_ACK){let s=t+1;for(;e.charAt(++t)!=="-"&&t!=e.length;);let o=e.substring(s,t);if(o!=Number(o)||e.charAt(t)!=="-")throw new Error("Illegal attachments");r.attachments=Number(o)}if(e.charAt(t+1)==="/"){let s=t+1;for(;++t&&!(e.charAt(t)===","||t===e.length););r.nsp=e.substring(s,t)}else r.nsp="/";let n=e.charAt(t+1);if(n!==""&&Number(n)==n){let s=t+1;for(;++t;){let o=e.charAt(t);if(o==null||Number(o)!=o){--t;break}if(t===e.length)break}r.id=Number(e.substring(s,t+1))}if(e.charAt(++t)){let s=this.tryParse(e.substr(t));if(i.isPayloadValid(r.type,s))r.data=s;else throw new Error("invalid payload")}return Zr("decoded %s as %j",e,r),r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(t){return!1}}static isPayloadValid(e,t){switch(e){case C.CONNECT:return yo(t);case C.DISCONNECT:return t===void 0;case C.CONNECT_ERROR:return typeof t=="string"||yo(t);case C.EVENT:case C.BINARY_EVENT:return Array.isArray(t)&&(typeof t[0]=="number"||typeof t[0]=="string"&&El.indexOf(t[0])===-1);case C.ACK:case C.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}},rn=class{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){let t=go(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}};function oe(i,e,t){return i.on(e,t),function(){i.off(e,t)}}var Eo=K(Zt(),1),$=(0,Eo.default)("socket.io-client:socket"),Sl=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1}),Et=class extends I{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[oe(e,"open",this.onopen.bind(this)),oe(e,"packet",this.onpacket.bind(this)),oe(e,"error",this.onerror.bind(this)),oe(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){if(Sl.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let r={type:C.EVENT,data:t};if(r.options={},r.options.compress=this.flags.compress!==!1,typeof t[t.length-1]=="function"){let o=this.ids++;$("emitting packet with ack id %d",o);let c=t.pop();this._registerAckCallback(o,c),r.id=o}let n=this.io.engine&&this.io.engine.transport&&this.io.engine.transport.writable;return this.flags.volatile&&(!n||!this.connected)?$("discard packet as the transport is not currently writable"):this.connected?(this.notifyOutgoingListeners(r),this.packet(r)):this.sendBuffer.push(r),this.flags={},this}_registerAckCallback(e,t){var r;let n=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(n===void 0){this.acks[e]=t;return}let s=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let o=0;o<this.sendBuffer.length;o++)this.sendBuffer[o].id===e&&($("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(o,1));$("event with ack id %d has timed out after %d ms",e,n),t.call(this,new Error("operation has timed out"))},n);this.acks[e]=(...o)=>{this.io.clearTimeoutFn(s),t.apply(this,[null,...o])}}emitWithAck(e,...t){let r=this.flags.timeout!==void 0||this._opts.ackTimeout!==void 0;return new Promise((n,s)=>{t.push((o,c)=>r?o?s(o):n(c):n(o)),this.emit(e,...t)})}_addToQueue(e){let t;typeof e[e.length-1]=="function"&&(t=e.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((n,...s)=>r!==this._queue[0]?void 0:(n!==null?r.tryCount>this._opts.retries&&($("packet [%d] is discarded after %d tries",r.id,r.tryCount),this._queue.shift(),t&&t(n)):($("packet [%d] was successfully sent",r.id),this._queue.shift(),t&&t(null,...s)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if($("draining queue"),!this.connected||this._queue.length===0)return;let t=this._queue[0];if(t.pending&&!e){$("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,$("sending packet [%d] (try n\xB0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){$("transport is open - connecting"),typeof this.auth=="function"?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:C.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){$("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t)}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case C.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case C.EVENT:case C.BINARY_EVENT:this.onevent(e);break;case C.ACK:case C.BINARY_ACK:this.onack(e);break;case C.DISCONNECT:this.ondisconnect();break;case C.CONNECT_ERROR:this.destroy();let r=new Error(e.data.message);r.data=e.data.data,this.emitReserved("connect_error",r);break}}onevent(e){let t=e.data||[];$("emitting event %j",t),e.id!=null&&($("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){let t=this._anyListeners.slice();for(let r of t)r.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=="string"&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,r=!1;return function(...n){r||(r=!0,$("sending ack %j",n),t.packet({type:C.ACK,id:e,data:n}))}}onack(e){let t=this.acks[e.id];typeof t=="function"?($("calling ack %s with %j",e.id,e.data),t.apply(this,e.data),delete this.acks[e.id]):$("bad ack %s",e.id)}onconnect(e,t){$("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){$("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&($("performing disconnect (%s)",this.nsp),this.packet({type:C.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r])return t.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r])return t.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){let t=this._anyOutgoingListeners.slice();for(let r of t)r.apply(this,e.data)}}};function tt(i){i=i||{},this.ms=i.min||100,this.max=i.max||1e4,this.factor=i.factor||2,this.jitter=i.jitter>0&&i.jitter<=1?i.jitter:0,this.attempts=0}tt.prototype.duration=function(){var i=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),t=Math.floor(e*this.jitter*i);i=Math.floor(e*10)&1?i+t:i-t}return Math.min(i,this.max)|0};tt.prototype.reset=function(){this.attempts=0};tt.prototype.setMin=function(i){this.ms=i};tt.prototype.setMax=function(i){this.max=i};tt.prototype.setJitter=function(i){this.jitter=i};var So=K(Zt(),1),G=(0,So.default)("socket.io-client:manager"),St=class extends I{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&typeof e=="object"&&(t=e,e=void 0),t=t||{},t.path=t.path||"/socket.io",this.opts=t,ke(this,t),this.reconnection(t.reconnection!==!1),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor((r=t.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new tt({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(t.timeout==null?2e4:t.timeout),this._readyState="closed",this.uri=e;let n=t.parser||nn;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=t.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,this):this._reconnection}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(t=this.backoff)===null||t===void 0||t.setMin(e),this)}randomizationFactor(e){var t;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(t=this.backoff)===null||t===void 0||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(t=this.backoff)===null||t===void 0||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(e){if(G("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;G("opening %s",this.uri),this.engine=new et(this.uri,this.opts);let t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=oe(t,"open",function(){r.onopen(),e&&e()}),s=c=>{G("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",c),e?e(c):this.maybeReconnectOnOpen()},o=oe(t,"error",s);if(this._timeout!==!1){let c=this._timeout;G("connect attempt will timeout after %d",c);let h=this.setTimeoutFn(()=>{G("connect attempt timed out after %d",c),n(),s(new Error("timeout")),t.close()},c);this.opts.autoUnref&&h.unref(),this.subs.push(()=>{this.clearTimeoutFn(h)})}return this.subs.push(n),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){G("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(oe(e,"ping",this.onping.bind(this)),oe(e,"data",this.ondata.bind(this)),oe(e,"error",this.onerror.bind(this)),oe(e,"close",this.onclose.bind(this)),oe(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){Ze(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){G("error",e),this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new Et(this,e,t),this.nsps[e]=r),r}_destroy(e){let t=Object.keys(this.nsps);for(let r of t)if(this.nsps[r].active){G("socket %s is still active, skipping close",r);return}this._close()}_packet(e){G("writing packet %j",e);let t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){G("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){G("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close"),this.engine&&this.engine.close()}disconnect(){return this._close()}onclose(e,t){G("closed due to %s",e),this.cleanup(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)G("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();G("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let r=this.setTimeoutFn(()=>{e.skipReconnect||(G("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),!e.skipReconnect&&e.open(n=>{n?(G("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",n)):(G("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}};var bo=K(Zt(),1),wo=(0,bo.default)("socket.io-client"),ii={};function ri(i,e){typeof i=="object"&&(e=i,i=void 0),e=e||{};let t=uo(i,e.path||"/socket.io"),r=t.source,n=t.id,s=t.path,o=ii[n]&&s in ii[n].nsps,c=e.forceNew||e["force new connection"]||e.multiplex===!1||o,h;return c?(wo("ignoring socket cache for %s",r),h=new St(r,e)):(ii[n]||(wo("new io instance for %s",r),ii[n]=new St(r,e)),h=ii[n]),t.query&&!e.query&&(e.query=t.queryKey),h.socket(t.path,e)}Object.assign(ri,{Manager:St,Socket:Et,io:ri,connect:ri});var wl="https://ws.novu.co",xo="notifications.notification_received",Co="notifications.unseen_count_changed",No="notifications.unread_count_changed",bl=({_id:i,content:e,read:t,archived:r,snoozedUntil:n,deliveredAt:s,createdAt:o,lastReadDate:c,archivedAt:h,channel:f,subscriber:a,subject:u,avatar:d,cta:_,tags:b,data:g,workflow:w})=>{var A,M,q,ee,Ve,He,X,Pe;let T={id:a==null?void 0:a._id,subscriberId:a==null?void 0:a.subscriberId,firstName:a==null?void 0:a.firstName,lastName:a==null?void 0:a.lastName,avatar:a==null?void 0:a.avatar,locale:a==null?void 0:a.locale,data:a==null?void 0:a.data,timezone:a==null?void 0:a.timezone,email:a==null?void 0:a.email,phone:a==null?void 0:a.phone},x=(M=(A=_.action)==null?void 0:A.buttons)==null?void 0:M.find(_e=>_e.type==="primary"),p=(ee=(q=_.action)==null?void 0:q.buttons)==null?void 0:ee.find(_e=>_e.type==="secondary"),y=(He=(Ve=_.action)==null?void 0:Ve.result)==null?void 0:He.type,B=(X=_.action)==null?void 0:X.status;return R(E(E({id:i,subject:u,body:e,to:T,isRead:t,isArchived:r,isSnoozed:!!n},s&&{deliveredAt:s}),n&&{snoozedUntil:n}),{createdAt:o,readAt:c,archivedAt:h,avatar:d,primaryAction:x&&{label:x.content,isCompleted:y==="primary"&&B==="done",redirect:x.url?{target:x.target,url:x.url}:void 0},secondaryAction:p&&{label:p.content,isCompleted:y==="secondary"&&B==="done",redirect:p.url?{target:p.target,url:p.url}:void 0},channelType:f,tags:b,redirect:(Pe=_.data)!=null&&Pe.url?{url:_.data.url,target:_.data.target}:void 0,data:g,workflow:w})},wt,ge,ae,bt,Ki,Yi,Ji,be,ko,sn,on,Gi=class extends Ee{constructor({socketUrl:t,inboxServiceInstance:r,eventEmitterInstance:n}){super({eventEmitterInstance:n,inboxServiceInstance:r});v(this,be);v(this,wt);v(this,ge);v(this,ae);v(this,bt);v(this,Ki,({message:t})=>{l(this,ge).emit(xo,{result:new U(bl(t),l(this,ge),this._inboxService)})});v(this,Yi,({unseenCount:t})=>{l(this,ge).emit(Co,{result:t})});v(this,Ji,({unreadCount:t})=>{l(this,ge).emit(No,{result:t})});S(this,ge,n),S(this,bt,t!=null?t:wl)}onSessionSuccess({token:t}){S(this,wt,t)}isSocketEvent(t){return t===xo||t===Co||t===No}connect(){return m(this,null,function*(){return l(this,wt)?le(this,be,sn).call(this):this.callWithSession(le(this,be,sn).bind(this))})}disconnect(){return m(this,null,function*(){return l(this,ae)?le(this,be,on).call(this):this.callWithSession(le(this,be,on).bind(this))})}};wt=new WeakMap,ge=new WeakMap,ae=new WeakMap,bt=new WeakMap,Ki=new WeakMap,Yi=new WeakMap,Ji=new WeakMap,be=new WeakSet,ko=function(){return m(this,null,function*(){var r,n,s;if(l(this,ae))return;let t={socketUrl:l(this,bt)};l(this,ge).emit("socket.connect.pending",{args:t}),S(this,ae,ri(l(this,bt),{reconnectionDelayMax:1e4,transports:["websocket"],query:{token:`${l(this,wt)}`}})),l(this,ae).on("connect",()=>{l(this,ge).emit("socket.connect.resolved",{args:t})}),l(this,ae).on("connect_error",o=>{l(this,ge).emit("socket.connect.resolved",{args:t,error:o})}),(r=l(this,ae))==null||r.on("notification_received",l(this,Ki)),(n=l(this,ae))==null||n.on("unseen_count_changed",l(this,Yi)),(s=l(this,ae))==null||s.on("unread_count_changed",l(this,Ji))})},sn=function(){return m(this,null,function*(){try{return yield le(this,be,ko).call(this),{}}catch(t){return{error:new k("Failed to initialize the socket",t)}}})},on=function(){return m(this,null,function*(){var t;try{return(t=l(this,ae))==null||t.disconnect(),S(this,ae,void 0),{}}catch(r){return{error:new k("Failed to disconnect from the socket",r)}}})};(!globalThis.EventTarget||!globalThis.Event)&&console.error(`
  PartySocket requires a global 'EventTarget' class to be available!
  You can polyfill this global by adding this to your code before any partysocket imports: 
  
  \`\`\`
  import 'partysocket/event-target-polyfill';
  \`\`\`
  Please file an issue at https://github.com/partykit/partykit if you're still having trouble.
`);var To=class extends Event{constructor(e,t){super("error",t);O(this,"message");O(this,"error");this.message=e.message,this.error=e}},Oo=class extends Event{constructor(e=1e3,t="",r){super("close",r);O(this,"code");O(this,"reason");O(this,"wasClean",!0);this.code=e,this.reason=t}},an={Event,ErrorEvent:To,CloseEvent:Oo};function xl(i,e){if(!i)throw new Error(e)}function Cl(i){return new i.constructor(i.type,i)}function Nl(i){return"data"in i?new MessageEvent(i.type,i):"code"in i||"reason"in i?new Oo(i.code||1999,i.reason||"unknown reason",i):"error"in i?new To(i.error,i):new Event(i.type,i)}var Ao,kl=typeof process!="undefined"&&typeof((Ao=process.versions)==null?void 0:Ao.node)!="undefined"&&typeof document=="undefined",Xi=kl?Nl:Cl,it={maxReconnectionDelay:1e4,minReconnectionDelay:1e3+Math.random()*4e3,minUptime:5e3,reconnectionDelayGrowFactor:1.3,connectionTimeout:4e3,maxRetries:Number.POSITIVE_INFINITY,maxEnqueuedMessages:Number.POSITIVE_INFINITY,startClosed:!1,debug:!1},Ro=!1,Qi=class rt extends EventTarget{constructor(t,r,n={}){super();O(this,"_ws");O(this,"_retryCount",-1);O(this,"_uptimeTimeout");O(this,"_connectTimeout");O(this,"_shouldReconnect",!0);O(this,"_connectLock",!1);O(this,"_binaryType","blob");O(this,"_closeCalled",!1);O(this,"_messageQueue",[]);O(this,"_debugLogger",console.log.bind(console));O(this,"_url");O(this,"_protocols");O(this,"_options");O(this,"onclose",null);O(this,"onerror",null);O(this,"onmessage",null);O(this,"onopen",null);O(this,"_handleOpen",t=>{this._debug("open event");let{minUptime:r=it.minUptime}=this._options;clearTimeout(this._connectTimeout),this._uptimeTimeout=setTimeout(()=>this._acceptOpen(),r),xl(this._ws,"WebSocket is not defined"),this._ws.binaryType=this._binaryType,this._messageQueue.forEach(n=>{var s;return(s=this._ws)==null?void 0:s.send(n)}),this._messageQueue=[],this.onopen&&this.onopen(t),this.dispatchEvent(Xi(t))});O(this,"_handleMessage",t=>{this._debug("message event"),this.onmessage&&this.onmessage(t),this.dispatchEvent(Xi(t))});O(this,"_handleError",t=>{this._debug("error event",t.message),this._disconnect(void 0,t.message==="TIMEOUT"?"timeout":void 0),this.onerror&&this.onerror(t),this._debug("exec error listeners"),this.dispatchEvent(Xi(t)),this._connect()});O(this,"_handleClose",t=>{this._debug("close event"),this._clearTimeouts(),this._shouldReconnect&&this._connect(),this.onclose&&this.onclose(t),this.dispatchEvent(Xi(t))});this._url=t,this._protocols=r,this._options=n,this._options.startClosed&&(this._shouldReconnect=!1),this._options.debugLogger&&(this._debugLogger=this._options.debugLogger),this._connect()}static get CONNECTING(){return 0}static get OPEN(){return 1}static get CLOSING(){return 2}static get CLOSED(){return 3}get CONNECTING(){return rt.CONNECTING}get OPEN(){return rt.OPEN}get CLOSING(){return rt.CLOSING}get CLOSED(){return rt.CLOSED}get binaryType(){return this._ws?this._ws.binaryType:this._binaryType}set binaryType(t){this._binaryType=t,this._ws&&(this._ws.binaryType=t)}get retryCount(){return Math.max(this._retryCount,0)}get bufferedAmount(){return this._messageQueue.reduce((r,n)=>(typeof n=="string"?r+=n.length:n instanceof Blob?r+=n.size:r+=n.byteLength,r),0)+(this._ws?this._ws.bufferedAmount:0)}get extensions(){return this._ws?this._ws.extensions:""}get protocol(){return this._ws?this._ws.protocol:""}get readyState(){return this._ws?this._ws.readyState:this._options.startClosed?rt.CLOSED:rt.CONNECTING}get url(){return this._ws?this._ws.url:""}get shouldReconnect(){return this._shouldReconnect}close(t=1e3,r){if(this._closeCalled=!0,this._shouldReconnect=!1,this._clearTimeouts(),!this._ws){this._debug("close enqueued: no ws instance");return}if(this._ws.readyState===this.CLOSED){this._debug("close: already closed");return}this._ws.close(t,r)}reconnect(t,r){this._shouldReconnect=!0,this._closeCalled=!1,this._retryCount=-1,!this._ws||this._ws.readyState===this.CLOSED?this._connect():(this._disconnect(t,r),this._connect())}send(t){if(this._ws&&this._ws.readyState===this.OPEN)this._debug("send",t),this._ws.send(t);else{let{maxEnqueuedMessages:r=it.maxEnqueuedMessages}=this._options;this._messageQueue.length<r&&(this._debug("enqueue",t),this._messageQueue.push(t))}}_debug(...t){this._options.debug&&this._debugLogger("RWS>",...t)}_getNextDelay(){let{reconnectionDelayGrowFactor:t=it.reconnectionDelayGrowFactor,minReconnectionDelay:r=it.minReconnectionDelay,maxReconnectionDelay:n=it.maxReconnectionDelay}=this._options,s=0;return this._retryCount>0&&(s=r*dn(t,this._retryCount-1),s>n&&(s=n)),this._debug("next delay",s),s}_wait(){return new Promise(t=>{setTimeout(t,this._getNextDelay())})}_getNextProtocols(t){if(!t)return Promise.resolve(null);if(typeof t=="string"||Array.isArray(t))return Promise.resolve(t);if(typeof t=="function"){let r=t();if(!r)return Promise.resolve(null);if(typeof r=="string"||Array.isArray(r))return Promise.resolve(r);if(r.then)return r}throw Error("Invalid protocols")}_getNextUrl(t){if(typeof t=="string")return Promise.resolve(t);if(typeof t=="function"){let r=t();if(typeof r=="string")return Promise.resolve(r);if(r.then)return r}throw Error("Invalid URL")}_connect(){if(this._connectLock||!this._shouldReconnect)return;this._connectLock=!0;let{maxRetries:t=it.maxRetries,connectionTimeout:r=it.connectionTimeout}=this._options;if(this._retryCount>=t){this._debug("max retries reached",this._retryCount,">=",t);return}this._retryCount++,this._debug("connect",this._retryCount),this._removeListeners(),this._wait().then(()=>Promise.all([this._getNextUrl(this._url),this._getNextProtocols(this._protocols||null)])).then(([n,s])=>{if(this._closeCalled){this._connectLock=!1;return}!this._options.WebSocket&&typeof WebSocket=="undefined"&&!Ro&&(console.error(`\u203C\uFE0F No WebSocket implementation available. You should define options.WebSocket. 

For example, if you're using node.js, run \`npm install ws\`, and then in your code:

import PartySocket from 'partysocket';
import WS from 'ws';

const partysocket = new PartySocket({
  host: "127.0.0.1:1999",
  room: "test-room",
  WebSocket: WS
});

`),Ro=!0);let o=this._options.WebSocket||WebSocket;this._debug("connect",{url:n,protocols:s}),this._ws=s?new o(n,s):new o(n),this._ws.binaryType=this._binaryType,this._connectLock=!1,this._addListeners(),this._connectTimeout=setTimeout(()=>this._handleTimeout(),r)}).catch(n=>{this._connectLock=!1,this._handleError(new an.ErrorEvent(Error(n.message),this))})}_handleTimeout(){this._debug("timeout event"),this._handleError(new an.ErrorEvent(Error("TIMEOUT"),this))}_disconnect(t=1e3,r){if(this._clearTimeouts(),!!this._ws){this._removeListeners();try{(this._ws.readyState===this.OPEN||this._ws.readyState===this.CONNECTING)&&this._ws.close(t,r),this._handleClose(new an.CloseEvent(t,r,this))}catch(n){}}}_acceptOpen(){this._debug("accept open"),this._retryCount=0}_removeListeners(){this._ws&&(this._debug("removeListeners"),this._ws.removeEventListener("open",this._handleOpen),this._ws.removeEventListener("close",this._handleClose),this._ws.removeEventListener("message",this._handleMessage),this._ws.removeEventListener("error",this._handleError))}_addListeners(){this._ws&&(this._debug("addListeners"),this._ws.addEventListener("open",this._handleOpen),this._ws.addEventListener("close",this._handleClose),this._ws.addEventListener("message",this._handleMessage),this._ws.addEventListener("error",this._handleError))}_clearTimeouts(){clearTimeout(this._connectTimeout),clearTimeout(this._uptimeTimeout)}};var ni="wss://socket.novu.co",Io="notifications.notification_received",Po="notifications.unseen_count_changed",Lo="notifications.unread_count_changed",Al=({_id:i,content:e,read:t,archived:r,snoozedUntil:n,deliveredAt:s,createdAt:o,lastReadDate:c,archivedAt:h,channel:f,subscriber:a,subject:u,avatar:d,cta:_,tags:b,data:g,workflow:w})=>{var A,M,q,ee,Ve,He,X,Pe;let T={id:a==null?void 0:a._id,subscriberId:a==null?void 0:a.subscriberId,firstName:a==null?void 0:a.firstName,lastName:a==null?void 0:a.lastName,avatar:a==null?void 0:a.avatar,locale:a==null?void 0:a.locale,data:a==null?void 0:a.data,timezone:a==null?void 0:a.timezone,email:a==null?void 0:a.email,phone:a==null?void 0:a.phone},x=(M=(A=_.action)==null?void 0:A.buttons)==null?void 0:M.find(_e=>_e.type==="primary"),p=(ee=(q=_.action)==null?void 0:q.buttons)==null?void 0:ee.find(_e=>_e.type==="secondary"),y=(He=(Ve=_.action)==null?void 0:Ve.result)==null?void 0:He.type,B=(X=_.action)==null?void 0:X.status;return R(E(E({id:i,subject:u,body:e,to:T,isRead:t,isArchived:r,isSnoozed:!!n},s&&{deliveredAt:s}),n&&{snoozedUntil:n}),{createdAt:o,readAt:c,archivedAt:h,avatar:d,primaryAction:x&&{label:x.content,isCompleted:y==="primary"&&B==="done",redirect:x.url?{target:x.target,url:x.url}:void 0},secondaryAction:p&&{label:p.content,isCompleted:y==="secondary"&&B==="done",redirect:p.url?{target:p.target,url:p.url}:void 0},channelType:f,tags:b,redirect:(Pe=_.data)!=null&&Pe.url?{url:_.data.url,target:_.data.target}:void 0,data:g,workflow:w})},xt,ye,ve,Ct,er,tr,ir,rr,xe,Fo,cn,ln,Zi=class extends Ee{constructor({socketUrl:t,inboxServiceInstance:r,eventEmitterInstance:n}){super({eventEmitterInstance:n,inboxServiceInstance:r});v(this,xe);v(this,xt);v(this,ye);v(this,ve);v(this,Ct);v(this,er,t=>{try{let r=JSON.parse(t.data);r.event==="notification_received"&&l(this,ye).emit(Io,{result:new U(Al(r.data.message),l(this,ye),this._inboxService)})}catch(r){console.log("error",r)}});v(this,tr,t=>{try{let r=JSON.parse(t.data);r.event==="unseen_count_changed"&&l(this,ye).emit(Po,{result:r.data.unseenCount})}catch(r){}});v(this,ir,t=>{try{let r=JSON.parse(t.data);r.event==="unread_count_changed"&&l(this,ye).emit(Lo,{result:r.data.unreadCount})}catch(r){}});v(this,rr,t=>{try{switch(JSON.parse(t.data).event){case"notification_received":l(this,er).call(this,t);break;case"unseen_count_changed":l(this,tr).call(this,t);break;case"unread_count_changed":l(this,ir).call(this,t);break;default:}}catch(r){}});S(this,ye,n),S(this,Ct,t!=null?t:ni)}onSessionSuccess({token:t}){S(this,xt,t)}isSocketEvent(t){return t===Io||t===Po||t===Lo}connect(){return m(this,null,function*(){return l(this,xt)?le(this,xe,cn).call(this):this.callWithSession(le(this,xe,cn).bind(this))})}disconnect(){return m(this,null,function*(){return l(this,ve)?le(this,xe,ln).call(this):this.callWithSession(le(this,xe,ln).bind(this))})}};xt=new WeakMap,ye=new WeakMap,ve=new WeakMap,Ct=new WeakMap,er=new WeakMap,tr=new WeakMap,ir=new WeakMap,rr=new WeakMap,xe=new WeakSet,Fo=function(){return m(this,null,function*(){if(l(this,ve))return;let t={socketUrl:l(this,Ct)};l(this,ye).emit("socket.connect.pending",{args:t});let r=new URL(l(this,Ct));r.searchParams.set("token",l(this,xt)),S(this,ve,new Qi(r.toString())),l(this,ve).addEventListener("open",()=>{l(this,ye).emit("socket.connect.resolved",{args:t})}),l(this,ve).addEventListener("error",n=>{l(this,ye).emit("socket.connect.resolved",{args:t,error:n})}),l(this,ve).addEventListener("message",l(this,rr))})},cn=function(){return m(this,null,function*(){try{return yield le(this,xe,Fo).call(this),{}}catch(t){return{error:new k("Failed to initialize the PartySocket",t)}}})},ln=function(){return m(this,null,function*(){var t;try{return(t=l(this,ve))==null||t.close(),S(this,ve,void 0),{}}catch(r){return{error:new k("Failed to disconnect from the PartySocket",r)}}})};var Rl=["wss://eu.socket.novu.co",ni,"wss://socket.novu-staging.co","wss://socket-worker-local.cli-shortener.workers.dev"],Tl={"https://eu.ws.novu.co":"wss://eu.socket.novu.co","https://ws.novu.co":ni,"https://dev.ws.novu.co":"wss://socket.novu-staging.co"};function Ol(i){return i?Tl[i]||i:ni}function Il(i){return!i||Rl.includes(i)}function Uo({socketUrl:i,inboxServiceInstance:e,eventEmitterInstance:t}){let r=Ol(i);switch(Il(r)?"partysocket":"socket.io"){case"partysocket":return new Zi({socketUrl:r,inboxServiceInstance:e,eventEmitterInstance:t});case"socket.io":default:return new Gi({socketUrl:r,inboxServiceInstance:e,eventEmitterInstance:t})}}var Ce,Ie,ze,Nt,nr=class{constructor(e){v(this,Ce);v(this,Ie);v(this,ze);v(this,Nt);var r,n;S(this,ze,new li({apiUrl:e.apiUrl||e.backendUrl,userAgent:e.__userAgent})),S(this,Ce,new hi),S(this,Ie,new Si({applicationIdentifier:e.applicationIdentifier||"",subscriberHash:e.subscriberHash,subscriber:Do(e)},l(this,ze),l(this,Ce)));let t=Do(e);S(this,Nt,t.subscriberId),l(this,Ie).initialize(),this.notifications=new yi({useCache:(r=e.useCache)!=null?r:!0,inboxServiceInstance:l(this,ze),eventEmitterInstance:l(this,Ce)}),this.preferences=new Ei({useCache:(n=e.useCache)!=null?n:!0,inboxServiceInstance:l(this,ze),eventEmitterInstance:l(this,Ce)}),this.socket=Uo({socketUrl:e.socketUrl,eventEmitterInstance:l(this,Ce),inboxServiceInstance:l(this,ze)}),this.on=(s,o)=>{this.socket.isSocketEvent(s)&&this.socket.connect();let c=l(this,Ce).on(s,o);return()=>{c()}},this.off=(s,o)=>{l(this,Ce).off(s,o)}}get applicationIdentifier(){return l(this,Ie).applicationIdentifier}get subscriberId(){return l(this,Ie).subscriberId}changeSubscriber(e){return m(this,null,function*(){l(this,Nt)!==e.subscriber.subscriberId&&(yield l(this,Ie).initialize({applicationIdentifier:l(this,Ie).applicationIdentifier||"",subscriberHash:e.subscriberHash,subscriber:e.subscriber}),S(this,Nt,e.subscriber.subscriberId))})}};Ce=new WeakMap,Ie=new WeakMap,ze=new WeakMap,Nt=new WeakMap;function Do(i){return i.subscriber?typeof i.subscriber=="string"?{subscriberId:i.subscriber}:i.subscriber:i.subscriberId?{subscriberId:i.subscriberId}:{subscriberId:""}}window.Novu=nr;})();
/*! Bundled license information:

xmlhttprequest-ssl/lib/XMLHttpRequest.js:
  (**
   * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.
   *
   * This can be used with JS designed for browsers to improve reuse of code and
   * allow the use of existing libraries.
   *
   * Usage: include("XMLHttpRequest.js") and use XMLHttpRequest per W3C specs.
   *
   * <AUTHOR> DeFelippi <<EMAIL>>
   * @contributor David Ellis <<EMAIL>>
   * @license MIT
   *)

partysocket/dist/chunk-ZCZZNAX5.mjs:
  (*!
   * Reconnecting WebSocket
   * by Pedro Ladaria <<EMAIL>>
   * https://github.com/pladaria/reconnecting-websocket
   * License MIT
   *)
*/
