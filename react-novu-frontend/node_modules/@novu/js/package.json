{"name": "@novu/js", "version": "3.6.0", "repository": "https://github.com/novuhq/novu", "description": "Novu JavaScript SDK for <Inbox />", "author": "", "license": "ISC", "main": "dist/cjs/index.js", "module": "dist/esm/index.mjs", "types": "dist/cjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/esm/index.d.mts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./ui": {"import": {"types": "./dist/esm/ui/index.d.mts", "default": "./dist/esm/ui/index.mjs"}, "require": {"types": "./dist/cjs/ui/index.d.ts", "default": "./dist/cjs/ui/index.js"}}, "./themes": {"import": {"types": "./dist/esm/themes/index.d.mts", "default": "./dist/esm/themes/index.mjs"}, "require": {"types": "./dist/cjs/themes/index.d.ts", "default": "./dist/cjs/themes/index.js"}}, "./internal": {"import": {"types": "./dist/esm/internal/index.d.mts", "default": "./dist/esm/internal/index.mjs"}, "require": {"types": "./dist/cjs/internal/index.d.ts", "default": "./dist/cjs/internal/index.js"}}}, "files": ["dist/cjs", "dist/esm", "dist/index.css", "dist/novu.min.js", "dist/novu.min.js.gz", "ui/**/*", "themes/**/*", "internal/**/*"], "sideEffects": false, "private": false, "publishConfig": {"access": "public"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@types/jest": "^29.2.3", "@types/node": "^20.15.0", "autoprefixer": "^10.4.0", "bytes-iec": "^3.1.1", "chalk": "^5.3.0", "compression-webpack-plugin": "^10.0.0", "concurrently": "^5.3.0", "cssnano": "^7.0.4", "esbuild-plugin-compress": "^1.0.1", "esbuild-plugin-inline-import": "^1.0.4", "esbuild-plugin-solid": "^0.6.0", "http-server": "^0.13.0", "jest": "^29.3.1", "postcss": "^8.4.38", "postcss-load-config": "^6.0.1", "postcss-prefix-selector": "^1.16.1", "postcss-preset-env": "^9.5.14", "prettier-plugin-tailwindcss": "^0.6.5", "solid-devtools": "^0.29.2", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "terser-webpack-plugin": "^5.3.9", "tiny-glob": "^0.2.9", "ts-jest": "^29.0.3", "ts-loader": "~9.4.0", "tsup": "^8.1.0", "tsup-preset-solid": "^2.2.0", "typescript": "5.6.2", "webpack": "^5.74.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "mitt": "^3.0.1", "partysocket": "^1.1.4", "socket.io-client": "4.7.2", "solid-floating-ui": "^0.3.1", "solid-js": "^1.9.4", "solid-motionone": "^1.0.3", "tailwind-merge": "^2.4.0"}, "nx": {"tags": ["type:package"]}, "scripts": {"clean": "rimraf ./dist", "start:server": "http-server ./dist -p 4010", "prebuild": "cp ./src/ui/index.css ./src/ui/index.directcss", "build": "pnpm run clean && NODE_ENV=production tsup", "postbuild": "rm ./src/ui/index.directcss && ./scripts/copy-package-json.sh && node scripts/size-limit.mjs && pnpm run check-exports", "build:umd": "webpack --config webpack.config.cjs", "build:watch": "concurrently \"pnpm run prebuild\" \"NODE_ENV=development pnpm run tsup:watch\" \"pnpm run start:server\"", "tsup:watch": "tsup --watch", "check-exports": "attw --pack .", "lint": "eslint src", "lint:fix": "pnpm lint -- --fix", "test": "jest", "publish:rc": "pnpm publish --tag rc"}}