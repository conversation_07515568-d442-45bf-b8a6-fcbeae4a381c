{"name": "solid-motionone", "version": "1.0.4", "description": "A tiny, performant animation library for SolidJS", "license": "MIT", "author": "<PERSON> <<EMAIL>>; <PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "Phil <phil.s<PERSON><PERSON><PERSON><PERSON><PERSON>@gridventures.de>"], "private": false, "sideEffects": false, "type": "module", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "browser": {}, "exports": {"solid": "./dist/index.jsx", "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "typesVersions": {}, "dependencies": {"@motionone/dom": "^10.17.0", "@motionone/utils": "^10.17.0", "@solid-primitives/props": "^3.1.11", "@solid-primitives/refs": "^1.0.8", "@solid-primitives/transition-group": "^1.0.5", "csstype": "^3.1.3"}, "devDependencies": {"@babel/preset-env": "^7.23.7", "@babel/preset-typescript": "^7.23.3", "@jest/types": "^29.6.3", "@solidjs/testing-library": "^0.8.5", "@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "babel-jest": "^29.7.0", "babel-preset-solid": "^1.8.6", "enhanced-resolve-jest": "^1.1.0", "eslint": "^8.56.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-no-only-tests": "^3.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.1", "solid-js": "^1.8.17", "tsup": "^8.0.1", "tsup-preset-solid": "^2.2.0", "typescript": "^5.3.3"}, "peerDependencies": {"solid-js": "^1.8.0"}, "scripts": {"build": "tsup", "test": "pnpm run test:client && pnpm run test:ssr", "test:client": "jest --config jest/jest.config.cjs", "test:ssr": "SSR=true jest --config jest/jest.config.cjs", "format": "prettier --cache -w .", "lint": "pnpm run lint:code & pnpm run lint:types", "lint:code": "eslint --ignore-path .gitignore --max-warnings 0 src/**/*.{js,ts,tsx,jsx}", "lint:types": "tsc --noEmit"}}