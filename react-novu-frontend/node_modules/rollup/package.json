{"name": "rollup", "version": "4.45.1", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "types": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "napi": {"name": "rollup", "package": {"name": "@rollup/rollup"}, "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-freebsd", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabihf", "armv7-unknown-linux-musleabihf", "i686-pc-windows-msvc", "loongarch64-unknown-linux-gnu", "riscv64gc-unknown-linux-gnu", "riscv64gc-unknown-linux-musl", "powerpc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl"]}}, "scripts": {"build": "concurrently -c green,blue \"npm run build:wasm\" \"npm:build:ast-converters\" && concurrently -c green,blue \"npm run build:napi -- --release\" \"npm:build:js\" && npm run build:copy-native", "build:quick": "concurrently -c green,blue 'npm:build:napi' 'npm:build:cjs' && npm run build:copy-native", "build:napi": "napi build --platform --dts native.d.ts --js false --cargo-cwd rust -p bindings_napi --cargo-name bindings_napi", "build:wasm": "cross-env RUSTFLAGS=\"-C opt-level=z\" wasm-pack build rust/bindings_wasm --out-dir ../../wasm --target web --no-pack && shx rm wasm/.gitignore", "build:wasm:node": "wasm-pack build rust/bindings_wasm --out-dir ../../wasm-node --target nodejs --no-pack && shx rm wasm-node/.gitignore", "update:napi": "npm run build:napi && npm run build:copy-native", "build:js": "rollup --config rollup.config.ts --configPlugin typescript --forceExit", "build:js:node": "rollup --config rollup.config.ts --configPlugin typescript --configIsBuildNode --forceExit", "build:prepare": "concurrently -c green,blue \"npm run build:napi -- --release\" \"npm:build:js:node\" && npm run build:copy-native", "update:js": "npm run build:js && npm run build:copy-native", "build:copy-native": "shx mkdir -p dist && shx cp rollup.*.node dist/", "dev": "concurrently -kc green,blue 'nodemon --watch rust -e rs --exec \"npm run build:wasm\"' 'vitepress dev docs'", "build:cjs": "rollup --config rollup.config.ts --configPlugin typescript --configTest --forceExit", "build:bootstrap": "shx mv dist dist-build && node dist-build/bin/rollup --config rollup.config.ts --configPlugin typescript --forceExit && shx rm -rf dist-build", "build:bootstrap:cjs": "shx mv dist dist-build && node dist-build/bin/rollup --config rollup.config.ts --configPlugin typescript --configTest --forceExit && shx rm -rf dist-build", "build:docs": "vitepress build docs", "build:ast-converters": "node scripts/generate-ast-converters.js", "preview:docs": "vitepress preview docs", "ci:artifacts": "napi artifacts", "ci:lint": "concurrently -c red,yellow,green,blue 'npm:lint:js:nofix' 'npm:lint:native-js' 'npm:lint:markdown:nofix' 'npm:lint:rust:nofix'", "ci:test:only": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && npm run test:only", "ci:test:all": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && concurrently --kill-others-on-fail -c green,blue,magenta,cyan 'npm:test:only' 'npm:test:typescript' 'npm:test:leak' 'npm:test:browser'", "ci:coverage": "npm run build:cjs && npm run build:copy-native && npm run build:bootstrap && npm run build:copy-native && NODE_OPTIONS=--no-experimental-require-module nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "concurrently -c red,yellow,green,blue 'npm:lint:js' 'npm:lint:native-js' 'npm:lint:markdown' 'npm:lint:rust'", "lint:js": "eslint . --fix --cache", "lint:js:nofix": "eslint . --cache", "lint:native-js": "node scripts/lint-native-js.js", "lint:markdown": "prettier --write \"**/*.md\"", "lint:markdown:nofix": "prettier --check \"**/*.md\"", "lint:rust": "cd rust && cargo fmt && cargo clippy --fix --allow-dirty", "lint:rust:nofix": "cd rust && cargo fmt --check && cargo clippy", "perf": "npm run build:bootstrap:cjs && node --expose-gc scripts/perf-report/index.js", "prepare": "husky && node scripts/check-release.js || npm run build:prepare", "prepublishOnly": "node scripts/check-release.js && node scripts/prepublish.js", "postpublish": "node scripts/postpublish.js", "prepublish:napi": "napi prepublish --skip-gh-release", "release": "node scripts/prepare-release.js", "release:docs": "git fetch --update-head-ok origin master:master && git branch --force documentation-published master && git push origin documentation-published", "test": "npm run build && npm run test:all", "test:update-snapshots": "node scripts/update-snapshots.js", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "concurrently --kill-others-on-fail -c green,blue,magenta,cyan,red 'npm:test:only' 'npm:test:browser' 'npm:test:typescript' 'npm:test:package' 'npm:test:options'", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "npm install --no-save weak-napi && node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:options": "node scripts/test-options.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit && tsc --noEmit -p scripts", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2", "@rollup/rollup-darwin-arm64": "4.45.1", "@rollup/rollup-android-arm64": "4.45.1", "@rollup/rollup-win32-arm64-msvc": "4.45.1", "@rollup/rollup-freebsd-arm64": "4.45.1", "@rollup/rollup-linux-arm64-gnu": "4.45.1", "@rollup/rollup-linux-arm64-musl": "4.45.1", "@rollup/rollup-android-arm-eabi": "4.45.1", "@rollup/rollup-linux-arm-gnueabihf": "4.45.1", "@rollup/rollup-linux-arm-musleabihf": "4.45.1", "@rollup/rollup-win32-ia32-msvc": "4.45.1", "@rollup/rollup-linux-loongarch64-gnu": "4.45.1", "@rollup/rollup-linux-riscv64-gnu": "4.45.1", "@rollup/rollup-linux-riscv64-musl": "4.45.1", "@rollup/rollup-linux-powerpc64le-gnu": "4.45.1", "@rollup/rollup-linux-s390x-gnu": "4.45.1", "@rollup/rollup-darwin-x64": "4.45.1", "@rollup/rollup-win32-x64-msvc": "4.45.1", "@rollup/rollup-freebsd-x64": "4.45.1", "@rollup/rollup-linux-x64-gnu": "4.45.1", "@rollup/rollup-linux-x64-musl": "4.45.1"}, "dependencies": {"@types/estree": "1.0.8"}, "devDependenciesComments": {"core-js": "We only update manually as every update requires a snapshot update"}, "devDependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/language": "^6.11.2", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.38.0", "@eslint/js": "^9.30.0", "@inquirer/prompts": "^7.5.3", "@jridgewell/sourcemap-codec": "^1.5.3", "@mermaid-js/mermaid-cli": "^11.6.0", "@napi-rs/cli": "^2.18.4", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-buble": "^1.0.3", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.4", "@rollup/pluginutils": "^5.2.0", "@shikijs/vitepress-twoslash": "^3.7.0", "@types/mocha": "^10.0.10", "@types/node": "^20.19.0", "@types/picomatch": "^4.0.0", "@types/semver": "^7.7.0", "@types/yargs-parser": "^21.0.3", "@vue/language-server": "^2.2.10", "acorn": "^8.15.0", "acorn-import-assertions": "^1.9.0", "acorn-jsx": "^5.3.2", "buble": "^0.20.0", "builtin-modules": "^5.0.0", "chokidar": "^3.6.0", "concurrently": "^9.2.0", "core-js": "3.38.1", "cross-env": "^7.0.3", "date-time": "^4.0.0", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-vue": "^10.2.0", "fixturify": "^3.0.0", "flru": "^1.0.2", "fs-extra": "^11.3.0", "github-api": "^3.4.0", "globals": "^16.2.0", "husky": "^9.1.7", "is-reference": "^3.0.3", "lint-staged": "^16.1.2", "locate-character": "^3.0.0", "magic-string": "^0.30.17", "memfs": "^4.17.2", "mocha": "^11.7.1", "nodemon": "^3.1.10", "nyc": "^17.1.0", "picocolors": "^1.1.1", "picomatch": "^4.0.2", "pinia": "^3.0.3", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "pretty-bytes": "^7.0.0", "pretty-ms": "^9.2.0", "requirejs": "^2.3.7", "rollup": "^4.44.1", "rollup-plugin-license": "^3.6.0", "rollup-plugin-string": "^3.0.0", "semver": "^7.7.2", "shx": "^0.4.0", "signal-exit": "^4.1.0", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "systemjs": "^6.15.1", "terser": "^5.43.1", "tslib": "^2.8.1", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.0", "vitepress": "^1.6.3", "vue": "^3.5.17", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^2.2.10", "wasm-pack": "^0.13.1", "yargs-parser": "^21.1.1"}, "overrides": {"axios": "^1.10.0", "semver": "^7.7.2", "readable-stream": "npm:@built-in/readable-stream@1", "esbuild": ">0.24.2"}, "comments": {"vue-tsc": "This is necessary so that prettier-plugin-organize-imports works correctly in Vue templatges"}, "files": ["dist/*.node", "dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/es/package.json"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "exports": {".": {"types": "./dist/rollup.d.ts", "import": "./dist/es/rollup.js", "require": "./dist/rollup.js"}, "./loadConfigFile": {"types": "./dist/loadConfigFile.d.ts", "require": "./dist/loadConfigFile.js", "default": "./dist/loadConfigFile.js"}, "./getLogFilter": {"types": "./dist/getLogFilter.d.ts", "import": "./dist/es/getLogFilter.js", "require": "./dist/getLogFilter.js"}, "./parseAst": {"types": "./dist/parseAst.d.ts", "import": "./dist/es/parseAst.js", "require": "./dist/parseAst.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}}