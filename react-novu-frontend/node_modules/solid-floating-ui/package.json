{"name": "solid-floating-ui", "type": "module", "version": "0.3.1", "files": ["dist", "src"], "engines": {"node": ">=10"}, "license": "MIT", "keywords": ["pridepack"], "devDependencies": {"@floating-ui/dom": "^1.5.4", "@types/node": "^20.11.5", "pridepack": "2.6.0", "solid-js": "^1.8.11", "tslib": "^2.6.0", "typescript": "^5.3.3"}, "peerDependencies": {"@floating-ui/dom": "^1.5", "solid-js": "^1.8"}, "scripts": {"prepublishOnly": "pridepack clean && pridepack build", "build": "pridepack build", "type-check": "pridepack check", "clean": "pridepack clean", "watch": "pridepack watch", "start": "pridepack start", "dev": "pridepack dev", "test": "vitest"}, "private": false, "description": "SolidJS bindings for Floating UI", "repository": {"url": "https://github.com/lxsmnsyc/solid-floating-ui.git", "type": "git"}, "homepage": "https://github.com/lxsmnsyc/solid-floating-ui/tree/main/packages/solid-floating-ui", "bugs": {"url": "https://github.com/lxsmnsyc/solid-floating-ui/issues"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "exports": {".": {"development": {"require": "./dist/cjs/development/index.cjs", "import": "./dist/esm/development/index.mjs"}, "require": "./dist/cjs/production/index.cjs", "import": "./dist/esm/production/index.mjs", "types": "./dist/types/index.d.ts"}}, "typesVersions": {"*": {}}, "types": "./dist/types/index.d.ts", "main": "./dist/cjs/production/index.cjs", "module": "./dist/esm/production/index.mjs", "gitHead": "1dae1e4781dfb25727806464290d9e16a7d264b5"}