import{createEffect as f,createMemo as R,createSignal as g,onCleanup as y}from"solid-js";import{computePosition as E}from"@floating-ui/dom";function C(l,u,e){let c=()=>{var t;return(t=e==null?void 0:e.placement)!=null?t:"bottom"},d=()=>{var t;return(t=e==null?void 0:e.strategy)!=null?t:"absolute"},[n,o]=g({x:null,y:null,placement:c(),strategy:d(),middlewareData:{}}),[x,F]=g();f(()=>{let t=x();if(t)throw t.value});let s=R(()=>(l(),u(),{}));function m(){let t=l(),r=u();if(t&&r){let a=s();E(t,r,{middleware:e==null?void 0:e.middleware,placement:c(),strategy:d()}).then(i=>{a===s()&&o(i)},i=>{F(i)})}}return f(()=>{let t=l(),r=u();if(e==null||e.middleware,c(),d(),t&&r)if(e!=null&&e.whileElementsMounted){let a=e.whileElementsMounted(t,r,m);a&&y(a)}else m()}),{get x(){return n().x},get y(){return n().y},get placement(){return n().placement},get strategy(){return n().strategy},get middlewareData(){return n().middlewareData},update:m}}export{C as useFloating};
