// src/index.ts
import { createEffect, createMemo, createSignal, onCleanup } from "solid-js";
import { computePosition } from "@floating-ui/dom";
function useFloating(reference, floating, options) {
  const placement = () => {
    var _a;
    return (_a = options == null ? void 0 : options.placement) != null ? _a : "bottom";
  };
  const strategy = () => {
    var _a;
    return (_a = options == null ? void 0 : options.strategy) != null ? _a : "absolute";
  };
  const [data, setData] = createSignal({
    x: null,
    y: null,
    placement: placement(),
    strategy: strategy(),
    middlewareData: {}
  });
  const [error, setError] = createSignal();
  createEffect(() => {
    const currentError = error();
    if (currentError) {
      throw currentError.value;
    }
  });
  const version = createMemo(() => {
    reference();
    floating();
    return {};
  });
  function update() {
    const currentReference = reference();
    const currentFloating = floating();
    if (currentReference && currentFloating) {
      const capturedVersion = version();
      computePosition(currentReference, currentFloating, {
        middleware: options == null ? void 0 : options.middleware,
        placement: placement(),
        strategy: strategy()
      }).then(
        (currentData) => {
          if (capturedVersion === version()) {
            setData(currentData);
          }
        },
        (err) => {
          setError(err);
        }
      );
    }
  }
  createEffect(() => {
    const currentReference = reference();
    const currentFloating = floating();
    options == null ? void 0 : options.middleware;
    placement();
    strategy();
    if (currentReference && currentFloating) {
      if (options == null ? void 0 : options.whileElementsMounted) {
        const cleanup = options.whileElementsMounted(
          currentReference,
          currentFloating,
          update
        );
        if (cleanup) {
          onCleanup(cleanup);
        }
      } else {
        update();
      }
    }
  });
  return {
    get x() {
      return data().x;
    },
    get y() {
      return data().y;
    },
    get placement() {
      return data().placement;
    },
    get strategy() {
      return data().strategy;
    },
    get middlewareData() {
      return data().middlewareData;
    },
    update
  };
}
export {
  useFloating
};
//# sourceMappingURL=index.mjs.map
