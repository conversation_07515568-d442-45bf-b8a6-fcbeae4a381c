{"version": 3, "sources": ["../../../src/index.ts"], "sourcesContent": ["import { createEffect, createMemo, createSignal, onCleanup } from 'solid-js';\nimport type {\n  ComputePositionConfig,\n  ComputePositionReturn,\n  ReferenceElement,\n} from '@floating-ui/dom';\nimport { computePosition } from '@floating-ui/dom';\n\nexport interface UseFloatingOptions<\n  R extends ReferenceElement,\n  F extends HTMLElement,\n> extends Partial<ComputePositionConfig> {\n  whileElementsMounted?: (\n    reference: R,\n    floating: F,\n    update: () => void,\n    // biome-ignore lint/suspicious/noConfusingVoidType: void is a valid return type\n  ) => void | (() => void);\n}\n\ninterface UseFloatingState extends Omit<ComputePositionReturn, 'x' | 'y'> {\n  x?: number | null;\n  y?: number | null;\n}\n\nexport interface UseFloatingResult extends UseFloatingState {\n  update(): void;\n}\n\nexport function useFloating<R extends ReferenceElement, F extends HTMLElement>(\n  reference: () => R | undefined | null,\n  floating: () => F | undefined | null,\n  options?: UseFloatingOptions<R, F>,\n): UseFloatingResult {\n  const placement = () => options?.placement ?? 'bottom';\n  const strategy = () => options?.strategy ?? 'absolute';\n\n  const [data, setData] = createSignal<UseFloatingState>({\n    x: null,\n    y: null,\n    placement: placement(),\n    strategy: strategy(),\n    middlewareData: {},\n  });\n\n  const [error, setError] = createSignal<{ value: any } | undefined>();\n\n  createEffect(() => {\n    const currentError = error();\n    if (currentError) {\n      throw currentError.value;\n    }\n  });\n\n  const version = createMemo(() => {\n    reference();\n    floating();\n    return {};\n  });\n\n  function update() {\n    const currentReference = reference();\n    const currentFloating = floating();\n\n    if (currentReference && currentFloating) {\n      const capturedVersion = version();\n      computePosition(currentReference, currentFloating, {\n        middleware: options?.middleware,\n        placement: placement(),\n        strategy: strategy(),\n      }).then(\n        currentData => {\n          // Check if it's still valid\n          if (capturedVersion === version()) {\n            setData(currentData);\n          }\n        },\n        err => {\n          setError(err);\n        },\n      );\n    }\n  }\n\n  createEffect(() => {\n    const currentReference = reference();\n    const currentFloating = floating();\n\n    options?.middleware;\n    placement();\n    strategy();\n\n    if (currentReference && currentFloating) {\n      if (options?.whileElementsMounted) {\n        const cleanup = options.whileElementsMounted(\n          currentReference,\n          currentFloating,\n          update,\n        );\n\n        if (cleanup) {\n          onCleanup(cleanup);\n        }\n      } else {\n        update();\n      }\n    }\n  });\n\n  return {\n    get x() {\n      return data().x;\n    },\n    get y() {\n      return data().y;\n    },\n    get placement() {\n      return data().placement;\n    },\n    get strategy() {\n      return data().strategy;\n    },\n    get middlewareData() {\n      return data().middlewareData;\n    },\n    update,\n  };\n}\n"], "mappings": ";AAAA,SAAS,cAAc,YAAY,cAAc,iBAAiB;AAMlE,SAAS,uBAAuB;AAuBzB,SAAS,YACd,WACA,UACA,SACmB;AACnB,QAAM,YAAY,MAAG;AAlCvB;AAkC0B,oDAAS,cAAT,YAAsB;AAAA;AAC9C,QAAM,WAAW,MAAG;AAnCtB;AAmCyB,oDAAS,aAAT,YAAqB;AAAA;AAE5C,QAAM,CAAC,MAAM,OAAO,IAAI,aAA+B;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,WAAW,UAAU;AAAA,IACrB,UAAU,SAAS;AAAA,IACnB,gBAAgB,CAAC;AAAA,EACnB,CAAC;AAED,QAAM,CAAC,OAAO,QAAQ,IAAI,aAAyC;AAEnE,eAAa,MAAM;AACjB,UAAM,eAAe,MAAM;AAC3B,QAAI,cAAc;AAChB,YAAM,aAAa;AAAA,IACrB;AAAA,EACF,CAAC;AAED,QAAM,UAAU,WAAW,MAAM;AAC/B,cAAU;AACV,aAAS;AACT,WAAO,CAAC;AAAA,EACV,CAAC;AAED,WAAS,SAAS;AAChB,UAAM,mBAAmB,UAAU;AACnC,UAAM,kBAAkB,SAAS;AAEjC,QAAI,oBAAoB,iBAAiB;AACvC,YAAM,kBAAkB,QAAQ;AAChC,sBAAgB,kBAAkB,iBAAiB;AAAA,QACjD,YAAY,mCAAS;AAAA,QACrB,WAAW,UAAU;AAAA,QACrB,UAAU,SAAS;AAAA,MACrB,CAAC,EAAE;AAAA,QACD,iBAAe;AAEb,cAAI,oBAAoB,QAAQ,GAAG;AACjC,oBAAQ,WAAW;AAAA,UACrB;AAAA,QACF;AAAA,QACA,SAAO;AACL,mBAAS,GAAG;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,eAAa,MAAM;AACjB,UAAM,mBAAmB,UAAU;AACnC,UAAM,kBAAkB,SAAS;AAEjC,uCAAS;AACT,cAAU;AACV,aAAS;AAET,QAAI,oBAAoB,iBAAiB;AACvC,UAAI,mCAAS,sBAAsB;AACjC,cAAM,UAAU,QAAQ;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,YAAI,SAAS;AACX,oBAAU,OAAO;AAAA,QACnB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL,IAAI,IAAI;AACN,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,IACA,IAAI,IAAI;AACN,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,IACA,IAAI,YAAY;AACd,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,IACA,IAAI,WAAW;AACb,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,IACA,IAAI,iBAAiB;AACnB,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,IACA;AAAA,EACF;AACF;", "names": []}