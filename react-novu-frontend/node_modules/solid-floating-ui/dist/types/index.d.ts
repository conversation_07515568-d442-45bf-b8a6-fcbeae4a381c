import type { ComputePositionConfig, ComputePositionReturn, ReferenceElement } from '@floating-ui/dom';
export interface UseFloatingOptions<R extends ReferenceElement, F extends HTMLElement> extends Partial<ComputePositionConfig> {
    whileElementsMounted?: (reference: R, floating: F, update: () => void) => void | (() => void);
}
interface UseFloatingState extends Omit<ComputePositionReturn, 'x' | 'y'> {
    x?: number | null;
    y?: number | null;
}
export interface UseFloatingResult extends UseFloatingState {
    update(): void;
}
export declare function useFloating<R extends ReferenceElement, F extends HTMLElement>(reference: () => R | undefined | null, floating: () => F | undefined | null, options?: UseFloatingOptions<R, F>): UseFloatingResult;
export {};
