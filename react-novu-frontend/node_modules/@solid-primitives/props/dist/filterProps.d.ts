/**
 * An alternative primitive to Solid's [splitProps](https://www.solidjs.com/docs/latest/api#splitprops) allowing you to create a new props object with only the property names that match the predicate.
 *
 * The predicate is run for every property read lazily, instead of calculated eagerly like the original splitProps. Any signal accessed within the `predicate` will be tracked, and `predicate` re-executed if changed.
 *
 * @param props The props object to filter.
 * @param predicate A function that returns `true` if the property should be included in the filtered object.
 * @returns A new props object with only the properties that match the predicate.
 *
 * @example
 * ```tsx
 * const dataProps = filterProps(props, key => key.startsWith("data-"));
 * <div {...dataProps} />
 * ```
 */
export declare function filterProps<T extends object>(props: T, predicate: (key: keyof T) => boolean): T;
/**
 * Creates a predicate function that can be used to filter props by the prop name dynamically.
 *
 * The provided {@link predicate} function get's wrapped with a cache layer to prevent unnecessary re-evaluation. If one property is requested multiple times, the predicate function will only be evaluated once.
 *
 * The cache is only cleared when the keys of the props object change. *(when spreading props from a singal)*
 *
 * @param props The props object to filter.
 * @param predicate A function that returns `true` if the property should be included in the filtered object.
 * @returns A cached predicate function that filters the props object.
 *
 * @example
 * ```tsx
 * const predicate = createPropsPredicate(props, key => key.startsWith("data-"));
 * const dataProps = filterProps(props, predicate);
 * <div {...dataProps} />
 * ```
 */
export declare function createPropsPredicate<T extends object>(props: T, predicate: (key: keyof T) => boolean): (key: keyof T) => boolean;
