{"name": "@solid-primitives/props", "version": "3.2.2", "description": "Library of primitives focused around component props.", "author": "<PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "homepage": "https://primitives.solidjs.community/package/props", "repository": {"type": "git", "url": "git+https://github.com/solidjs-community/solid-primitives.git"}, "primitive": {"name": "props", "stage": 3, "list": ["combineProps", "filterProps"], "category": "Utilities"}, "keywords": ["props", "solid", "primitives"], "files": ["dist"], "private": false, "sideEffects": false, "type": "module", "module": "./dist/index.js", "types": "./dist/index.d.ts", "browser": {}, "exports": {"import": {"@solid-primitives/source": "./src/index.ts", "types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "dependencies": {"@solid-primitives/utils": "^6.3.2"}, "devDependencies": {"solid-js": "^1.9.7"}, "peerDependencies": {"solid-js": "^1.6.12"}, "typesVersions": {}, "scripts": {"dev": "node --import=@nothing-but/node-resolve-ts --experimental-transform-types ../../scripts/dev.ts", "build": "node --import=@nothing-but/node-resolve-ts --experimental-transform-types ../../scripts/build.ts", "vitest": "vitest -c ../../configs/vitest.config.ts", "test": "pnpm run vitest", "test:ssr": "pnpm run vitest --mode ssr"}}