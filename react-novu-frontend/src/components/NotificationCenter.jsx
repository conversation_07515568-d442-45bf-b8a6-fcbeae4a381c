import React from 'react';
import { Inbox } from '@novu/react';

export function NotificationCenter() {
  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <Inbox
        applicationIdentifier="YOUR_APPLICATION_IDENTIFIER"
        subscriberId="YOUR_SUBSCRIBER_ID"
        backendUrl="http://localhost:3000" // Novu API running on localhost:3000
        socketUrl="http://localhost:3002" // Novu WebSocket running on localhost:3002
        routerPush={(path) => {
          // Simple navigation - you can integrate with react-router if needed
          console.log('Navigate to:', path);
        }}
        appearance={{
          theme: 'light',
          variables: {
            colorPrimary: '#0081f1',
            colorPrimaryForeground: '#ffffff',
            colorSecondary: '#f1f5f9',
            colorSecondaryForeground: '#0f172a',
            colorCounter: '#e11d48',
            colorCounterForeground: '#ffffff',
            colorBackground: '#ffffff',
            colorForeground: '#0f172a',
            colorNeutral: '#f1f5f9',
            borderRadius: '0.5rem',
          },
        }}
      />
    </div>
  );
}
