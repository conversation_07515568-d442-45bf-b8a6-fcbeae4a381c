import { useState } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.css";
import { NotificationCenter } from "./components/NotificationCenter";

function App() {
	const [count, setCount] = useState(0);

	return (
		<>
			<div style={{ position: "relative" }}>
				<div
					style={{
						position: "absolute",
						top: "10px",
						right: "10px",
						zIndex: 1000,
					}}
				>
					<NotificationCenter />
				</div>
				<div>
					<a href="https://vite.dev" target="_blank">
						<img src={viteLogo} className="logo" alt="Vite logo" />
					</a>
					<a href="https://react.dev" target="_blank">
						<img src={reactLogo} className="logo react" alt="React logo" />
					</a>
				</div>
				<h1>Vite + React + Novu</h1>
				<div className="card">
					<button onClick={() => setCount((count) => count + 1)}>
						count is {count}
					</button>
					<p>
						Edit <code>src/App.jsx</code> and save to test HMR
					</p>
				</div>
				<p className="read-the-docs">
					Click on the Vite and React logos to learn more
				</p>
				<div
					style={{
						marginTop: "2rem",
						padding: "1rem",
						backgroundColor: "#f0f0f0",
						borderRadius: "8px",
					}}
				>
					<h3>🔔 Novu Notification Center</h3>
					<p>
						The notification bell icon should appear in the top-right corner.
					</p>
					<p>Make sure Novu Docker services are running on:</p>
					<ul
						style={{ textAlign: "left", maxWidth: "400px", margin: "0 auto" }}
					>
						<li>API: http://localhost:3000</li>
						<li>Dashboard: http://localhost:4000</li>
						<li>WebSocket: http://localhost:3002</li>
					</ul>
				</div>
			</div>
		</>
	);
}

export default App;
