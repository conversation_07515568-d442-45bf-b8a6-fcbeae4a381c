version: '3.8'

services:
  # React Frontend
  react-frontend:
    build: ./react-novu-frontend
    ports:
      - "3001:3001"
    volumes:
      - ./react-novu-frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - novu-api
    networks:
      - novu-network

  # Novu Services (extending the community docker-compose)
  novu-api:
    image: ghcr.io/novuhq/novu/api:latest
    depends_on:
      - mongodb
      - redis
    container_name: novu-api
    environment:
      NODE_ENV: ${NODE_ENV}
      API_ROOT_URL: ${API_ROOT_URL}
      DISABLE_USER_REGISTRATION: ${DISABLE_USER_REGISTRATION}
      PORT: ${API_PORT}
      FRONT_BASE_URL: ${FRONT_BASE_URL}
      MONGO_URL: ${MONGO_URL}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_DB_INDEX: 2
      REDIS_CACHE_SERVICE_HOST: ${REDIS_CACHE_SERVICE_HOST}
      REDIS_CACHE_SERVICE_PORT: ${REDIS_CACHE_SERVICE_PORT}
      JWT_SECRET: ${JWT_SECRET}
      STORE_ENCRYPTION_KEY: ${STORE_ENCRYPTION_KEY}
      NEW_RELIC_APP_NAME: ${NEW_RELIC_APP_NAME}
      NEW_RELIC_LICENSE_KEY: ${NEW_RELIC_LICENSE_KEY}
      API_CONTEXT_PATH: ${API_CONTEXT_PATH}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      S3_LOCAL_STACK: ${S3_LOCAL_STACK}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      S3_REGION: ${S3_REGION}
      SENTRY_DSN: ${SENTRY_DSN}
      IS_DOCKER_HOSTED: true
      BROADCAST_QUEUE_CHUNK_SIZE: ${BROADCAST_QUEUE_CHUNK_SIZE}
      MULTICAST_QUEUE_CHUNK_SIZE: ${MULTICAST_QUEUE_CHUNK_SIZE}
    ports:
      - "3000:3000"
    networks:
      - novu-network

  novu-ws:
    image: ghcr.io/novuhq/novu/ws:latest
    depends_on:
      - mongodb
      - redis
    container_name: novu-ws
    environment:
      PORT: ${WS_PORT}
      NODE_ENV: ${NODE_ENV}
      MONGO_URL: ${MONGO_URL}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_DB_INDEX: 2
      JWT_SECRET: ${JWT_SECRET}
      WS_CONTEXT_PATH: ${WS_CONTEXT_PATH}
    ports:
      - "3002:3002"
    networks:
      - novu-network

  novu-web:
    image: ghcr.io/novuhq/novu/web:latest
    depends_on:
      - novu-api
    container_name: novu-web
    environment:
      REACT_APP_API_URL: ${API_ROOT_URL}
      REACT_APP_ENVIRONMENT: ${NODE_ENV}
      REACT_APP_WIDGET_EMBED_PATH: ${WIDGET_EMBED_PATH}
      REACT_APP_DOCKER_HOSTED_ENV: 'true'
      REACT_APP_WS_URL: ${REACT_APP_WS_URL}
      VITE_LEGACY_DASHBOARD_URL: ${VITE_LEGACY_DASHBOARD_URL}
    ports:
      - "4000:4000"
    networks:
      - novu-network

  novu-worker:
    image: ghcr.io/novuhq/novu/worker:latest
    depends_on:
      - mongodb
      - redis
    container_name: novu-worker
    environment:
      NODE_ENV: ${NODE_ENV}
      MONGO_URL: ${MONGO_URL}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_DB_INDEX: 2
      REDIS_CACHE_SERVICE_HOST: ${REDIS_CACHE_SERVICE_HOST}
      REDIS_CACHE_SERVICE_PORT: ${REDIS_CACHE_SERVICE_PORT}
      JWT_SECRET: ${JWT_SECRET}
      NEW_RELIC_APP_NAME: ${NEW_RELIC_APP_NAME}
      NEW_RELIC_LICENSE_KEY: ${NEW_RELIC_LICENSE_KEY}
      PORT: 3004
      BROADCAST_QUEUE_CHUNK_SIZE: ${BROADCAST_QUEUE_CHUNK_SIZE}
      MULTICAST_QUEUE_CHUNK_SIZE: ${MULTICAST_QUEUE_CHUNK_SIZE}
    networks:
      - novu-network

  mongodb:
    image: mongo:5.0.15
    container_name: mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - novu-network

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - novu-network

volumes:
  mongodb_data:

networks:
  novu-network:
    driver: bridge
